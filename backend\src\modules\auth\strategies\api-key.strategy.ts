import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { PrismaService } from '../../prisma/prisma.service';
import { AuthService } from '../services/auth.service';

@Injectable()
export class ApiKeyStrategy extends PassportStrategy(class {} as any, 'api-key') {
  constructor(
    private readonly prismaService: PrismaService,
    private readonly authService: AuthService,
  ) {
    super();
  }

  async validate(apiKey: string) {
    try {
      // Find the API key in the database
      const apiKeyRecord = await this.prismaService.apiKey.findFirst({
        where: {
          key: apiKey,
          isActive: true,
          OR: [
            { expiresAt: null },
            { expiresAt: { gt: new Date() } }
          ]
        },
        include: {
          organization: true,
          createdBy: {
            include: {
              role: true,
            }
          }
        }
      });

      if (!apiKeyRecord) {
        throw new UnauthorizedException('Invalid or expired API key');
      }

      // Update usage tracking
      await this.prismaService.apiKey.update({
        where: { id: apiKeyRecord.id },
        data: {
          lastUsedAt: new Date(),
          usageCount: { increment: 1 }
        }
      });

      // Check quota limits if set
      if (apiKeyRecord.quotaLimit && apiKeyRecord.usageCount >= apiKeyRecord.quotaLimit) {
        throw new UnauthorizedException('API key quota exceeded');
      }

      return { 
        id: apiKeyRecord.createdBy.id,
        email: apiKeyRecord.createdBy.email,
        organizationId: apiKeyRecord.organizationId,
        role: apiKeyRecord.createdBy.role.name,
        apiKeyId: apiKeyRecord.id,
        permissions: apiKeyRecord.permissions,
        type: 'api-key' 
      };
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('Invalid API key');
    }
  } 
} 