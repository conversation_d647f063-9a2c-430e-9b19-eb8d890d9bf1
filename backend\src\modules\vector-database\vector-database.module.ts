import { Module, DynamicModule, Global } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PineconeProvider } from './providers/pinecone.provider';
import { EmbeddingService } from './services/embedding.service';
import { VectorDatabaseService } from './services/vector-database.service';
import { VectorDatabaseController } from './controllers/vector-database.controller';
import { VectorDatabaseModuleConfig } from './interfaces/vector-database.interface';

/**
 * Vector Database Module Configuration Interface
 */
export interface VectorDatabaseModuleOptions {
  useFactory?: (
    configService: ConfigService,
  ) => Promise<VectorDatabaseModuleConfig> | VectorDatabaseModuleConfig;
  inject?: any[];
  imports?: any[];
}

/**
 * Vector Database Module for SynapseAI Platform
 * 
 * Provides comprehensive vector database functionality with support for:
 * - Multiple providers (Pinecone, Weaviate, etc.)
 * - Embedding generation with multiple AI providers
 * - Document processing and chunking
 * - Semantic search and retrieval
 * - Performance monitoring and metrics
 */
@Global()
@Module({})
export class VectorDatabaseModule {
  /**
   * Create a dynamic module with configuration
   */
  static forRootAsync(options: VectorDatabaseModuleOptions): DynamicModule {
    return {
      module: VectorDatabaseModule,
      imports: [
        ConfigModule,
        ...(options.imports || []),
      ],
      providers: [
        {
          provide: 'VECTOR_DATABASE_CONFIG',
          useFactory: options.useFactory || this.createDefaultConfig,
          inject: options.inject || [ConfigService],
        },
        PineconeProvider,
        EmbeddingService,
        VectorDatabaseService,
        {
          provide: 'VECTOR_DATABASE_PROVIDERS',
          useFactory: (
            pineconeProvider: PineconeProvider,
          ) => {
            return {
              pinecone: pineconeProvider,
              // Add other providers here as they are implemented
              // weaviate: weaviateProvider,
              // qdrant: qdrantProvider,
            };
          },
          inject: [PineconeProvider],
        },
      ],
      controllers: [VectorDatabaseController],
      exports: [
        VectorDatabaseService,
        EmbeddingService,
        PineconeProvider,
        'VECTOR_DATABASE_CONFIG',
        'VECTOR_DATABASE_PROVIDERS',
      ],
    };
  }

  /**
   * Create module with synchronous configuration
   */
  static forRoot(config: VectorDatabaseModuleConfig): DynamicModule {
    return {
      module: VectorDatabaseModule,
      imports: [ConfigModule],
      providers: [
        {
          provide: 'VECTOR_DATABASE_CONFIG',
          useValue: config,
        },
        PineconeProvider,
        EmbeddingService,
        VectorDatabaseService,
        {
          provide: 'VECTOR_DATABASE_PROVIDERS',
          useFactory: (
            pineconeProvider: PineconeProvider,
          ) => {
            return {
              pinecone: pineconeProvider,
            };
          },
          inject: [PineconeProvider],
        },
      ],
      controllers: [VectorDatabaseController],
      exports: [
        VectorDatabaseService,
        EmbeddingService,
        PineconeProvider,
        'VECTOR_DATABASE_CONFIG',
        'VECTOR_DATABASE_PROVIDERS',
      ],
    };
  }

  /**
   * Create default configuration from environment variables
   */
  private static createDefaultConfig(
    configService: ConfigService,
  ): VectorDatabaseModuleConfig {
    return {
      providers: {
        pinecone: {
          provider: 'pinecone',
          apiKey: configService.get<string>('PINECONE_API_KEY') || '',
          environment: configService.get<string>('PINECONE_ENVIRONMENT') || 'us-east1-gcp',
          indexName: configService.get<string>('PINECONE_INDEX_NAME') || 'synapseai-vectors',
          dimension: parseInt(configService.get<string>('PINECONE_DIMENSION') || '1536', 10),
          metric: 'cosine' as const,
          serverless: configService.get<string>('PINECONE_SERVERLESS') === 'true',
          cloudRegion: configService.get<string>('PINECONE_CLOUD_REGION') || 'us-east-1',
          maxRetries: parseInt(configService.get<string>('PINECONE_MAX_RETRIES') || '3', 10),
          timeout: parseInt(configService.get<string>('PINECONE_TIMEOUT') || '30000', 10),
        },
      },
      defaultProvider: 'pinecone',
      embedding: {
        provider: configService.get<string>('EMBEDDING_PROVIDER') as any || 'openai',
        config: {
          model: configService.get<string>('EMBEDDING_MODEL') || 'text-embedding-ada-002',
          apiKey: configService.get<string>('OPENAI_API_KEY'),
          dimension: parseInt(configService.get<string>('EMBEDDING_DIMENSION') || '1536', 10),
          maxTokens: parseInt(configService.get<string>('EMBEDDING_MAX_TOKENS') || '8192', 10),
          batchSize: parseInt(configService.get<string>('EMBEDDING_BATCH_SIZE') || '100', 10),
          rateLimitRpm: parseInt(configService.get<string>('EMBEDDING_RATE_LIMIT_RPM') || '3500', 10),
          retryConfig: {
            maxRetries: parseInt(configService.get<string>('EMBEDDING_MAX_RETRIES') || '3', 10),
            baseDelay: parseInt(configService.get<string>('EMBEDDING_BASE_DELAY') || '1000', 10),
            maxDelay: parseInt(configService.get<string>('EMBEDDING_MAX_DELAY') || '10000', 10),
          },
        },
      },
      processing: {
        chunkSize: parseInt(configService.get<string>('CHUNK_SIZE') || '1000', 10),
        chunkOverlap: parseInt(configService.get<string>('CHUNK_OVERLAP') || '200', 10),
        maxTokens: parseInt(configService.get<string>('MAX_TOKENS_PER_CHUNK') || '8192', 10),
        batchSize: parseInt(configService.get<string>('PROCESSING_BATCH_SIZE') || '10', 10),
      },
      monitoring: {
        enabled: configService.get<string>('VECTOR_DB_MONITORING_ENABLED') === 'true',
        metricsInterval: parseInt(configService.get<string>('METRICS_INTERVAL') || '60000', 10),
        healthCheckInterval: parseInt(configService.get<string>('HEALTH_CHECK_INTERVAL') || '30000', 10),
      },
      cache: {
        enabled: configService.get<string>('VECTOR_DB_CACHE_ENABLED') === 'true',
        ttl: parseInt(configService.get<string>('CACHE_TTL') || '3600', 10),
        maxSize: parseInt(configService.get<string>('CACHE_MAX_SIZE') || '1000', 10),
      },
    };
  }
} 