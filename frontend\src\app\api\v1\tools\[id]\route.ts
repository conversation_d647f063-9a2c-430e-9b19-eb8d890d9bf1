import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth';
import { getBackendUrl } from '@/lib/config';

// Helper to forward requests to backend
async function forwardToBackend(req: NextRequest, id: string, path: string = '') {
  try {
    const session = await getSession();
    
    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const backendUrl = getBackendUrl();
    const url = `${backendUrl}/api/v1/tools/${id}${path}`;
    
    // Parse the request body if it exists
    let body;
    try {
      body = await req.json();
    } catch {
      body = null;
    }
    
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const queryString = searchParams.toString();
    
    // Forward the request to the backend
    const response = await fetch(
      queryString ? `${url}?${queryString}` : url,
      {
        method: req.method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`,
        },
        body: body ? JSON.stringify(body) : undefined,
        cache: 'no-store',
      }
    );
    
    // Get the response data
    const data = await response.json().catch(() => ({}));
    
    // Return the response
    return NextResponse.json(
      data,
      { status: response.status }
    );
  } catch (error) {
    console.error('Error forwarding request to backend:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
}

// GET /api/v1/tools/[id]
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  return forwardToBackend(req, params.id);
}

// PUT /api/v1/tools/[id]
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  return forwardToBackend(req, params.id);
}

// DELETE /api/v1/tools/[id]
export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  return forwardToBackend(req, params.id);
} 