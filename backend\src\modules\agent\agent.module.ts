import { Module } from '@nestjs/common';
import { PrismaModule } from '../prisma/prisma.module';
import { RedisModule } from '../redis/redis.module';
import { SessionModule } from '../session/session.module';
import { VectorDatabaseModule } from '../vector-database/vector-database.module';
import { AgentService } from './services/agent.service';
import { AgentController } from './controllers/agent.controller';
import { AgentExecutionService } from './services/agent-execution.service';
import { AgentTemplateService } from './services/agent-template.service';
import { AgentVersioningService } from './services/agent-versioning.service';
import { AgentAnalyticsService } from './services/agent-analytics.service';
import { AgentMarketplaceService } from './services/agent-marketplace.service';
import { AIProviderService } from './services/ai-provider.service';

/**
 * Agent Module
 * 
 * Core module for AI agent creation and management:
 * - Visual drag-and-drop agent configuration
 * - Agent execution with session memory
 * - Real-time testing with AI providers
 * - Agent versioning and rollback
 * - Integration with billing and analytics
 */
@Module({
  imports: [
    PrismaModule,
    RedisModule,
    SessionModule,
    VectorDatabaseModule,
  ],
  controllers: [AgentController],
  providers: [
    AgentService,
    AgentExecutionService,
    AgentTemplateService,
    AgentVersioningService,
    AgentAnalyticsService,
    AgentMarketplaceService,
    AIProviderService,
  ],
  exports: [
    AgentService,
    AgentExecutionService,
    AgentTemplateService,
  ],
})
export class AgentModule {} 