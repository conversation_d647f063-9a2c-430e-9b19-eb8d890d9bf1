'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Brain, 
  Plus, 
  Search, 
  Filter, 
  Grid3X3,
  List,
  MoreVertical,
  Play,
  Edit,
  Trash2,
  Copy,
  Share2,
  Eye,
  TrendingUp,
  Zap,
  Clock,
  Users,
  Star,
  Download,
  Upload,
  Settings,
  Rocket,
  BarChart3,
  Target,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  AlertCircle,
  CheckCircle2,
  XCircle,
  Loader2,
  RefreshCw,
  SortAsc,
  SortDesc,
  Calendar,
  Tag,
  Globe,
  Lock,
  Archive
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import axios from 'axios';
import { useToast } from '@/lib/usetoast';

interface Agent {
  id: string;
  name: string;
  description: string;
  status: 'ACTIVE' | 'INACTIVE' | 'DRAFT' | 'ARCHIVED' | 'ERROR';
  model: string;
  temperature: number;
  createdAt: string;
  updatedAt: string;
  category: string;
  tags: string[];
  isPublic: boolean;
  analytics: {
    executions: number;
    successRate: number;
    avgResponseTime: number;
    lastExecution: string;
    totalUsers: number;
    rating: number;
  };
  capabilities: string[];
}

interface AgentStats {
  total: number;
  active: number;
  draft: number;
  totalExecutions: number;
  avgSuccessRate: number;
  trends: {
    executions: { value: number; change: number };
    successRate: { value: number; change: number };
    responseTime: { value: number; change: number };
  };
}

type ViewMode = 'grid' | 'list';
type SortField = 'name' | 'createdAt' | 'executions' | 'successRate' | 'lastExecution';
type SortOrder = 'asc' | 'desc';

export default function AgentDashboard() {
  const router = useRouter();
  const { showSuccess, showError } = useToast();

  // Data state
  const [agents, setAgents] = useState<Agent[]>([]);
  const [stats, setStats] = useState<AgentStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // UI state
  const [viewMode, setViewMode] = useState<ViewMode>('grid');
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [categoryFilter, setCategoryFilter] = useState<string>('');
  const [sortField, setSortField] = useState<SortField>('createdAt');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
  const [showFilters, setShowFilters] = useState(false);

  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [agentsPerPage] = useState(12);
  const [totalAgents, setTotalAgents] = useState(0);

  // Action states
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);
  const [bulkActionLoading, setBulkActionLoading] = useState(false);

  useEffect(() => {
    fetchAgents();
    fetchStats();
  }, [currentPage, statusFilter, categoryFilter, sortField, sortOrder]);

  useEffect(() => {
    const delayedSearch = setTimeout(() => {
      if (searchQuery !== undefined) {
        setCurrentPage(1);
        fetchAgents();
      }
    }, 300);

    return () => clearTimeout(delayedSearch);
  }, [searchQuery]);

  const fetchAgents = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      params.append('limit', agentsPerPage.toString());
      params.append('offset', ((currentPage - 1) * agentsPerPage).toString());
      params.append('orderBy', sortField);
      params.append('order', sortOrder);
      
      if (searchQuery) params.append('search', searchQuery);
      if (statusFilter) params.append('status', statusFilter);
      if (categoryFilter) params.append('category', categoryFilter);
      
      const response = await axios.get(`/api/v1/agents?${params.toString()}`);
      setAgents(response.data.agents || []);
      setTotalAgents(response.data.total || 0);
    } catch (err: any) {
      console.error('Error fetching agents:', err);
      setError(err.response?.data?.message || 'Failed to fetch agents');
      showError('Failed to load agents');
    } finally {
      setIsLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await axios.get('/api/v1/agents/analytics/overview');
      setStats(response.data);
    } catch (err: any) {
      console.error('Error fetching stats:', err);
    }
  };

  const handleDeleteAgent = async (agentId: string) => {
    if (!confirm('Are you sure you want to delete this agent?')) {
      return;
    }
    
    try {
      await axios.delete(`/api/v1/agents/${agentId}`);
      showSuccess('Agent deleted successfully');
      fetchAgents();
      fetchStats();
    } catch (err: any) {
      console.error('Error deleting agent:', err);
      showError(err.response?.data?.message || 'Failed to delete agent');
    }
  };

  const handleDuplicateAgent = async (agentId: string) => {
    try {
      const agent = agents.find(a => a.id === agentId);
      if (!agent) return;
      
      const response = await axios.post(`/api/v1/agents/${agentId}/clone`, {
        name: `${agent.name} (Copy)`
      });
      
      showSuccess('Agent duplicated successfully');
      fetchAgents();
    } catch (err: any) {
      console.error('Error duplicating agent:', err);
      showError(err.response?.data?.message || 'Failed to duplicate agent');
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedAgents.length === 0) {
      showError('No agents selected');
      return;
    }

    setBulkActionLoading(true);
    try {
      switch (action) {
        case 'delete':
          if (!confirm(`Are you sure you want to delete ${selectedAgents.length} agents?`)) {
            return;
          }
          await Promise.all(selectedAgents.map(id => axios.delete(`/api/v1/agents/${id}`)));
          showSuccess(`${selectedAgents.length} agents deleted successfully`);
          break;
        case 'activate':
          await Promise.all(selectedAgents.map(id => axios.put(`/api/v1/agents/${id}/activate`)));
          showSuccess(`${selectedAgents.length} agents activated successfully`);
          break;
        case 'deactivate':
          await Promise.all(selectedAgents.map(id => axios.put(`/api/v1/agents/${id}/deactivate`)));
          showSuccess(`${selectedAgents.length} agents deactivated successfully`);
          break;
      }
      setSelectedAgents([]);
      fetchAgents();
      fetchStats();
    } catch (err: any) {
      showError('Bulk action failed');
    } finally {
      setBulkActionLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const config = {
      ACTIVE: { color: 'bg-green-500/20 text-green-400', icon: CheckCircle2 },
      INACTIVE: { color: 'bg-yellow-500/20 text-yellow-400', icon: XCircle },
      DRAFT: { color: 'bg-blue-500/20 text-blue-400', icon: Edit },
      ARCHIVED: { color: 'bg-gray-500/20 text-gray-400', icon: Archive },
      ERROR: { color: 'bg-red-500/20 text-red-400', icon: AlertCircle },
    };

    const { color, icon: Icon } = config[status as keyof typeof config] || config.DRAFT;

    return (
      <span className={`px-2 py-1 ${color} rounded-full text-xs font-medium flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {status.toLowerCase()}
      </span>
    );
  };

  const getPerformanceIndicator = (value: number, threshold: { good: number; ok: number }) => {
    if (value >= threshold.good) return 'text-green-400';
    if (value >= threshold.ok) return 'text-yellow-400';
    return 'text-red-400';
  };

  const totalPages = Math.ceil(totalAgents / agentsPerPage);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 mb-8"
        >
          <div>
            <h1 className="text-4xl font-bold text-white flex items-center gap-3">
              <Brain className="w-10 h-10 text-blue-400" />
              Agent Dashboard
            </h1>
            <p className="text-gray-400 mt-2">
              Manage, monitor, and optimize your AI agents
            </p>
          </div>
          
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => router.push('/dashboard/agents/marketplace')}
              className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-all flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Marketplace
            </button>
            <button
              onClick={() => router.push('/dashboard/agents/templates')}
              className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-all flex items-center gap-2"
            >
              <Upload className="w-4 h-4" />
              Templates
            </button>
            <button
              onClick={() => router.push('/dashboard/agents/new')}
              className="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white hover:opacity-90 transition-all flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              New Agent
            </button>
          </div>
        </motion.div>

        {/* Stats Overview */}
        {stats && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
          >
            <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Total Agents</p>
                  <p className="text-2xl font-bold text-white">{stats.total}</p>
                </div>
                <div className="p-3 bg-blue-500/20 rounded-lg">
                  <Brain className="w-6 h-6 text-blue-400" />
                </div>
              </div>
              <div className="flex items-center gap-1 mt-2">
                <span className="text-green-400 text-sm">{stats.active} active</span>
                <span className="text-gray-400 text-sm">• {stats.draft} draft</span>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Executions</p>
                  <p className="text-2xl font-bold text-white">{stats.totalExecutions.toLocaleString()}</p>
                </div>
                <div className="p-3 bg-emerald-500/20 rounded-lg">
                  <Activity className="w-6 h-6 text-emerald-400" />
                </div>
              </div>
              <div className="flex items-center gap-1 mt-2">
                {stats.trends.executions.change >= 0 ? (
                  <ArrowUpRight className="w-4 h-4 text-green-400" />
                ) : (
                  <ArrowDownRight className="w-4 h-4 text-red-400" />
                )}
                <span className={`text-sm ${stats.trends.executions.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {Math.abs(stats.trends.executions.change)}% vs last week
                </span>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Success Rate</p>
                  <p className="text-2xl font-bold text-white">{stats.avgSuccessRate.toFixed(1)}%</p>
                </div>
                <div className="p-3 bg-green-500/20 rounded-lg">
                  <Target className="w-6 h-6 text-green-400" />
                </div>
              </div>
              <div className="flex items-center gap-1 mt-2">
                {stats.trends.successRate.change >= 0 ? (
                  <ArrowUpRight className="w-4 h-4 text-green-400" />
                ) : (
                  <ArrowDownRight className="w-4 h-4 text-red-400" />
                )}
                <span className={`text-sm ${stats.trends.successRate.change >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {Math.abs(stats.trends.successRate.change)}% vs last week
                </span>
              </div>
            </div>

            <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-gray-400 text-sm">Avg Response</p>
                  <p className="text-2xl font-bold text-white">{stats.trends.responseTime.value}ms</p>
                </div>
                <div className="p-3 bg-yellow-500/20 rounded-lg">
                  <Clock className="w-6 h-6 text-yellow-400" />
                </div>
              </div>
              <div className="flex items-center gap-1 mt-2">
                {stats.trends.responseTime.change <= 0 ? (
                  <ArrowDownRight className="w-4 h-4 text-green-400" />
                ) : (
                  <ArrowUpRight className="w-4 h-4 text-red-400" />
                )}
                <span className={`text-sm ${stats.trends.responseTime.change <= 0 ? 'text-green-400' : 'text-red-400'}`}>
                  {Math.abs(stats.trends.responseTime.change)}% vs last week
                </span>
              </div>
            </div>
          </motion.div>
        )}

        {/* Controls */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6 mb-6"
        >
          {/* Top Controls */}
          <div className="flex flex-col lg:flex-row gap-4 mb-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search agents..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40"
              />
            </div>

            {/* View Mode Toggle */}
            <div className="flex bg-white/10 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'grid' ? 'bg-white/20 text-white' : 'text-gray-400 hover:text-white'
                }`}
              >
                <Grid3X3 className="w-5 h-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded-md transition-colors ${
                  viewMode === 'list' ? 'bg-white/20 text-white' : 'text-gray-400 hover:text-white'
                }`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>

            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`px-4 py-3 rounded-lg transition-colors flex items-center gap-2 ${
                showFilters ? 'bg-blue-500/20 text-blue-400' : 'bg-white/10 text-white hover:bg-white/20'
              }`}
            >
              <Filter className="w-5 h-5" />
              Filters
            </button>

            {/* Refresh */}
            <button
              onClick={() => {
                fetchAgents();
                fetchStats();
              }}
              className="px-4 py-3 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-all flex items-center gap-2"
            >
              <RefreshCw className="w-5 h-5" />
              Refresh
            </button>
          </div>

          {/* Filters */}
          <AnimatePresence>
            {showFilters && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="border-t border-white/10 pt-4"
              >
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
                  >
                    <option value="">All Status</option>
                    <option value="ACTIVE">Active</option>
                    <option value="INACTIVE">Inactive</option>
                    <option value="DRAFT">Draft</option>
                    <option value="ARCHIVED">Archived</option>
                    <option value="ERROR">Error</option>
                  </select>

                  <select
                    value={categoryFilter}
                    onChange={(e) => setCategoryFilter(e.target.value)}
                    className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
                  >
                    <option value="">All Categories</option>
                    <option value="Customer Support">Customer Support</option>
                    <option value="Content Creation">Content Creation</option>
                    <option value="Development">Development</option>
                    <option value="Education">Education</option>
                    <option value="Business Intelligence">Business Intelligence</option>
                  </select>

                  <select
                    value={`${sortField}-${sortOrder}`}
                    onChange={(e) => {
                      const [field, order] = e.target.value.split('-');
                      setSortField(field as SortField);
                      setSortOrder(order as SortOrder);
                    }}
                    className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
                  >
                    <option value="createdAt-desc">Newest First</option>
                    <option value="createdAt-asc">Oldest First</option>
                    <option value="name-asc">Name A-Z</option>
                    <option value="name-desc">Name Z-A</option>
                    <option value="executions-desc">Most Executions</option>
                    <option value="successRate-desc">Highest Success Rate</option>
                  </select>

                  <button
                    onClick={() => {
                      setSearchQuery('');
                      setStatusFilter('');
                      setCategoryFilter('');
                      setSortField('createdAt');
                      setSortOrder('desc');
                    }}
                    className="px-4 py-2 bg-white/5 hover:bg-white/10 rounded-lg text-white transition-colors"
                  >
                    Clear Filters
                  </button>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Bulk Actions */}
          {selectedAgents.length > 0 && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="border-t border-white/10 pt-4 mt-4"
            >
              <div className="flex items-center justify-between">
                <span className="text-white">
                  {selectedAgents.length} agent{selectedAgents.length > 1 ? 's' : ''} selected
                </span>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleBulkAction('activate')}
                    disabled={bulkActionLoading}
                    className="px-3 py-1 bg-green-500/20 text-green-400 rounded-lg hover:bg-green-500/30 transition-colors disabled:opacity-50"
                  >
                    Activate
                  </button>
                  <button
                    onClick={() => handleBulkAction('deactivate')}
                    disabled={bulkActionLoading}
                    className="px-3 py-1 bg-yellow-500/20 text-yellow-400 rounded-lg hover:bg-yellow-500/30 transition-colors disabled:opacity-50"
                  >
                    Deactivate
                  </button>
                  <button
                    onClick={() => handleBulkAction('delete')}
                    disabled={bulkActionLoading}
                    className="px-3 py-1 bg-red-500/20 text-red-400 rounded-lg hover:bg-red-500/30 transition-colors disabled:opacity-50"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </motion.div>

        {/* Error State */}
        {error && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-red-500/20 border border-red-500/40 rounded-lg flex items-center gap-2 text-white"
          >
            <AlertCircle className="w-5 h-5" />
            <span>{error}</span>
          </motion.div>
        )}

        {/* Loading State */}
        {isLoading ? (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="w-10 h-10 text-white animate-spin" />
          </div>
        ) : agents.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-12 text-center"
          >
            <Brain className="w-16 h-16 text-white/40 mx-auto mb-4" />
            <h2 className="text-2xl font-semibold text-white mb-2">No agents found</h2>
            <p className="text-gray-400 mb-6">
              {searchQuery || statusFilter || categoryFilter
                ? "No agents match your search criteria. Try adjusting your filters."
                : "You haven't created any agents yet. Get started by creating your first agent."}
            </p>
            <button
              onClick={() => router.push('/dashboard/agents/new')}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white flex items-center gap-2 mx-auto hover:opacity-90 transition-all"
            >
              <Plus className="w-5 h-5" />
              Create Your First Agent
            </button>
          </motion.div>
        ) : (
          <>
            {/* Agents Grid/List */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className={
                viewMode === 'grid'
                  ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
                  : 'space-y-4'
              }
            >
              {agents.map((agent, index) => (
                <motion.div
                  key={agent.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.05 }}
                  className={
                    viewMode === 'grid'
                      ? 'bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6 hover:bg-white/15 transition-all cursor-pointer group'
                      : 'bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-4 hover:bg-white/15 transition-all'
                  }
                  onClick={() => viewMode === 'grid' && router.push(`/dashboard/agents/${agent.id}`)}
                >
                  {viewMode === 'grid' ? (
                    // Grid View
                    <div>
                      {/* Agent Header */}
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex items-center gap-3">
                          <input
                            type="checkbox"
                            checked={selectedAgents.includes(agent.id)}
                            onChange={(e) => {
                              e.stopPropagation();
                              if (e.target.checked) {
                                setSelectedAgents(prev => [...prev, agent.id]);
                              } else {
                                setSelectedAgents(prev => prev.filter(id => id !== agent.id));
                              }
                            }}
                            className="form-checkbox h-4 w-4 text-blue-500"
                          />
                          <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg">
                            <Brain className="w-5 h-5 text-white" />
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {agent.isPublic ? (
                            <Globe className="w-4 h-4 text-blue-400" />
                          ) : (
                            <Lock className="w-4 h-4 text-gray-400" />
                          )}
                          <div className="relative">
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                setActionMenuOpen(actionMenuOpen === agent.id ? null : agent.id);
                              }}
                              className="p-1 hover:bg-white/10 rounded opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <MoreVertical className="w-4 h-4 text-white" />
                            </button>
                            {actionMenuOpen === agent.id && (
                              <div className="absolute right-0 top-full mt-1 w-48 bg-gray-800 rounded-lg shadow-lg border border-white/20 z-10">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setActionMenuOpen(null);
                                    router.push(`/dashboard/agents/${agent.id}`);
                                  }}
                                  className="w-full text-left px-4 py-2 text-white hover:bg-white/10 flex items-center gap-2"
                                >
                                  <Edit className="w-4 h-4" />
                                  Edit
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setActionMenuOpen(null);
                                    router.push(`/dashboard/agents/${agent.id}/execute`);
                                  }}
                                  className="w-full text-left px-4 py-2 text-white hover:bg-white/10 flex items-center gap-2"
                                >
                                  <Play className="w-4 h-4" />
                                  Execute
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setActionMenuOpen(null);
                                    handleDuplicateAgent(agent.id);
                                  }}
                                  className="w-full text-left px-4 py-2 text-white hover:bg-white/10 flex items-center gap-2"
                                >
                                  <Copy className="w-4 h-4" />
                                  Duplicate
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    setActionMenuOpen(null);
                                    router.push(`/dashboard/agents/${agent.id}/analytics`);
                                  }}
                                  className="w-full text-left px-4 py-2 text-white hover:bg-white/10 flex items-center gap-2"
                                >
                                  <BarChart3 className="w-4 h-4" />
                                  Analytics
                                </button>
                                <div className="border-t border-white/10">
                                  <button
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setActionMenuOpen(null);
                                      handleDeleteAgent(agent.id);
                                    }}
                                    className="w-full text-left px-4 py-2 text-red-400 hover:bg-white/10 flex items-center gap-2"
                                  >
                                    <Trash2 className="w-4 h-4" />
                                    Delete
                                  </button>
                                </div>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Agent Info */}
                      <div className="mb-4">
                        <h3 className="text-white font-semibold text-lg mb-1 truncate">{agent.name}</h3>
                        <p className="text-gray-400 text-sm line-clamp-2 mb-2">
                          {agent.description || 'No description provided'}
                        </p>
                        <div className="flex items-center gap-2 mb-2">
                          {getStatusBadge(agent.status)}
                          {agent.category && (
                            <span className="px-2 py-1 bg-purple-500/20 text-purple-400 rounded-full text-xs">
                              {agent.category}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Tags */}
                      {agent.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-4">
                          {agent.tags.slice(0, 3).map(tag => (
                            <span key={tag} className="px-2 py-1 bg-gray-500/20 text-gray-400 rounded text-xs">
                              {tag}
                            </span>
                          ))}
                          {agent.tags.length > 3 && (
                            <span className="px-2 py-1 bg-gray-500/20 text-gray-400 rounded text-xs">
                              +{agent.tags.length - 3}
                            </span>
                          )}
                        </div>
                      )}

                      {/* Analytics */}
                      <div className="grid grid-cols-2 gap-3 mb-4">
                        <div className="text-center">
                          <p className="text-white font-semibold">{agent.analytics.executions}</p>
                          <p className="text-gray-400 text-xs">Executions</p>
                        </div>
                        <div className="text-center">
                          <p className={`font-semibold ${getPerformanceIndicator(agent.analytics.successRate, { good: 90, ok: 70 })}`}>
                            {agent.analytics.successRate.toFixed(1)}%
                          </p>
                          <p className="text-gray-400 text-xs">Success Rate</p>
                        </div>
                      </div>

                      {/* Footer */}
                      <div className="flex items-center justify-between text-xs text-gray-400">
                        <span>{agent.model}</span>
                        <span>{new Date(agent.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>
                  ) : (
                    // List View
                    <div className="flex items-center gap-4">
                      <input
                        type="checkbox"
                        checked={selectedAgents.includes(agent.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedAgents(prev => [...prev, agent.id]);
                          } else {
                            setSelectedAgents(prev => prev.filter(id => id !== agent.id));
                          }
                        }}
                        className="form-checkbox h-4 w-4 text-blue-500"
                      />
                      
                      <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg">
                        <Brain className="w-5 h-5 text-white" />
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-3 mb-1">
                          <h3 className="text-white font-medium truncate">{agent.name}</h3>
                          {getStatusBadge(agent.status)}
                          {agent.isPublic ? (
                            <Globe className="w-4 h-4 text-blue-400" />
                          ) : (
                            <Lock className="w-4 h-4 text-gray-400" />
                          )}
                        </div>
                        <p className="text-gray-400 text-sm truncate">
                          {agent.description || 'No description'}
                        </p>
                      </div>

                      <div className="flex items-center gap-6 text-sm">
                        <div className="text-center">
                          <p className="text-white font-medium">{agent.analytics.executions}</p>
                          <p className="text-gray-400 text-xs">Executions</p>
                        </div>
                        <div className="text-center">
                          <p className={`font-medium ${getPerformanceIndicator(agent.analytics.successRate, { good: 90, ok: 70 })}`}>
                            {agent.analytics.successRate.toFixed(1)}%
                          </p>
                          <p className="text-gray-400 text-xs">Success Rate</p>
                        </div>
                        <div className="text-center">
                          <p className="text-white font-medium">{agent.analytics.avgResponseTime}ms</p>
                          <p className="text-gray-400 text-xs">Avg Response</p>
                        </div>
                        <div className="text-center">
                          <p className="text-white font-medium">{agent.model}</p>
                          <p className="text-gray-400 text-xs">Model</p>
                        </div>
                      </div>

                      <div className="relative">
                        <button
                          onClick={() => setActionMenuOpen(actionMenuOpen === agent.id ? null : agent.id)}
                          className="p-2 hover:bg-white/10 rounded transition-colors"
                        >
                          <MoreVertical className="w-5 h-5 text-white" />
                        </button>
                        {actionMenuOpen === agent.id && (
                          <div className="absolute right-0 top-full mt-1 w-48 bg-gray-800 rounded-lg shadow-lg border border-white/20 z-10">
                            <button
                              onClick={() => {
                                setActionMenuOpen(null);
                                router.push(`/dashboard/agents/${agent.id}`);
                              }}
                              className="w-full text-left px-4 py-2 text-white hover:bg-white/10 flex items-center gap-2"
                            >
                              <Edit className="w-4 h-4" />
                              Edit
                            </button>
                            <button
                              onClick={() => {
                                setActionMenuOpen(null);
                                router.push(`/dashboard/agents/${agent.id}/execute`);
                              }}
                              className="w-full text-left px-4 py-2 text-white hover:bg-white/10 flex items-center gap-2"
                            >
                              <Play className="w-4 h-4" />
                              Execute
                            </button>
                            <button
                              onClick={() => {
                                setActionMenuOpen(null);
                                handleDuplicateAgent(agent.id);
                              }}
                              className="w-full text-left px-4 py-2 text-white hover:bg-white/10 flex items-center gap-2"
                            >
                              <Copy className="w-4 h-4" />
                              Duplicate
                            </button>
                            <div className="border-t border-white/10">
                              <button
                                onClick={() => {
                                  setActionMenuOpen(null);
                                  handleDeleteAgent(agent.id);
                                }}
                                className="w-full text-left px-4 py-2 text-red-400 hover:bg-white/10 flex items-center gap-2"
                              >
                                <Trash2 className="w-4 h-4" />
                                Delete
                              </button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                </motion.div>
              ))}
            </motion.div>

            {/* Pagination */}
            {totalPages > 1 && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="flex justify-between items-center mt-8"
              >
                <div className="text-gray-400">
                  Showing {((currentPage - 1) * agentsPerPage) + 1} to {Math.min(currentPage * agentsPerPage, totalAgents)} of {totalAgents} agents
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all disabled:opacity-50"
                  >
                    Previous
                  </button>
                  
                  {/* Page Numbers */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNum = Math.max(1, Math.min(totalPages - 4, currentPage - 2)) + i;
                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-4 py-2 border border-white/20 rounded-lg transition-all ${
                          currentPage === pageNum
                            ? 'bg-blue-500 text-white'
                            : 'bg-white/10 text-white hover:bg-white/20'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                  
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all disabled:opacity-50"
                  >
                    Next
                  </button>
                </div>
              </motion.div>
            )}
          </>
        )}
      </div>
    </div>
  );
}