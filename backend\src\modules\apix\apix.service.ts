import { Injectable, Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { RedisService } from '../redis/redis.service';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../prisma/prisma.service';


/**
 * APIX Service
 * 
 * Manages WebSocket connections, subscriptions, and
 * cross-instance communication via Redis pub/sub.
 */
@Injectable()
export class ApixService {
  private readonly logger = new Logger(ApixService.name);
  private readonly connections = new Map<string, { userId: string; organizationId: string; subscriptions: Set<string> }>();
  private server!: Server;

  constructor(
    private readonly redisService: RedisService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly prismaService: PrismaService,
  ) {}

  /**
   * Set up Redis pub/sub for cross-instance communication
   */
  async setupRedisPubSub(server: Server) {
    this.server = server;
    
    // Subscribe to Redis channels for cross-instance events
    const pubSubClient = await this.redisService.getClient();
    
    pubSubClient.on('message', (channel: string, message: string) => {
      try {
        const { event, data, target } = JSON.parse(message);
        
        // Route message based on target
        if (target.type === 'organization') {
          this.server.to(`org:${target.id}`).emit(event, data);
        } else if (target.type === 'user') {
          this.server.to(`user:${target.id}`).emit(event, data);
        } else if (target.type === 'broadcast') {
          this.server.emit(event, data);
        }
      } catch (error) {
        this.logger.error('Failed to process Redis message', error);
      }
    });

    // Subscribe to platform event channels
    await pubSubClient.subscribe('apix:events');
    
    this.logger.log('✅ Redis pub/sub initialized for APIX');
  }

  /**
   * Validate WebSocket connection
   */
  async validateConnection(client: Socket): Promise<any> {
    try {
      // Extract token from handshake
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
      
      if (!token) {
        return null;
      }

      // Verify JWT
      const payload = this.jwtService.verify(token, {
        secret: this.configService.get('JWT_SECRET'),
      });

      // Get user from database
        const user = await this.prismaService.user.findFirst({
        where: { id: payload.sub },
        include: { organization: true },
      });

      if (!user || user.status !== 'ACTIVE') {
        return null;
      }

      return user;
    } catch (error) {
      this.logger.error('Connection validation failed', error);
      return null;
    }
  }

  /**
   * Handle client connection
   */
  async handleConnect(client: Socket, user: any) {
    // Store connection info
    this.connections.set(client.id, {
      userId: user.id,
      organizationId: user.organizationId,
      subscriptions: new Set(),
    });

    // Update user online status
    await this.updateUserStatus(user.id, 'online');
    
    // Emit user online event
    await this.emitEvent('user.online', { userId: user.id }, {
      type: 'organization',
      id: user.organizationId,
    });
  }

  /**
   * Handle client disconnection
   */
  async handleDisconnect(client: Socket) {
    const connection = this.connections.get(client.id);
    
    if (connection) {
      // Check if user has other connections
      const hasOtherConnections = Array.from(this.connections.values())
        .some(conn => conn.userId === connection.userId && client.id !== client.id);
      
      if (!hasOtherConnections) {
        // Update user offline status
        await this.updateUserStatus(connection.userId, 'offline');
        
        // Emit user offline event
        await this.emitEvent('user.offline', { userId: connection.userId }, {
          type: 'organization',
          id: connection.organizationId,
        });
      }
      
      // Remove connection
      this.connections.delete(client.id);
    }
  }

  /**
   * Subscribe to events
   */
  async subscribe(client: Socket, events: string[]): Promise<string[]> {
    const connection = this.connections.get(client.id);
    
    if (!connection) {
      throw new Error('Connection not found');
    }

    // Validate event permissions
    const allowedEvents = await this.validateEventPermissions(connection.userId, events);
    
    // Add subscriptions
    allowedEvents.forEach(event => {
      connection.subscriptions.add(event);
    });

    return Array.from(connection.subscriptions);
  }

  /**
   * Unsubscribe from events
   */
  async unsubscribe(client: Socket, events: string[]): Promise<string[]> {
    const connection = this.connections.get(client.id);
    
    if (!connection) {
      throw new Error('Connection not found');
    }

    // Remove subscriptions
    events.forEach(event => {
      connection.subscriptions.delete(event);
    });

    return Array.from(connection.subscriptions);
  }

  /**
   * Get current subscriptions
   */
  async getSubscriptions(client: Socket): Promise<string[]> {
    const connection = this.connections.get(client.id);
    
    if (!connection) {
      return [];
    }

    return Array.from(connection.subscriptions);
  }

  /**
   * Get platform capabilities
   */
  getCapabilities(): string[] {
    return [
      'agent.*',
      'tool.*',
      'hybrid.*',
      'session.*',
      'hitl.*',
      'knowledge.*',
      'widget.*',
      'analytics.*',
      'billing.*',
      'notification.*',
      'system.*',
    ];
  }

  /**
   * Get server status
   */
  async getStatus() {
    const connections = this.connections.size;
    const uptime = process.uptime();
    const memory = process.memoryUsage();
    
    return {
      status: 'healthy',
      connections,
      uptime,
      memory: {
        used: Math.round(memory.heapUsed / 1024 / 1024),
        total: Math.round(memory.heapTotal / 1024 / 1024),
      },
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Emit event via Redis pub/sub
   */
  async emitEvent(event: string, data: any, target: { type: 'organization' | 'user' | 'broadcast'; id?: string }) {
    const pubClient = await this.redisService.getClient();
    
    await pubClient.publish('apix:events', JSON.stringify({
      event,
      data,
      target,
      timestamp: new Date().toISOString(),
    }));
  }

  /**
   * Update user online status
   */
  private async updateUserStatus(userId: string, status: 'online' | 'offline') {
    try {
      await this.redisService.set(`user:status:${userId}`, status, 3600); // 1 hour TTL
    } catch (error) {
      this.logger.error('Failed to update user status', error);
    }
  }

  /**
   * Validate event permissions
   */
  private async validateEventPermissions(userId: string, events: string[]): Promise<string[]> {
    // TODO: Implement RBAC-based event permission validation
    // For now, allow all requested events
    return events;
  }
} 