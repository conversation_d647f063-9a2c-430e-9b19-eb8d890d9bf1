'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Wrench, 
  Plus, 
  Settings, 
  Play, 
  Save,
  Code,
  Database,
  Server,
  Cloud,
  MessageSquare,
  Search,
  Filter,
  ArrowUpDown,
  Trash2,
  Edit,
  Copy,
  Check,
  X,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { useToast } from '@/lib/usetoast';
import { useRouter } from 'next/navigation';
import axios from 'axios';

interface Tool {
  id: string;
  name: string;
  description: string;
  status: string;
  type: string;
  createdAt: string;
  updatedAt: string;
  executionCount?: number;
  successRate?: number;
  avgResponseTime?: number;
}

export default function ToolDashboard() {
  const router = useRouter();
  const { showSuccess, showError } = useToast();
  
  const [tools, setTools] = useState<Tool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filters and pagination
  const [search, setSearch] = useState('');
  const [status, setStatus] = useState('');
  const [type, setType] = useState('');
  const [sortBy, setSortBy] = useState('updatedAt');
  const [sortOrder, setSortOrder] = useState('desc');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [total, setTotal] = useState(0);
  
  // Action states
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const [activatingId, setActivatingId] = useState<string | null>(null);
  const [deactivatingId, setDeactivatingId] = useState<string | null>(null);
  
  // Load tools on component mount and when filters change
  useEffect(() => {
    fetchTools();
  }, [search, status, type, sortBy, sortOrder, page, limit]);
  
  // Fetch tools from the API
  const fetchTools = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get('/api/v1/tools', {
        params: {
          search: search || undefined,
          status: status || undefined,
          type: type || undefined,
          orderBy: sortBy,
          order: sortOrder,
          offset: (page - 1) * limit,
          limit,
        },
      });
      
      setTools(response.data.tools);
      setTotal(response.data.total);
    } catch (err: any) {
      console.error('Error fetching tools:', err);
      setError(err.response?.data?.message || 'Failed to load tools');
      showError('Failed to load tools');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle tool deletion
  const handleDeleteTool = async (id: string) => {
    try {
      setDeletingId(id);
      
      await axios.delete(`/api/v1/tools/${id}`);
      
      showSuccess('Tool deleted successfully');
      fetchTools();
    } catch (err: any) {
      console.error('Error deleting tool:', err);
      showError(err.response?.data?.message || 'Failed to delete tool');
    } finally {
      setDeletingId(null);
    }
  };
  
  // Handle tool activation
  const handleActivateTool = async (id: string) => {
    try {
      setActivatingId(id);
      
      await axios.put(`/api/v1/tools/${id}/activate`);
      
      showSuccess('Tool activated successfully');
      fetchTools();
    } catch (err: any) {
      console.error('Error activating tool:', err);
      showError(err.response?.data?.message || 'Failed to activate tool');
    } finally {
      setActivatingId(null);
    }
  };
  
  // Handle tool deactivation
  const handleDeactivateTool = async (id: string) => {
    try {
      setDeactivatingId(id);
      
      await axios.put(`/api/v1/tools/${id}/deactivate`);
      
      showSuccess('Tool deactivated successfully');
      fetchTools();
    } catch (err: any) {
      console.error('Error deactivating tool:', err);
      showError(err.response?.data?.message || 'Failed to deactivate tool');
    } finally {
      setDeactivatingId(null);
    }
  };
  
  // Get tool type icon
  const getToolTypeIcon = (type: string) => {
    switch (type) {
      case 'API':
        return <Server className="w-4 h-4" />;
      case 'FUNCTION':
        return <Code className="w-4 h-4" />;
      case 'DATABASE':
        return <Database className="w-4 h-4" />;
      case 'STORAGE':
        return <Cloud className="w-4 h-4" />;
      case 'MESSAGING':
        return <MessageSquare className="w-4 h-4" />;
      default:
        return <Wrench className="w-4 h-4" />;
    }
  };
  
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-500';
      case 'DRAFT':
        return 'bg-yellow-500';
      case 'INACTIVE':
        return 'bg-gray-500';
      case 'DEPRECATED':
        return 'bg-red-500';
      default:
        return 'bg-blue-500';
    }
  };
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-7xl mx-auto"
      >
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-4xl font-bold text-white">
            Tool Manager
          </h1>
          <button 
            onClick={() => router.push('/dashboard/tools/create')}
            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white transition-all hover:opacity-90 flex items-center gap-2"
          >
            <Plus className="w-5 h-5" />
            Create Tool
          </button>
        </div>
        
        {/* Filters */}
        <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-white text-sm font-medium mb-1 block">
                Search
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-2.5 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  placeholder="Search tools..."
                  className="w-full pl-10 pr-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40"
                />
              </div>
            </div>
            
            <div>
              <label className="text-white text-sm font-medium mb-1 block">
                Status
              </label>
              <select
                value={status}
                onChange={(e) => setStatus(e.target.value)}
                className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
              >
                <option value="">All Statuses</option>
                <option value="DRAFT">Draft</option>
                <option value="ACTIVE">Active</option>
                <option value="INACTIVE">Inactive</option>
                <option value="DEPRECATED">Deprecated</option>
              </select>
            </div>
            
            <div>
              <label className="text-white text-sm font-medium mb-1 block">
                Type
              </label>
              <select
                value={type}
                onChange={(e) => setType(e.target.value)}
                className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
              >
                <option value="">All Types</option>
                <option value="API">API</option>
                <option value="FUNCTION">Function</option>
                <option value="DATABASE">Database</option>
                <option value="STORAGE">Storage</option>
                <option value="MESSAGING">Messaging</option>
              </select>
            </div>
            
            <div>
              <label className="text-white text-sm font-medium mb-1 block">
                Sort By
              </label>
              <div className="flex gap-2">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="flex-1 px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
                >
                  <option value="updatedAt">Last Updated</option>
                  <option value="createdAt">Created Date</option>
                  <option value="name">Name</option>
                </select>
                <button
                  onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                  className="px-3 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all"
                >
                  <ArrowUpDown className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {/* Tools List */}
        <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 overflow-hidden">
          {error && (
            <div className="p-4 bg-red-500/20 border border-red-500/40 rounded-lg m-4 flex items-center gap-2 text-white">
              <AlertCircle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          )}
          
          {loading ? (
            <div className="flex items-center justify-center p-12">
              <Loader2 className="w-8 h-8 text-white animate-spin" />
            </div>
          ) : tools.length === 0 ? (
            <div className="p-8 text-center text-gray-400">
              <Wrench className="w-12 h-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-xl font-medium text-white mb-2">No tools found</h3>
              <p>Create your first tool or adjust your filters.</p>
              <button
                onClick={() => router.push('/dashboard/tools/create')}
                className="mt-4 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white transition-all hover:opacity-90 inline-flex items-center gap-2"
              >
                <Plus className="w-4 h-4" />
                Create Tool
              </button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-white/5 border-b border-white/10">
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Name</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Type</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Status</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Last Updated</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Stats</th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-400 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-white/10">
                  {tools.map((tool) => (
                    <tr key={tool.id} className="hover:bg-white/5 transition-colors">
                      <td className="px-6 py-4">
                        <div className="flex items-start">
                          <div className="p-2 bg-white/10 rounded-lg mr-3">
                            <Wrench className="w-5 h-5 text-white" />
                          </div>
                          <div>
                            <div className="text-white font-medium">{tool.name}</div>
                            <div className="text-gray-400 text-sm line-clamp-1">{tool.description || 'No description'}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex items-center gap-1.5">
                          {getToolTypeIcon(tool.type)}
                          <span className="text-white">{tool.type}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(tool.status)} bg-opacity-20 text-white`}>
                          {tool.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-gray-300">
                        {new Date(tool.updatedAt).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4">
                        <div className="flex flex-col">
                          <span className="text-gray-300 text-sm">
                            {tool.executionCount !== undefined ? `${tool.executionCount} executions` : '-'}
                          </span>
                          <span className="text-gray-400 text-xs">
                            {tool.successRate !== undefined ? `${tool.successRate.toFixed(1)}% success rate` : ''}
                          </span>
                        </div>
                      </td>
                      <td className="px-6 py-4 text-right">
                        <div className="flex justify-end gap-2">
                          <button
                            onClick={() => router.push(`/dashboard/tools/${tool.id}`)}
                            className="p-1.5 bg-white/10 rounded-lg text-white hover:bg-white/20 transition-all"
                            title="View details"
                          >
                            <Settings className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => router.push(`/dashboard/tools/${tool.id}/edit`)}
                            className="p-1.5 bg-white/10 rounded-lg text-white hover:bg-white/20 transition-all"
                            title="Edit tool"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => router.push(`/dashboard/tools/${tool.id}/test`)}
                            className="p-1.5 bg-white/10 rounded-lg text-white hover:bg-white/20 transition-all"
                            title="Test tool"
                          >
                            <Play className="w-4 h-4" />
                          </button>
                          {tool.status === 'ACTIVE' ? (
                            <button
                              onClick={() => handleDeactivateTool(tool.id)}
                              disabled={deactivatingId === tool.id}
                              className="p-1.5 bg-white/10 rounded-lg text-white hover:bg-red-500/20 transition-all disabled:opacity-50"
                              title="Deactivate tool"
                            >
                              {deactivatingId === tool.id ? (
                                <Loader2 className="w-4 h-4 animate-spin" />
                              ) : (
                                <X className="w-4 h-4" />
                              )}
                            </button>
                          ) : (
                            <button
                              onClick={() => handleActivateTool(tool.id)}
                              disabled={activatingId === tool.id}
                              className="p-1.5 bg-white/10 rounded-lg text-white hover:bg-green-500/20 transition-all disabled:opacity-50"
                              title="Activate tool"
                            >
                              {activatingId === tool.id ? (
                                <Loader2 className="w-4 h-4 animate-spin" />
                              ) : (
                                <Check className="w-4 h-4" />
                              )}
                            </button>
                          )}
                          <button
                            onClick={() => handleDeleteTool(tool.id)}
                            disabled={deletingId === tool.id}
                            className="p-1.5 bg-white/10 rounded-lg text-white hover:bg-red-500/20 transition-all disabled:opacity-50"
                            title="Delete tool"
                          >
                            {deletingId === tool.id ? (
                              <Loader2 className="w-4 h-4 animate-spin" />
                            ) : (
                              <Trash2 className="w-4 h-4" />
                            )}
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          
          {/* Pagination */}
          {!loading && tools.length > 0 && (
            <div className="px-6 py-4 flex items-center justify-between border-t border-white/10">
              <div className="text-sm text-gray-400">
                Showing {Math.min((page - 1) * limit + 1, total)} to {Math.min(page * limit, total)} of {total} tools
              </div>
              <div className="flex gap-2">
                <button
                  onClick={() => setPage(Math.max(1, page - 1))}
                  disabled={page === 1}
                  className="px-3 py-1 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all disabled:opacity-50 disabled:hover:bg-white/10"
                >
                  Previous
                </button>
                <button
                  onClick={() => setPage(page + 1)}
                  disabled={page * limit >= total}
                  className="px-3 py-1 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all disabled:opacity-50 disabled:hover:bg-white/10"
                >
                  Next
                </button>
              </div>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
} 