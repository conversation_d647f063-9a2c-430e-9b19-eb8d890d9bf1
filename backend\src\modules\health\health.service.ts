import { Injectable, Logger } from '@nestjs/common';
import { HealthIndicatorResult } from '@nestjs/terminus';
import { PrismaService } from '../prisma/prisma.service';
import { RedisService } from '../redis/redis.service';

/**
 * Health Service
 * 
 * Provides custom health checks for the platform
 */
@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Database health check
   */
  async databaseCheck(): Promise<HealthIndicatorResult> {
    const key = 'database';
    
    try {
      const isHealthy = await this.prismaService.healthCheck();
      
      if (isHealthy) {
        return {
          [key]: {
            status: 'up',
            message: 'PostgreSQL is healthy',
          },
        };
      }
      
      return {
        [key]: {
          status: 'down',
          message: 'PostgreSQL is unhealthy',
        },
      };
    } catch (error) {
      this.logger.error('Database health check failed', error);
      
      return {
        [key]: {
          status: 'down',
          message: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Redis health check
   */
  async redisCheck(): Promise<HealthIndicatorResult> {
    const key = 'redis';
    
    try {
      const isHealthy = await this.redisService.healthCheck();
      
      if (isHealthy) {
        return {
          [key]: {
            status: 'up',
            message: 'Redis is healthy',
          },
        };
      }
      
      return {
        [key]: {
          status: 'down',
          message: 'Redis is unhealthy',
        },
      };
    } catch (error) {
      this.logger.error('Redis health check failed', error);
      
      return {
        [key]: {
          status: 'down',
          message: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * Vector database health check
   */
  async vectorDbCheck(): Promise<HealthIndicatorResult> {
    const key = 'vectorDb';
    
    try {
      // TODO: Implement vector DB health check
      return {
        [key]: {
          status: 'up',
          message: 'Vector database is healthy',
        },
      };
    } catch (error) {
      this.logger.error('Vector database health check failed', error);
      
      return {
        [key]: {
          status: 'down',
          message: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }

  /**
   * External services health check
   */
  async externalServicesCheck(): Promise<HealthIndicatorResult> {
    const key = 'externalServices';
    
    try {
      // TODO: Check external services (AI providers, etc.)
      return {
        [key]: {
          status: 'up',
          message: 'External services are healthy',
        },
      };
    } catch (error) {
      this.logger.error('External services health check failed', error);
      
      return {
        [key]: {
          status: 'down',
          message: error instanceof Error ? error.message : 'Unknown error',
        },
      };
    }
  }
} 