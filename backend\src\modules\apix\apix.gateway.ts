import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  OnGatewayInit,
  MessageBody,
  ConnectedSocket,
  WsException,
} from '@nestjs/websockets';
import { Logger, UseGuards, UsePipes, ValidationPipe } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { ApixService } from './apix.service';
import { WsJwtGuard } from '../auth/guards/ws-jwt.guard';
import { ConfigService } from '@nestjs/config';
/**
 * APIX WebSocket Gateway
 * 
 * Central WebSocket gateway handling ALL platform events
 * for real-time communication across modules.
 * 
 * Event Categories:
 * - Agent events: agent.*, agent_execution.*
 * - Tool events: tool.*, tool_execution.*
 * - Hybrid events: hybrid.*, hybrid_execution.*
 * - Session events: session.*
 * - HITL events: hitl.*
 * - Knowledge events: knowledge.*
 * - Widget events: widget.*
 * - Analytics events: analytics.*
 * - Billing events: billing.*
 * - Notification events: notification.*
 * - System events: system.*
 */
@WebSocketGateway({
  namespace: '/apix',
  cors: {
    origin: (origin, callback) => {
      // Allow configured origins or development
      const allowedOrigins = process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'];
      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
  },
})
@UsePipes(new ValidationPipe({ transform: true }))
export class ApixGateway implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server!: Server;

  private readonly logger = new Logger(ApixGateway.name);

  constructor(
    private readonly apixService: ApixService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Gateway initialization
   */
  async afterInit(server: Server) {
    this.logger.log('✅ APIX WebSocket Gateway initialized');
    
    // Set up Redis pub/sub for cross-instance communication
    await this.apixService.setupRedisPubSub(server);
  }

  /**
   * Handle client connection
   */
  @UseGuards(WsJwtGuard)
  async handleConnection(client: Socket) {
    try {
      const user = await this.apixService.validateConnection(client);
      
      if (!user) {
        client.disconnect();
        return;
      }

      // Store client info
      await this.apixService.handleConnect(client, user);

      // Join organization room
      if (user.organizationId) {
        await client.join(`org:${user.organizationId}`);
      }

      // Join user-specific room
      await client.join(`user:${user.id}`);

      // Send connection success
      client.emit('connected', {
        userId: user.id,
        organizationId: user.organizationId,
        serverTime: new Date().toISOString(),
        capabilities: this.apixService.getCapabilities(),
      });

      this.logger.log(`Client connected: ${client.id} (User: ${user.email})`);
    } catch (error) {
      this.logger.error('Connection failed', error);
      client.disconnect();
    }
  }

  /**
   * Handle client disconnection
   */
  async handleDisconnect(client: Socket) {
    await this.apixService.handleDisconnect(client);
    this.logger.log(`Client disconnected: ${client.id}`);
  }

  /**
   * Subscribe to events
   */
  @SubscribeMessage('subscribe')
  @UseGuards(WsJwtGuard)
  async handleSubscribe(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { events: string[] },
  ) {
    try {
      const subscriptions = await this.apixService.subscribe(client, data.events);
      
      return {
        event: 'subscribed',
        data: { subscriptions },
      };
    } catch (error: any) {
      throw new WsException(error instanceof Error ? error.message : 'Unknown error');
    }
  }

  /**
   * Unsubscribe from events
   */
  @SubscribeMessage('unsubscribe')
  @UseGuards(WsJwtGuard)
  async handleUnsubscribe(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { events: string[] },
  ) {
    try {
      const subscriptions = await this.apixService.unsubscribe(client, data.events);
      
      return {
        event: 'unsubscribed',
        data: { subscriptions },
      };
    } catch (error: any) {
      throw new WsException(error instanceof Error ? error.message : 'Unknown error' );
    }
  }

  /**
   * Get current subscriptions
   */
  @SubscribeMessage('subscriptions')
  @UseGuards(WsJwtGuard)
  async handleGetSubscriptions(@ConnectedSocket() client: Socket) {
    const subscriptions = await this.apixService.getSubscriptions(client);
    
    return {
      event: 'subscriptions',
      data: { subscriptions },
    };
  }

  /**
   * Heartbeat/ping
   */
  @SubscribeMessage('ping')
  async handlePing(@ConnectedSocket() client: Socket) {
    return {
      event: 'pong',
      data: {
        timestamp: new Date().toISOString(),
        clientId: client.id,
      },
    };
  }

  /**
   * Get server status
   */
  @SubscribeMessage('status')
  async handleStatus() {
    const status = await this.apixService.getStatus();
    
    return {
      event: 'status',
      data: status,
    };
  }

  /**
   * Emit event to organization
   */
  async emitToOrganization(organizationId: string, event: string, data: any) {
    this.server.to(`org:${organizationId}`).emit(event, data);
  }

  /**
   * Emit event to user
   */
  async emitToUser(userId: string, event: string, data: any) {
    this.server.to(`user:${userId}`).emit(event, data);
  }

  /**
   * Emit event to specific clients
   */
  async emitToClients(clientIds: string[], event: string, data: any) {
    clientIds.forEach(clientId => {
      this.server.to(clientId).emit(event, data);
    });
  }

  /**
   * Broadcast event to all connected clients
   */
  async broadcast(event: string, data: any) {
    this.server.emit(event, data);
  }
} 