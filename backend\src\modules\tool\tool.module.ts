import { Modu<PERSON> } from '@nestjs/common';
import { ToolController } from './controllers/tool.controller';
import { ToolService } from './services/tool.service';
import { ToolExecutionService } from './services/tool-execution.service';
import { ToolAnalyticsService } from './services/tool-analytics.service';
import { ToolMarketplaceService } from './services/tool-marketplace.service';
import { ToolValidationService } from './services/tool-validation.service';
import { PrismaModule } from '../prisma/prisma.module';
import { RedisModule } from '../redis/redis.module';
import { SessionModule } from '../session/session.module';

@Module({
  imports: [PrismaModule, RedisModule, SessionModule],
  controllers: [ToolController],
  providers: [
    ToolService,
    ToolExecutionService,
    ToolAnalyticsService,
    ToolMarketplaceService,
    ToolValidationService,
  ],
  exports: [
    ToolService,
    ToolExecutionService,
    ToolAnalyticsService,
    ToolMarketplaceService,
    ToolValidationService,
  ],
})
export class ToolModule {}