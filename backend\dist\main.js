/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/app.module.ts":
/*!***************************!*\
  !*** ./src/app.module.ts ***!
  \***************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AppModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const throttler_1 = __webpack_require__(/*! @nestjs/throttler */ "@nestjs/throttler");
const schedule_1 = __webpack_require__(/*! @nestjs/schedule */ "@nestjs/schedule");
const prisma_module_1 = __webpack_require__(/*! ./modules/prisma/prisma.module */ "./src/modules/prisma/prisma.module.ts");
const redis_module_1 = __webpack_require__(/*! ./modules/redis/redis.module */ "./src/modules/redis/redis.module.ts");
const health_module_1 = __webpack_require__(/*! ./modules/health/health.module */ "./src/modules/health/health.module.ts");
const auth_module_1 = __webpack_require__(/*! ./modules/auth/auth.module */ "./src/modules/auth/auth.module.ts");
const session_module_1 = __webpack_require__(/*! ./modules/session/session.module */ "./src/modules/session/session.module.ts");
const vector_database_module_1 = __webpack_require__(/*! ./modules/vector-database/vector-database.module */ "./src/modules/vector-database/vector-database.module.ts");
const apix_module_1 = __webpack_require__(/*! ./modules/apix/apix.module */ "./src/modules/apix/apix.module.ts");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: ['.env.local', '.env'],
            }),
            throttler_1.ThrottlerModule.forRoot([{
                    ttl: 60000,
                    limit: 100,
                }]),
            schedule_1.ScheduleModule.forRoot(),
            prisma_module_1.PrismaModule,
            redis_module_1.RedisModule.forRootAsync({
                imports: [config_1.ConfigModule],
                useFactory: (configService) => ({
                    host: configService.get('REDIS_HOST', 'localhost'),
                    port: configService.get('REDIS_PORT', 6379),
                    password: configService.get('REDIS_PASSWORD'),
                    db: configService.get('REDIS_DB', 0),
                    maxRetriesPerRequest: 3,
                }),
                inject: [config_1.ConfigService],
            }),
            health_module_1.HealthModule,
            auth_module_1.AuthModule,
            session_module_1.SessionModule,
            vector_database_module_1.VectorDatabaseModule,
            apix_module_1.ApixModule,
        ],
        controllers: [],
        providers: [],
    })
], AppModule);


/***/ }),

/***/ "./src/main.ts":
/*!*********************!*\
  !*** ./src/main.ts ***!
  \*********************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
const core_1 = __webpack_require__(/*! @nestjs/core */ "@nestjs/core");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const swagger_1 = __webpack_require__(/*! @nestjs/swagger */ "@nestjs/swagger");
const platform_ws_1 = __webpack_require__(/*! @nestjs/platform-ws */ "@nestjs/platform-ws");
const helmet_1 = __importDefault(__webpack_require__(/*! helmet */ "helmet"));
const compression_1 = __importDefault(__webpack_require__(/*! compression */ "compression"));
const express_rate_limit_1 = __importDefault(__webpack_require__(/*! express-rate-limit */ "express-rate-limit"));
const app_module_1 = __webpack_require__(/*! ./app.module */ "./src/app.module.ts");
async function bootstrap() {
    const logger = new common_1.Logger('Bootstrap');
    try {
        const app = await core_1.NestFactory.create(app_module_1.AppModule, {
            logger: ['error', 'warn', 'log', 'debug', 'verbose'],
        });
        const configService = app.get(config_1.ConfigService);
        const port = configService.get('PORT', 3001);
        const nodeEnv = configService.get('NODE_ENV', 'development');
        const corsOrigins = configService.get('CORS_ORIGINS', 'http://localhost:3000').split(',');
        app.use((0, helmet_1.default)({
            crossOriginEmbedderPolicy: false,
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    imgSrc: ["'self'", "data:", "https:"],
                    connectSrc: ["'self'", "wss:", "ws:"],
                },
            },
        }));
        app.use((0, compression_1.default)());
        app.use((0, express_rate_limit_1.default)({
            windowMs: 15 * 60 * 1000,
            max: 1000,
            message: 'Too many requests from this IP, please try again later.',
            standardHeaders: true,
            legacyHeaders: false,
        }));
        app.enableCors({
            origin: nodeEnv === 'production' ? corsOrigins : true,
            credentials: true,
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Organization-ID'],
        });
        app.setGlobalPrefix('api/v1');
        app.useGlobalPipes(new common_1.ValidationPipe({
            transform: true,
            whitelist: true,
            forbidNonWhitelisted: true,
            disableErrorMessages: nodeEnv === 'production',
            validationError: {
                target: false,
                value: false,
            },
        }));
        app.useWebSocketAdapter(new platform_ws_1.WsAdapter(app));
        if (nodeEnv !== 'production') {
            const config = new swagger_1.DocumentBuilder()
                .setTitle('SynapseAI Platform API')
                .setDescription('Production-ready SaaS platform for AI agents, tools, and hybrid workflows')
                .setVersion('1.0.0')
                .addBearerAuth()
                .addTag('Authentication', 'User authentication and authorization')
                .addTag('Vector Database', 'Vector database operations for RAG and knowledge management')
                .addTag('Agents', 'AI agent management and execution')
                .addTag('Tools', 'Tool creation and execution')
                .addTag('Workflows', 'Hybrid workflow management')
                .addTag('Analytics', 'Usage analytics and reporting')
                .addTag('Admin', 'Administrative operations')
                .build();
            const document = swagger_1.SwaggerModule.createDocument(app, config);
            swagger_1.SwaggerModule.setup('api/docs', app, document, {
                swaggerOptions: {
                    persistAuthorization: true,
                },
            });
            logger.log(`Swagger documentation available at http://localhost:${port}/api/docs`);
        }
        const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2'];
        signals.forEach((signal) => {
            process.on(signal, async () => {
                logger.log(`Received ${signal}, shutting down gracefully...`);
                await app.close();
                process.exit(0);
            });
        });
        await app.listen(port, '0.0.0.0');
        logger.log(`🚀 SynapseAI Platform API started successfully`);
        logger.log(`🌍 Environment: ${nodeEnv}`);
        logger.log(`🔗 Server running on: http://localhost:${port}/api/v1`);
        logger.log(`📚 Health check: http://localhost:${port}/api/v1/health`);
        if (nodeEnv !== 'production') {
            logger.log(`📖 API Documentation: http://localhost:${port}/api/docs`);
        }
    }
    catch (error) {
        logger.error('Failed to start application', error);
        process.exit(1);
    }
}
bootstrap();


/***/ }),

/***/ "./src/modules/apix/apix.gateway.ts":
/*!******************************************!*\
  !*** ./src/modules/apix/apix.gateway.ts ***!
  \******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var ApixGateway_1;
var _a, _b, _c, _d, _e, _f, _g, _h;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ApixGateway = void 0;
const websockets_1 = __webpack_require__(/*! @nestjs/websockets */ "@nestjs/websockets");
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const socket_io_1 = __webpack_require__(/*! socket.io */ "socket.io");
const apix_service_1 = __webpack_require__(/*! ./apix.service */ "./src/modules/apix/apix.service.ts");
const ws_jwt_guard_1 = __webpack_require__(/*! ../auth/guards/ws-jwt.guard */ "./src/modules/auth/guards/ws-jwt.guard.ts");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
let ApixGateway = ApixGateway_1 = class ApixGateway {
    apixService;
    configService;
    server;
    logger = new common_1.Logger(ApixGateway_1.name);
    constructor(apixService, configService) {
        this.apixService = apixService;
        this.configService = configService;
    }
    async afterInit(server) {
        this.logger.log('✅ APIX WebSocket Gateway initialized');
        await this.apixService.setupRedisPubSub(server);
    }
    async handleConnection(client) {
        try {
            const user = await this.apixService.validateConnection(client);
            if (!user) {
                client.disconnect();
                return;
            }
            await this.apixService.handleConnect(client, user);
            if (user.organizationId) {
                await client.join(`org:${user.organizationId}`);
            }
            await client.join(`user:${user.id}`);
            client.emit('connected', {
                userId: user.id,
                organizationId: user.organizationId,
                serverTime: new Date().toISOString(),
                capabilities: this.apixService.getCapabilities(),
            });
            this.logger.log(`Client connected: ${client.id} (User: ${user.email})`);
        }
        catch (error) {
            this.logger.error('Connection failed', error);
            client.disconnect();
        }
    }
    async handleDisconnect(client) {
        await this.apixService.handleDisconnect(client);
        this.logger.log(`Client disconnected: ${client.id}`);
    }
    async handleSubscribe(client, data) {
        try {
            const subscriptions = await this.apixService.subscribe(client, data.events);
            return {
                event: 'subscribed',
                data: { subscriptions },
            };
        }
        catch (error) {
            throw new websockets_1.WsException(error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async handleUnsubscribe(client, data) {
        try {
            const subscriptions = await this.apixService.unsubscribe(client, data.events);
            return {
                event: 'unsubscribed',
                data: { subscriptions },
            };
        }
        catch (error) {
            throw new websockets_1.WsException(error instanceof Error ? error.message : 'Unknown error');
        }
    }
    async handleGetSubscriptions(client) {
        const subscriptions = await this.apixService.getSubscriptions(client);
        return {
            event: 'subscriptions',
            data: { subscriptions },
        };
    }
    async handlePing(client) {
        return {
            event: 'pong',
            data: {
                timestamp: new Date().toISOString(),
                clientId: client.id,
            },
        };
    }
    async handleStatus() {
        const status = await this.apixService.getStatus();
        return {
            event: 'status',
            data: status,
        };
    }
    async emitToOrganization(organizationId, event, data) {
        this.server.to(`org:${organizationId}`).emit(event, data);
    }
    async emitToUser(userId, event, data) {
        this.server.to(`user:${userId}`).emit(event, data);
    }
    async emitToClients(clientIds, event, data) {
        clientIds.forEach(clientId => {
            this.server.to(clientId).emit(event, data);
        });
    }
    async broadcast(event, data) {
        this.server.emit(event, data);
    }
};
exports.ApixGateway = ApixGateway;
__decorate([
    (0, websockets_1.WebSocketServer)(),
    __metadata("design:type", typeof (_c = typeof socket_io_1.Server !== "undefined" && socket_io_1.Server) === "function" ? _c : Object)
], ApixGateway.prototype, "server", void 0);
__decorate([
    (0, common_1.UseGuards)(ws_jwt_guard_1.WsJwtGuard),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_d = typeof socket_io_1.Socket !== "undefined" && socket_io_1.Socket) === "function" ? _d : Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleConnection", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('subscribe'),
    (0, common_1.UseGuards)(ws_jwt_guard_1.WsJwtGuard),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_e = typeof socket_io_1.Socket !== "undefined" && socket_io_1.Socket) === "function" ? _e : Object, Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleSubscribe", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('unsubscribe'),
    (0, common_1.UseGuards)(ws_jwt_guard_1.WsJwtGuard),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __param(1, (0, websockets_1.MessageBody)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_f = typeof socket_io_1.Socket !== "undefined" && socket_io_1.Socket) === "function" ? _f : Object, Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleUnsubscribe", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('subscriptions'),
    (0, common_1.UseGuards)(ws_jwt_guard_1.WsJwtGuard),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_g = typeof socket_io_1.Socket !== "undefined" && socket_io_1.Socket) === "function" ? _g : Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleGetSubscriptions", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('ping'),
    __param(0, (0, websockets_1.ConnectedSocket)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_h = typeof socket_io_1.Socket !== "undefined" && socket_io_1.Socket) === "function" ? _h : Object]),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handlePing", null);
__decorate([
    (0, websockets_1.SubscribeMessage)('status'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ApixGateway.prototype, "handleStatus", null);
exports.ApixGateway = ApixGateway = ApixGateway_1 = __decorate([
    (0, websockets_1.WebSocketGateway)({
        namespace: '/apix',
        cors: {
            origin: (origin, callback) => {
                const allowedOrigins = process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'];
                if (!origin || allowedOrigins.includes(origin)) {
                    callback(null, true);
                }
                else {
                    callback(new Error('Not allowed by CORS'));
                }
            },
            credentials: true,
        },
    }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __metadata("design:paramtypes", [typeof (_a = typeof apix_service_1.ApixService !== "undefined" && apix_service_1.ApixService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object])
], ApixGateway);


/***/ }),

/***/ "./src/modules/apix/apix.module.ts":
/*!*****************************************!*\
  !*** ./src/modules/apix/apix.module.ts ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ApixModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const jwt_1 = __webpack_require__(/*! @nestjs/jwt */ "@nestjs/jwt");
const redis_module_1 = __webpack_require__(/*! ../redis/redis.module */ "./src/modules/redis/redis.module.ts");
const apix_gateway_1 = __webpack_require__(/*! ./apix.gateway */ "./src/modules/apix/apix.gateway.ts");
const apix_service_1 = __webpack_require__(/*! ./apix.service */ "./src/modules/apix/apix.service.ts");
const auth_module_1 = __webpack_require__(/*! ../auth/auth.module */ "./src/modules/auth/auth.module.ts");
const prisma_module_1 = __webpack_require__(/*! ../prisma/prisma.module */ "./src/modules/prisma/prisma.module.ts");
let ApixModule = class ApixModule {
};
exports.ApixModule = ApixModule;
exports.ApixModule = ApixModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule,
            redis_module_1.RedisModule,
            auth_module_1.AuthModule,
            prisma_module_1.PrismaModule,
            jwt_1.JwtModule.register({
                secret: process.env.JWT_SECRET || 'default-secret',
                signOptions: { expiresIn: '7d' },
            }),
        ],
        providers: [
            apix_gateway_1.ApixGateway,
            apix_service_1.ApixService,
        ],
        exports: [
            apix_service_1.ApixService,
        ],
    })
], ApixModule);


/***/ }),

/***/ "./src/modules/apix/apix.service.ts":
/*!******************************************!*\
  !*** ./src/modules/apix/apix.service.ts ***!
  \******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ApixService_1;
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ApixService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const redis_service_1 = __webpack_require__(/*! ../redis/redis.service */ "./src/modules/redis/redis.service.ts");
const jwt_1 = __webpack_require__(/*! @nestjs/jwt */ "@nestjs/jwt");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const prisma_service_1 = __webpack_require__(/*! ../prisma/prisma.service */ "./src/modules/prisma/prisma.service.ts");
let ApixService = ApixService_1 = class ApixService {
    redisService;
    jwtService;
    configService;
    prismaService;
    logger = new common_1.Logger(ApixService_1.name);
    connections = new Map();
    server;
    constructor(redisService, jwtService, configService, prismaService) {
        this.redisService = redisService;
        this.jwtService = jwtService;
        this.configService = configService;
        this.prismaService = prismaService;
    }
    async setupRedisPubSub(server) {
        this.server = server;
        const pubSubClient = await this.redisService.getClient();
        pubSubClient.on('message', (channel, message) => {
            try {
                const { event, data, target } = JSON.parse(message);
                if (target.type === 'organization') {
                    this.server.to(`org:${target.id}`).emit(event, data);
                }
                else if (target.type === 'user') {
                    this.server.to(`user:${target.id}`).emit(event, data);
                }
                else if (target.type === 'broadcast') {
                    this.server.emit(event, data);
                }
            }
            catch (error) {
                this.logger.error('Failed to process Redis message', error);
            }
        });
        await pubSubClient.subscribe('apix:events');
        this.logger.log('✅ Redis pub/sub initialized for APIX');
    }
    async validateConnection(client) {
        try {
            const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
            if (!token) {
                return null;
            }
            const payload = this.jwtService.verify(token, {
                secret: this.configService.get('JWT_SECRET'),
            });
            const user = await this.prismaService.user.findFirst({
                where: { id: payload.sub },
                include: { organization: true },
            });
            if (!user || user.status !== 'ACTIVE') {
                return null;
            }
            return user;
        }
        catch (error) {
            this.logger.error('Connection validation failed', error);
            return null;
        }
    }
    async handleConnect(client, user) {
        this.connections.set(client.id, {
            userId: user.id,
            organizationId: user.organizationId,
            subscriptions: new Set(),
        });
        await this.updateUserStatus(user.id, 'online');
        await this.emitEvent('user.online', { userId: user.id }, {
            type: 'organization',
            id: user.organizationId,
        });
    }
    async handleDisconnect(client) {
        const connection = this.connections.get(client.id);
        if (connection) {
            const hasOtherConnections = Array.from(this.connections.values())
                .some(conn => conn.userId === connection.userId && client.id !== client.id);
            if (!hasOtherConnections) {
                await this.updateUserStatus(connection.userId, 'offline');
                await this.emitEvent('user.offline', { userId: connection.userId }, {
                    type: 'organization',
                    id: connection.organizationId,
                });
            }
            this.connections.delete(client.id);
        }
    }
    async subscribe(client, events) {
        const connection = this.connections.get(client.id);
        if (!connection) {
            throw new Error('Connection not found');
        }
        const allowedEvents = await this.validateEventPermissions(connection.userId, events);
        allowedEvents.forEach(event => {
            connection.subscriptions.add(event);
        });
        return Array.from(connection.subscriptions);
    }
    async unsubscribe(client, events) {
        const connection = this.connections.get(client.id);
        if (!connection) {
            throw new Error('Connection not found');
        }
        events.forEach(event => {
            connection.subscriptions.delete(event);
        });
        return Array.from(connection.subscriptions);
    }
    async getSubscriptions(client) {
        const connection = this.connections.get(client.id);
        if (!connection) {
            return [];
        }
        return Array.from(connection.subscriptions);
    }
    getCapabilities() {
        return [
            'agent.*',
            'tool.*',
            'hybrid.*',
            'session.*',
            'hitl.*',
            'knowledge.*',
            'widget.*',
            'analytics.*',
            'billing.*',
            'notification.*',
            'system.*',
        ];
    }
    async getStatus() {
        const connections = this.connections.size;
        const uptime = process.uptime();
        const memory = process.memoryUsage();
        return {
            status: 'healthy',
            connections,
            uptime,
            memory: {
                used: Math.round(memory.heapUsed / 1024 / 1024),
                total: Math.round(memory.heapTotal / 1024 / 1024),
            },
            timestamp: new Date().toISOString(),
        };
    }
    async emitEvent(event, data, target) {
        const pubClient = await this.redisService.getClient();
        await pubClient.publish('apix:events', JSON.stringify({
            event,
            data,
            target,
            timestamp: new Date().toISOString(),
        }));
    }
    async updateUserStatus(userId, status) {
        try {
            await this.redisService.set(`user:status:${userId}`, status, 3600);
        }
        catch (error) {
            this.logger.error('Failed to update user status', error);
        }
    }
    async validateEventPermissions(userId, events) {
        return events;
    }
};
exports.ApixService = ApixService;
exports.ApixService = ApixService = ApixService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof redis_service_1.RedisService !== "undefined" && redis_service_1.RedisService) === "function" ? _a : Object, typeof (_b = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _b : Object, typeof (_c = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _c : Object, typeof (_d = typeof prisma_service_1.PrismaService !== "undefined" && prisma_service_1.PrismaService) === "function" ? _d : Object])
], ApixService);


/***/ }),

/***/ "./src/modules/auth/auth.module.ts":
/*!*****************************************!*\
  !*** ./src/modules/auth/auth.module.ts ***!
  \*****************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const jwt_1 = __webpack_require__(/*! @nestjs/jwt */ "@nestjs/jwt");
const passport_1 = __webpack_require__(/*! @nestjs/passport */ "@nestjs/passport");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const prisma_module_1 = __webpack_require__(/*! ../prisma/prisma.module */ "./src/modules/prisma/prisma.module.ts");
const redis_module_1 = __webpack_require__(/*! ../redis/redis.module */ "./src/modules/redis/redis.module.ts");
const auth_service_1 = __webpack_require__(/*! ./services/auth.service */ "./src/modules/auth/services/auth.service.ts");
const auth_controller_1 = __webpack_require__(/*! ./controllers/auth.controller */ "./src/modules/auth/controllers/auth.controller.ts");
const jwt_strategy_1 = __webpack_require__(/*! ./strategies/jwt.strategy */ "./src/modules/auth/strategies/jwt.strategy.ts");
const jwt_refresh_strategy_1 = __webpack_require__(/*! ./strategies/jwt-refresh.strategy */ "./src/modules/auth/strategies/jwt-refresh.strategy.ts");
const local_strategy_1 = __webpack_require__(/*! ./strategies/local.strategy */ "./src/modules/auth/strategies/local.strategy.ts");
const api_key_strategy_1 = __webpack_require__(/*! ./strategies/api-key.strategy */ "./src/modules/auth/strategies/api-key.strategy.ts");
const casl_ability_factory_1 = __webpack_require__(/*! ./casl/casl-ability.factory */ "./src/modules/auth/casl/casl-ability.factory.ts");
const auth_guard_1 = __webpack_require__(/*! ./guards/auth.guard */ "./src/modules/auth/guards/auth.guard.ts");
const roles_guard_1 = __webpack_require__(/*! ./guards/roles.guard */ "./src/modules/auth/guards/roles.guard.ts");
const organization_guard_1 = __webpack_require__(/*! ./guards/organization.guard */ "./src/modules/auth/guards/organization.guard.ts");
let AuthModule = class AuthModule {
};
exports.AuthModule = AuthModule;
exports.AuthModule = AuthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            passport_1.PassportModule.register({ defaultStrategy: 'jwt' }),
            jwt_1.JwtModule.registerAsync({
                imports: [config_1.ConfigModule],
                useFactory: async (configService) => ({
                    secret: configService.get('JWT_SECRET'),
                    signOptions: {
                        expiresIn: configService.get('JWT_ACCESS_EXPIRATION', '15m'),
                    },
                }),
                inject: [config_1.ConfigService],
            }),
            prisma_module_1.PrismaModule,
            redis_module_1.RedisModule,
        ],
        controllers: [auth_controller_1.AuthController],
        providers: [
            auth_service_1.AuthService,
            jwt_strategy_1.JwtStrategy,
            jwt_refresh_strategy_1.JwtRefreshStrategy,
            local_strategy_1.LocalStrategy,
            api_key_strategy_1.ApiKeyStrategy,
            casl_ability_factory_1.CaslAbilityFactory,
            auth_guard_1.AuthGuard,
            roles_guard_1.RolesGuard,
            organization_guard_1.OrganizationGuard,
        ],
        exports: [auth_service_1.AuthService, casl_ability_factory_1.CaslAbilityFactory, auth_guard_1.AuthGuard, roles_guard_1.RolesGuard, organization_guard_1.OrganizationGuard],
    })
], AuthModule);


/***/ }),

/***/ "./src/modules/auth/casl/casl-ability.factory.ts":
/*!*******************************************************!*\
  !*** ./src/modules/auth/casl/casl-ability.factory.ts ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.CaslAbilityFactory = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const ability_1 = __webpack_require__(/*! @casl/ability */ "@casl/ability");
const prisma_service_1 = __webpack_require__(/*! ../../prisma/prisma.service */ "./src/modules/prisma/prisma.service.ts");
class Agent {
}
class Tool {
}
class Hybrid {
}
class Organization {
}
class User {
}
let CaslAbilityFactory = class CaslAbilityFactory {
    prismaService;
    constructor(prismaService) {
        this.prismaService = prismaService;
    }
    async createForUser(userId) {
        const user = await this.prismaService.user.findUnique({
            where: { id: userId },
            include: {
                role: true,
                organization: true,
            },
        });
        if (!user) {
            throw new common_1.UnauthorizedException('User not found');
        }
        const { can, cannot, build } = new ability_1.AbilityBuilder(ability_1.PureAbility);
        switch (user.role.name) {
            case 'SUPER_ADMIN':
                can('manage', 'all');
                break;
            case 'ORG_ADMIN':
                can('manage', Agent, { organizationId: user.organizationId });
                can('manage', Tool, { organizationId: user.organizationId });
                can('manage', Hybrid, { organizationId: user.organizationId });
                can('manage', User, { organizationId: user.organizationId });
                can('read', Organization, { id: user.organizationId });
                can('update', Organization, { id: user.organizationId });
                break;
            case 'DEVELOPER':
                can('create', Agent, { organizationId: user.organizationId });
                can('read', Agent, { organizationId: user.organizationId });
                can('update', Agent, { createdById: user.id });
                can('delete', Agent, { createdById: user.id });
                can('execute', Agent, { organizationId: user.organizationId });
                can('create', Tool, { organizationId: user.organizationId });
                can('read', Tool, { organizationId: user.organizationId });
                can('update', Tool, { createdById: user.id });
                can('delete', Tool, { createdById: user.id });
                can('create', Hybrid, { organizationId: user.organizationId });
                can('read', Hybrid, { organizationId: user.organizationId });
                can('update', Hybrid, { createdById: user.id });
                can('delete', Hybrid, { createdById: user.id });
                can('read', Organization, { id: user.organizationId });
                can('read', User, { organizationId: user.organizationId });
                break;
            case 'VIEWER':
                can('read', Agent, { organizationId: user.organizationId });
                can('read', Tool, { organizationId: user.organizationId });
                can('read', Hybrid, { organizationId: user.organizationId });
                can('read', Organization, { id: user.organizationId });
                can('read', User, { organizationId: user.organizationId });
                break;
            default:
                cannot('manage', 'all');
        }
        return build({
            detectSubjectType: (item) => item.constructor,
        });
    }
    async checkAbility(userId, action, subject, resource) {
        const ability = await this.createForUser(userId);
        return ability.can(action, subject, resource);
    }
};
exports.CaslAbilityFactory = CaslAbilityFactory;
exports.CaslAbilityFactory = CaslAbilityFactory = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof prisma_service_1.PrismaService !== "undefined" && prisma_service_1.PrismaService) === "function" ? _a : Object])
], CaslAbilityFactory);


/***/ }),

/***/ "./src/modules/auth/controllers/auth.controller.ts":
/*!*********************************************************!*\
  !*** ./src/modules/auth/controllers/auth.controller.ts ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var _a, _b, _c, _d, _e, _f, _g;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthController = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const swagger_1 = __webpack_require__(/*! @nestjs/swagger */ "@nestjs/swagger");
const auth_service_1 = __webpack_require__(/*! ../services/auth.service */ "./src/modules/auth/services/auth.service.ts");
const jwt_auth_guard_1 = __webpack_require__(/*! ../guards/jwt-auth.guard */ "./src/modules/auth/guards/jwt-auth.guard.ts");
const local_auth_guard_1 = __webpack_require__(/*! ../guards/local-auth.guard */ "./src/modules/auth/guards/local-auth.guard.ts");
const express_1 = __webpack_require__(/*! express */ "express");
const auth_tokens_dto_1 = __webpack_require__(/*! ../dto/auth-tokens.dto */ "./src/modules/auth/dto/auth-tokens.dto.ts");
const refresh_token_dto_1 = __webpack_require__(/*! ../dto/refresh-token.dto */ "./src/modules/auth/dto/refresh-token.dto.ts");
const logout_dto_1 = __webpack_require__(/*! ../dto/logout.dto */ "./src/modules/auth/dto/logout.dto.ts");
const change_password_dto_1 = __webpack_require__(/*! ../dto/change-password.dto */ "./src/modules/auth/dto/change-password.dto.ts");
let AuthController = class AuthController {
    authService;
    constructor(authService) {
        this.authService = authService;
    }
    async register(body) {
        if (!body.email || !body.password || !body.firstName || !body.lastName) {
            throw new common_1.BadRequestException('Missing required fields');
        }
        if (body.password.length < 8) {
            throw new common_1.BadRequestException('Password must be at least 8 characters');
        }
        return this.authService.register(body);
    }
    async login(body) {
        return this.authService.login({ email: body.email, password: body.password });
    }
    async refresh(body) {
        return this.authService.refreshTokens(body.refreshToken);
    }
    async logout(req, body) {
        const userId = req.user?.id;
        await this.authService.logout(userId);
    }
    async getProfile(req) {
        const userId = req.user?.id;
        const user = await this.authService.validateUserById(userId);
        if (!user) {
            throw new common_1.BadRequestException('User not found');
        }
        return user;
    }
    async changePassword(req, body) {
        const userId = req.user?.id;
        await this.authService.changePassword(userId, body.currentPassword, body.newPassword);
        return { message: 'Password changed successfully' };
    }
};
exports.AuthController = AuthController;
__decorate([
    (0, common_1.Post)('register'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({
        summary: 'Register new user',
        description: 'Create a new user account with email and password',
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['email', 'password', 'firstName', 'lastName'],
            properties: {
                email: { type: 'string', format: 'email', example: '<EMAIL>' },
                password: { type: 'string', minLength: 8, example: 'Password123!' },
                firstName: { type: 'string', example: 'John' },
                lastName: { type: 'string', example: 'Doe' },
                organizationId: { type: 'string', example: 'org_123' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'User registered successfully',
        schema: {
            type: 'object',
            properties: {
                user: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        email: { type: 'string' },
                        firstName: { type: 'string' },
                        lastName: { type: 'string' },
                        role: { type: 'string' },
                        status: { type: 'string' },
                    },
                },
                accessToken: { type: 'string' },
                refreshToken: { type: 'string' },
                expiresIn: { type: 'number' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid registration data',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CONFLICT,
        description: 'User already exists',
    }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "register", null);
__decorate([
    (0, common_1.Post)('login'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, common_1.UseGuards)(local_auth_guard_1.LocalAuthGuard),
    (0, swagger_1.ApiOperation)({
        summary: 'User login',
        description: 'Authenticate user with email and password',
    }),
    (0, swagger_1.ApiBody)({
        schema: {
            type: 'object',
            required: ['email', 'password'],
            properties: {
                email: { type: 'string', format: 'email', example: '<EMAIL>' },
                password: { type: 'string', example: 'Password123!' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Login successful',
        schema: {
            type: 'object',
            properties: {
                user: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        email: { type: 'string' },
                        firstName: { type: 'string' },
                        lastName: { type: 'string' },
                        role: { type: 'string' },
                        status: { type: 'string' },
                    },
                },
                accessToken: { type: 'string' },
                refreshToken: { type: 'string' },
                expiresIn: { type: 'number' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Invalid credentials',
    }),
    __param(0, (0, common_1.Body)(common_1.ValidationPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "login", null);
__decorate([
    (0, common_1.Post)('refresh'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Refresh access token',
        description: 'Get a new access token using a valid refresh token',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'New tokens generated successfully',
        type: auth_tokens_dto_1.AuthTokensDto,
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({
        description: 'Invalid or expired refresh token',
    }),
    (0, swagger_1.ApiBody)({ type: refresh_token_dto_1.RefreshTokenDto }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof refresh_token_dto_1.RefreshTokenDto !== "undefined" && refresh_token_dto_1.RefreshTokenDto) === "function" ? _b : Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "refresh", null);
__decorate([
    (0, common_1.Post)('logout'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Logout user',
        description: 'Logout the current user and invalidate their tokens',
    }),
    (0, swagger_1.ApiNoContentResponse)({
        description: 'User logged out successfully',
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({
        description: 'Invalid or missing authentication token',
    }),
    (0, swagger_1.ApiBody)({ type: logout_dto_1.LogoutDto, required: false }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_c = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _c : Object, typeof (_d = typeof logout_dto_1.LogoutDto !== "undefined" && logout_dto_1.LogoutDto) === "function" ? _d : Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "logout", null);
__decorate([
    (0, common_1.Get)('profile'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Get user profile',
        description: 'Get current authenticated user profile',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'User profile retrieved successfully',
        schema: {
            type: 'object',
            properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                firstName: { type: 'string' },
                lastName: { type: 'string' },
                role: { type: 'string' },
                status: { type: 'string' },
                organization: {
                    type: 'object',
                    properties: {
                        id: { type: 'string' },
                        name: { type: 'string' },
                        slug: { type: 'string' },
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.UNAUTHORIZED,
        description: 'Invalid or expired token',
    }),
    __param(0, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "getProfile", null);
__decorate([
    (0, common_1.Put)('change-password'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Change user password',
        description: 'Change the password for the authenticated user',
    }),
    (0, swagger_1.ApiOkResponse)({
        description: 'Password changed successfully',
        schema: {
            type: 'object',
            properties: {
                message: { type: 'string', example: 'Password changed successfully' },
            },
        },
    }),
    (0, swagger_1.ApiUnauthorizedResponse)({
        description: 'Invalid authentication or current password',
    }),
    (0, swagger_1.ApiBadRequestResponse)({
        description: 'Password validation failed',
    }),
    (0, swagger_1.ApiBody)({ type: change_password_dto_1.ChangePasswordDto }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_f = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _f : Object, typeof (_g = typeof change_password_dto_1.ChangePasswordDto !== "undefined" && change_password_dto_1.ChangePasswordDto) === "function" ? _g : Object]),
    __metadata("design:returntype", Promise)
], AuthController.prototype, "changePassword", null);
exports.AuthController = AuthController = __decorate([
    (0, swagger_1.ApiTags)('Authentication'),
    (0, common_1.Controller)('auth'),
    __metadata("design:paramtypes", [typeof (_a = typeof auth_service_1.AuthService !== "undefined" && auth_service_1.AuthService) === "function" ? _a : Object])
], AuthController);


/***/ }),

/***/ "./src/modules/auth/decorators/public.decorator.ts":
/*!*********************************************************!*\
  !*** ./src/modules/auth/decorators/public.decorator.ts ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Public = exports.IS_PUBLIC_KEY = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
exports.IS_PUBLIC_KEY = 'isPublic';
const Public = () => (0, common_1.SetMetadata)(exports.IS_PUBLIC_KEY, true);
exports.Public = Public;


/***/ }),

/***/ "./src/modules/auth/decorators/roles.decorator.ts":
/*!********************************************************!*\
  !*** ./src/modules/auth/decorators/roles.decorator.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.Public = exports.Roles = exports.ROLES_KEY = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const public_decorator_1 = __webpack_require__(/*! ./public.decorator */ "./src/modules/auth/decorators/public.decorator.ts");
exports.ROLES_KEY = 'roles';
const Roles = (...roles) => (0, common_1.SetMetadata)(exports.ROLES_KEY, roles);
exports.Roles = Roles;
const Public = () => (0, common_1.SetMetadata)(public_decorator_1.IS_PUBLIC_KEY, true);
exports.Public = Public;


/***/ }),

/***/ "./src/modules/auth/dto/auth-tokens.dto.ts":
/*!*************************************************!*\
  !*** ./src/modules/auth/dto/auth-tokens.dto.ts ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthTokensDto = void 0;
const swagger_1 = __webpack_require__(/*! @nestjs/swagger */ "@nestjs/swagger");
class AuthTokensDto {
    accessToken;
    refreshToken;
    expiresIn;
}
exports.AuthTokensDto = AuthTokensDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'JWT access token',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    }),
    __metadata("design:type", String)
], AuthTokensDto.prototype, "accessToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'JWT refresh token',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    }),
    __metadata("design:type", String)
], AuthTokensDto.prototype, "refreshToken", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Access token expiration time in seconds',
        example: 900,
    }),
    __metadata("design:type", Number)
], AuthTokensDto.prototype, "expiresIn", void 0);


/***/ }),

/***/ "./src/modules/auth/dto/change-password.dto.ts":
/*!*****************************************************!*\
  !*** ./src/modules/auth/dto/change-password.dto.ts ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ChangePasswordDto = void 0;
const swagger_1 = __webpack_require__(/*! @nestjs/swagger */ "@nestjs/swagger");
const class_validator_1 = __webpack_require__(/*! class-validator */ "class-validator");
class ChangePasswordDto {
    currentPassword;
    newPassword;
}
exports.ChangePasswordDto = ChangePasswordDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Current password',
        example: 'OldPassword123!',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], ChangePasswordDto.prototype, "currentPassword", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'New password',
        example: 'NewPassword123!',
        minLength: 8,
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.MinLength)(8),
    __metadata("design:type", String)
], ChangePasswordDto.prototype, "newPassword", void 0);


/***/ }),

/***/ "./src/modules/auth/dto/logout.dto.ts":
/*!********************************************!*\
  !*** ./src/modules/auth/dto/logout.dto.ts ***!
  \********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.LogoutDto = void 0;
const swagger_1 = __webpack_require__(/*! @nestjs/swagger */ "@nestjs/swagger");
const class_validator_1 = __webpack_require__(/*! class-validator */ "class-validator");
class LogoutDto {
    refreshToken;
}
exports.LogoutDto = LogoutDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'JWT refresh token to revoke',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], LogoutDto.prototype, "refreshToken", void 0);


/***/ }),

/***/ "./src/modules/auth/dto/refresh-token.dto.ts":
/*!***************************************************!*\
  !*** ./src/modules/auth/dto/refresh-token.dto.ts ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RefreshTokenDto = void 0;
const swagger_1 = __webpack_require__(/*! @nestjs/swagger */ "@nestjs/swagger");
const class_validator_1 = __webpack_require__(/*! class-validator */ "class-validator");
class RefreshTokenDto {
    refreshToken;
}
exports.RefreshTokenDto = RefreshTokenDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'JWT refresh token',
        example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    __metadata("design:type", String)
], RefreshTokenDto.prototype, "refreshToken", void 0);


/***/ }),

/***/ "./src/modules/auth/guards/auth.guard.ts":
/*!***********************************************!*\
  !*** ./src/modules/auth/guards/auth.guard.ts ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthGuard = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const core_1 = __webpack_require__(/*! @nestjs/core */ "@nestjs/core");
const jwt_1 = __webpack_require__(/*! @nestjs/jwt */ "@nestjs/jwt");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const public_decorator_1 = __webpack_require__(/*! ../decorators/public.decorator */ "./src/modules/auth/decorators/public.decorator.ts");
let AuthGuard = class AuthGuard {
    jwtService;
    configService;
    reflector;
    constructor(jwtService, configService, reflector) {
        this.jwtService = jwtService;
        this.configService = configService;
        this.reflector = reflector;
    }
    async canActivate(context) {
        const isPublic = this.reflector.getAllAndOverride(public_decorator_1.IS_PUBLIC_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (isPublic) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const token = this.extractTokenFromHeader(request);
        if (!token) {
            throw new common_1.UnauthorizedException('No authorization token provided');
        }
        try {
            const payload = await this.jwtService.verifyAsync(token, {
                secret: this.configService.get('JWT_SECRET'),
            });
            request['user'] = payload;
            return true;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid or expired token');
        }
    }
    extractTokenFromHeader(request) {
        const [type, token] = request.headers.authorization?.split(' ') ?? [];
        return type === 'Bearer' ? token : undefined;
    }
};
exports.AuthGuard = AuthGuard;
exports.AuthGuard = AuthGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object, typeof (_c = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _c : Object])
], AuthGuard);


/***/ }),

/***/ "./src/modules/auth/guards/jwt-auth.guard.ts":
/*!***************************************************!*\
  !*** ./src/modules/auth/guards/jwt-auth.guard.ts ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.JwtAuthGuard = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const passport_1 = __webpack_require__(/*! @nestjs/passport */ "@nestjs/passport");
let JwtAuthGuard = class JwtAuthGuard extends (0, passport_1.AuthGuard)('jwt') {
    canActivate(context) {
        return super.canActivate(context);
    }
};
exports.JwtAuthGuard = JwtAuthGuard;
exports.JwtAuthGuard = JwtAuthGuard = __decorate([
    (0, common_1.Injectable)()
], JwtAuthGuard);


/***/ }),

/***/ "./src/modules/auth/guards/local-auth.guard.ts":
/*!*****************************************************!*\
  !*** ./src/modules/auth/guards/local-auth.guard.ts ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.LocalAuthGuard = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const passport_1 = __webpack_require__(/*! @nestjs/passport */ "@nestjs/passport");
let LocalAuthGuard = class LocalAuthGuard extends (0, passport_1.AuthGuard)('local') {
    canActivate(context) {
        return super.canActivate(context);
    }
};
exports.LocalAuthGuard = LocalAuthGuard;
exports.LocalAuthGuard = LocalAuthGuard = __decorate([
    (0, common_1.Injectable)()
], LocalAuthGuard);


/***/ }),

/***/ "./src/modules/auth/guards/organization.guard.ts":
/*!*******************************************************!*\
  !*** ./src/modules/auth/guards/organization.guard.ts ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.OrganizationGuard = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const core_1 = __webpack_require__(/*! @nestjs/core */ "@nestjs/core");
let OrganizationGuard = class OrganizationGuard {
    reflector;
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user || !user.organizationId) {
            throw new common_1.ForbiddenException('User organization not found');
        }
        const params = request.params;
        const body = request.body;
        const query = request.query;
        const requestOrgId = params?.organizationId ||
            body?.organizationId ||
            query?.organizationId ||
            body?.data?.organizationId ||
            body?.agent?.organizationId ||
            body?.tool?.organizationId;
        if (!requestOrgId) {
            if (body) {
                body.organizationId = user.organizationId;
            }
            return true;
        }
        if (requestOrgId !== user.organizationId) {
            if (user.role === 'SUPER_ADMIN') {
                return true;
            }
            throw new common_1.ForbiddenException('Access denied. You cannot access resources from another organization.');
        }
        return true;
    }
};
exports.OrganizationGuard = OrganizationGuard;
exports.OrganizationGuard = OrganizationGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object])
], OrganizationGuard);


/***/ }),

/***/ "./src/modules/auth/guards/roles.guard.ts":
/*!************************************************!*\
  !*** ./src/modules/auth/guards/roles.guard.ts ***!
  \************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RolesGuard = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const core_1 = __webpack_require__(/*! @nestjs/core */ "@nestjs/core");
const roles_decorator_1 = __webpack_require__(/*! ../decorators/roles.decorator */ "./src/modules/auth/decorators/roles.decorator.ts");
let RolesGuard = class RolesGuard {
    reflector;
    roleHierarchy = {
        SUPER_ADMIN: 4,
        ORG_ADMIN: 3,
        DEVELOPER: 2,
        VIEWER: 1,
    };
    constructor(reflector) {
        this.reflector = reflector;
    }
    canActivate(context) {
        const requiredRoles = this.reflector.getAllAndOverride(roles_decorator_1.ROLES_KEY, [
            context.getHandler(),
            context.getClass(),
        ]);
        if (!requiredRoles || requiredRoles.length === 0) {
            return true;
        }
        const request = context.switchToHttp().getRequest();
        const user = request.user;
        if (!user || !user.role) {
            throw new common_1.ForbiddenException('User role not found');
        }
        const userRoleLevel = this.roleHierarchy[user.role] || 0;
        const hasRequiredRole = requiredRoles.some(role => {
            const requiredLevel = this.roleHierarchy[role];
            return userRoleLevel >= requiredLevel;
        });
        if (!hasRequiredRole) {
            throw new common_1.ForbiddenException(`Access denied. Required role: ${requiredRoles.join(' or ')}`);
        }
        return true;
    }
};
exports.RolesGuard = RolesGuard;
exports.RolesGuard = RolesGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof core_1.Reflector !== "undefined" && core_1.Reflector) === "function" ? _a : Object])
], RolesGuard);


/***/ }),

/***/ "./src/modules/auth/guards/ws-jwt.guard.ts":
/*!*************************************************!*\
  !*** ./src/modules/auth/guards/ws-jwt.guard.ts ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var WsJwtGuard_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.WsJwtGuard = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const jwt_1 = __webpack_require__(/*! @nestjs/jwt */ "@nestjs/jwt");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const websockets_1 = __webpack_require__(/*! @nestjs/websockets */ "@nestjs/websockets");
let WsJwtGuard = WsJwtGuard_1 = class WsJwtGuard {
    jwtService;
    configService;
    logger = new common_1.Logger(WsJwtGuard_1.name);
    constructor(jwtService, configService) {
        this.jwtService = jwtService;
        this.configService = configService;
    }
    async canActivate(context) {
        try {
            const client = context.switchToWs().getClient();
            const token = this.extractToken(client);
            if (!token) {
                throw new websockets_1.WsException('Missing authentication token');
            }
            const payload = this.jwtService.verify(token, {
                secret: this.configService.get('JWT_SECRET'),
            });
            client.data.user = payload;
            return true;
        }
        catch (error) {
            this.logger.error('WebSocket authentication failed', error);
            throw new websockets_1.WsException('Invalid authentication token');
        }
    }
    extractToken(client) {
        if (client.handshake.auth?.token) {
            return client.handshake.auth.token;
        }
        const authHeader = client.handshake.headers?.authorization;
        if (authHeader?.startsWith('Bearer ')) {
            return authHeader.substring(7);
        }
        return null;
    }
};
exports.WsJwtGuard = WsJwtGuard;
exports.WsJwtGuard = WsJwtGuard = WsJwtGuard_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object])
], WsJwtGuard);


/***/ }),

/***/ "./src/modules/auth/services/auth.service.ts":
/*!***************************************************!*\
  !*** ./src/modules/auth/services/auth.service.ts ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var AuthService_1;
var _a, _b, _c, _d;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.AuthService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const jwt_1 = __webpack_require__(/*! @nestjs/jwt */ "@nestjs/jwt");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const prisma_service_1 = __webpack_require__(/*! ../../prisma/prisma.service */ "./src/modules/prisma/prisma.service.ts");
const redis_service_1 = __webpack_require__(/*! ../../redis/redis.service */ "./src/modules/redis/redis.service.ts");
const bcrypt = __importStar(__webpack_require__(/*! bcrypt */ "bcrypt"));
const uuid_1 = __webpack_require__(/*! uuid */ "uuid");
const client_1 = __webpack_require__(/*! .prisma/client */ ".prisma/client");
let AuthService = AuthService_1 = class AuthService {
    prismaService;
    jwtService;
    configService;
    redisService;
    logger = new common_1.Logger(AuthService_1.name);
    jwtAccessExpiration;
    jwtRefreshExpiration;
    saltRounds = 12;
    constructor(prismaService, jwtService, configService, redisService) {
        this.prismaService = prismaService;
        this.jwtService = jwtService;
        this.configService = configService;
        this.redisService = redisService;
        this.jwtAccessExpiration = this.configService.get('JWT_ACCESS_EXPIRATION_SECONDS', 900);
        this.jwtRefreshExpiration = this.configService.get('JWT_REFRESH_EXPIRATION_SECONDS', 604800);
    }
    async register(dto) {
        const { email, password, firstName, lastName, organizationName, organizationSlug } = dto;
        const existingUser = await this.prismaService.user.findUnique({
            where: { email },
        });
        if (existingUser) {
            throw new common_1.BadRequestException('User with this email already exists');
        }
        const passwordHash = await bcrypt.hash(password, this.saltRounds);
        const result = await this.prismaService.$transaction(async (prisma) => {
            let organizationId;
            if (organizationName && organizationSlug) {
                const organization = await prisma.organization.create({
                    data: {
                        name: organizationName,
                        slug: organizationSlug,
                        settings: {
                            features: ['agents', 'tools', 'workflows'],
                            limits: {
                                agents: 5,
                                tools: 10,
                                workflows: 5,
                                users: 3,
                            },
                        },
                    },
                });
                organizationId = organization.id;
            }
            else {
                const defaultOrg = await prisma.organization.findFirst({
                    where: { slug: 'default' },
                });
                if (defaultOrg) {
                    organizationId = defaultOrg.id;
                }
                else {
                    const newDefaultOrg = await prisma.organization.create({
                        data: {
                            name: 'Default Organization',
                            slug: 'default',
                        },
                    });
                    organizationId = newDefaultOrg.id;
                }
            }
            const defaultRole = await prisma.role.findFirst({
                where: { level: 'DEVELOPER' },
            });
            if (!defaultRole) {
                throw new Error('Default role not found. Please run database seed.');
            }
            const user = await prisma.user.create({
                data: {
                    email,
                    passwordHash,
                    firstName,
                    lastName,
                    organizationId,
                    roleId: defaultRole.id,
                    emailVerifyToken: (0, uuid_1.v4)(),
                },
                include: {
                    role: true,
                    organization: true,
                },
            });
            return user;
        });
        const tokens = await this.generateTokens(result);
        await this.storeRefreshToken(result.id, tokens.refreshToken);
        this.logger.log(`User registered: ${email} in organization: ${result.organization.name}`);
        return tokens;
    }
    async login(dto) {
        const { email, password, organizationId } = dto;
        const user = await this.prismaService.user.findFirst({
            where: {
                email,
                ...(organizationId && { organizationId }),
                status: client_1.UserStatus.ACTIVE,
            },
            include: {
                role: true,
                organization: true,
            },
        });
        if (!user) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const isPasswordValid = await bcrypt.compare(password, user.passwordHash || '');
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        if (user.organization.status !== 'ACTIVE') {
            throw new common_1.UnauthorizedException('Organization is not active');
        }
        const tokens = await this.generateTokens(user);
        await this.storeRefreshToken(user.id, tokens.refreshToken);
        await this.prismaService.user.update({
            where: { id: user.id },
            data: { lastSeen: new Date() },
        });
        this.logger.log(`User logged in: ${email} from organization: ${user.organization.name}`);
        return tokens;
    }
    async refreshTokens(refreshToken) {
        try {
            const payload = this.jwtService.verify(refreshToken, {
                secret: this.configService.get('JWT_REFRESH_SECRET'),
            });
            const storedToken = await this.redisService.get(`refresh_token:${payload.sub}`);
            if (!storedToken || storedToken !== refreshToken) {
                throw new common_1.UnauthorizedException('Invalid refresh token');
            }
            const user = await this.prismaService.user.findUnique({
                where: { id: payload.sub },
                include: {
                    role: true,
                    organization: true,
                },
            });
            if (!user || user.status !== client_1.UserStatus.ACTIVE) {
                throw new common_1.UnauthorizedException('User not found or inactive');
            }
            const tokens = await this.generateTokens(user);
            await this.storeRefreshToken(user.id, tokens.refreshToken);
            return tokens;
        }
        catch (error) {
            this.logger.error('Refresh token error:', error);
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
    }
    async logout(userId) {
        await this.redisService.del(`refresh_token:${userId}`);
        const sessionKeys = await this.redisService.keys(`session:${userId}:*`);
        if (sessionKeys && sessionKeys.length > 0) {
            await Promise.all(sessionKeys.map((key) => this.redisService.del(key)));
        }
        this.logger.log(`User logged out: ${userId}`);
    }
    async validateToken(token) {
        try {
            const payload = this.jwtService.verify(token, {
                secret: this.configService.get('JWT_SECRET'),
            });
            const user = await this.prismaService.user.findUnique({
                where: { id: payload.sub },
                select: { status: true },
            });
            if (!user || user.status !== client_1.UserStatus.ACTIVE) {
                throw new common_1.UnauthorizedException('User not found or inactive');
            }
            return payload;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid token');
        }
    }
    async changePassword(userId, currentPassword, newPassword) {
        const user = await this.prismaService.user.findUnique({
            where: { id: userId },
        });
        if (!user) {
            throw new common_1.BadRequestException('User not found');
        }
        const isPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash || '');
        if (!isPasswordValid) {
            throw new common_1.BadRequestException('Current password is incorrect');
        }
        const newPasswordHash = await bcrypt.hash(newPassword, this.saltRounds);
        await this.prismaService.user.update({
            where: { id: userId },
            data: {
                passwordHash: newPasswordHash,
                resetToken: null,
                resetTokenExpiry: null,
            },
        });
        await this.logout(userId);
        this.logger.log(`Password changed for user: ${userId}`);
    }
    async generateTokens(user) {
        const permissions = user.role.permissions;
        const payload = {
            sub: user.id,
            email: user.email,
            organizationId: user.organizationId,
            role: user.role.name,
            permissions,
        };
        const [accessToken, refreshToken] = await Promise.all([
            this.jwtService.signAsync(payload, {
                secret: this.configService.get('JWT_SECRET'),
                expiresIn: this.jwtAccessExpiration,
            }),
            this.jwtService.signAsync(payload, {
                secret: this.configService.get('JWT_REFRESH_SECRET'),
                expiresIn: this.jwtRefreshExpiration,
            }),
        ]);
        return {
            accessToken,
            refreshToken,
            expiresIn: this.jwtAccessExpiration,
        };
    }
    async storeRefreshToken(userId, refreshToken) {
        await this.redisService.set(`refresh_token:${userId}`, refreshToken, this.jwtRefreshExpiration);
    }
    async validateUserById(userId) {
        return this.prismaService.user.findUnique({
            where: { id: userId },
            include: {
                role: true,
                organization: true,
            },
        });
    }
    async createApiKey(organizationId, name) {
        const apiKey = `synapseai_${(0, uuid_1.v4)().replace(/-/g, '')}`;
        const hashedKey = await bcrypt.hash(apiKey, this.saltRounds);
        await this.redisService.set(`api_key:${hashedKey}`, JSON.stringify({ organizationId, name }), 0);
        return apiKey;
    }
    async validateApiKey(apiKey) {
        const keyData = await this.redisService.get(`api_key:${apiKey}`);
        if (!keyData) {
            return null;
        }
        return JSON.parse(keyData);
    }
};
exports.AuthService = AuthService;
exports.AuthService = AuthService = AuthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof prisma_service_1.PrismaService !== "undefined" && prisma_service_1.PrismaService) === "function" ? _a : Object, typeof (_b = typeof jwt_1.JwtService !== "undefined" && jwt_1.JwtService) === "function" ? _b : Object, typeof (_c = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _c : Object, typeof (_d = typeof redis_service_1.RedisService !== "undefined" && redis_service_1.RedisService) === "function" ? _d : Object])
], AuthService);


/***/ }),

/***/ "./src/modules/auth/strategies/api-key.strategy.ts":
/*!*********************************************************!*\
  !*** ./src/modules/auth/strategies/api-key.strategy.ts ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.ApiKeyStrategy = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const passport_1 = __webpack_require__(/*! @nestjs/passport */ "@nestjs/passport");
const prisma_service_1 = __webpack_require__(/*! ../../prisma/prisma.service */ "./src/modules/prisma/prisma.service.ts");
const auth_service_1 = __webpack_require__(/*! ../services/auth.service */ "./src/modules/auth/services/auth.service.ts");
let ApiKeyStrategy = class ApiKeyStrategy extends (0, passport_1.PassportStrategy)(class {
}, 'api-key') {
    prismaService;
    authService;
    constructor(prismaService, authService) {
        super();
        this.prismaService = prismaService;
        this.authService = authService;
    }
    async validate(apiKey) {
        try {
            const apiKeyRecord = await this.prismaService.apiKey.findFirst({
                where: {
                    key: apiKey,
                    isActive: true,
                    OR: [
                        { expiresAt: null },
                        { expiresAt: { gt: new Date() } }
                    ]
                },
                include: {
                    organization: true,
                    createdBy: {
                        include: {
                            role: true,
                        }
                    }
                }
            });
            if (!apiKeyRecord) {
                throw new common_1.UnauthorizedException('Invalid or expired API key');
            }
            await this.prismaService.apiKey.update({
                where: { id: apiKeyRecord.id },
                data: {
                    lastUsedAt: new Date(),
                    usageCount: { increment: 1 }
                }
            });
            if (apiKeyRecord.quotaLimit && apiKeyRecord.usageCount >= apiKeyRecord.quotaLimit) {
                throw new common_1.UnauthorizedException('API key quota exceeded');
            }
            return {
                id: apiKeyRecord.createdBy.id,
                email: apiKeyRecord.createdBy.email,
                organizationId: apiKeyRecord.organizationId,
                role: apiKeyRecord.createdBy.role.name,
                apiKeyId: apiKeyRecord.id,
                permissions: apiKeyRecord.permissions,
                type: 'api-key'
            };
        }
        catch (error) {
            if (error instanceof common_1.UnauthorizedException) {
                throw error;
            }
            throw new common_1.UnauthorizedException('Invalid API key');
        }
    }
};
exports.ApiKeyStrategy = ApiKeyStrategy;
exports.ApiKeyStrategy = ApiKeyStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof prisma_service_1.PrismaService !== "undefined" && prisma_service_1.PrismaService) === "function" ? _a : Object, typeof (_b = typeof auth_service_1.AuthService !== "undefined" && auth_service_1.AuthService) === "function" ? _b : Object])
], ApiKeyStrategy);


/***/ }),

/***/ "./src/modules/auth/strategies/jwt-refresh.strategy.ts":
/*!*************************************************************!*\
  !*** ./src/modules/auth/strategies/jwt-refresh.strategy.ts ***!
  \*************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.JwtRefreshStrategy = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const passport_1 = __webpack_require__(/*! @nestjs/passport */ "@nestjs/passport");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const passport_jwt_1 = __webpack_require__(/*! passport-jwt */ "passport-jwt");
const prisma_service_1 = __webpack_require__(/*! ../../prisma/prisma.service */ "./src/modules/prisma/prisma.service.ts");
let JwtRefreshStrategy = class JwtRefreshStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy, 'jwt-refresh') {
    configService;
    prismaService;
    constructor(configService, prismaService) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromBodyField('refreshToken'),
            ignoreExpiration: false,
            secretOrKey: configService.get('JWT_REFRESH_SECRET'),
        });
        this.configService = configService;
        this.prismaService = prismaService;
    }
    async validate(payload) {
        const user = await this.prismaService.user.findFirst({
            where: {
                id: payload.sub,
            },
        });
        if (!user) {
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
        return { userId: payload.sub, email: payload.email, user };
    }
};
exports.JwtRefreshStrategy = JwtRefreshStrategy;
exports.JwtRefreshStrategy = JwtRefreshStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object, typeof (_b = typeof prisma_service_1.PrismaService !== "undefined" && prisma_service_1.PrismaService) === "function" ? _b : Object])
], JwtRefreshStrategy);


/***/ }),

/***/ "./src/modules/auth/strategies/jwt.strategy.ts":
/*!*****************************************************!*\
  !*** ./src/modules/auth/strategies/jwt.strategy.ts ***!
  \*****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.JwtStrategy = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const passport_1 = __webpack_require__(/*! @nestjs/passport */ "@nestjs/passport");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const passport_jwt_1 = __webpack_require__(/*! passport-jwt */ "passport-jwt");
const auth_service_1 = __webpack_require__(/*! ../services/auth.service */ "./src/modules/auth/services/auth.service.ts");
let JwtStrategy = class JwtStrategy extends (0, passport_1.PassportStrategy)(passport_jwt_1.Strategy) {
    authService;
    configService;
    constructor(authService, configService) {
        super({
            jwtFromRequest: passport_jwt_1.ExtractJwt.fromAuthHeaderAsBearerToken(),
            ignoreExpiration: false,
            secretOrKey: configService.get('JWT_SECRET'),
        });
        this.authService = authService;
        this.configService = configService;
    }
    async validate(payload) {
        try {
            const validatedPayload = await this.authService.validateToken(payload);
            if (!validatedPayload) {
                throw new common_1.UnauthorizedException('Invalid token');
            }
            return validatedPayload;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid token');
        }
    }
};
exports.JwtStrategy = JwtStrategy;
exports.JwtStrategy = JwtStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof auth_service_1.AuthService !== "undefined" && auth_service_1.AuthService) === "function" ? _a : Object, typeof (_b = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _b : Object])
], JwtStrategy);


/***/ }),

/***/ "./src/modules/auth/strategies/local.strategy.ts":
/*!*******************************************************!*\
  !*** ./src/modules/auth/strategies/local.strategy.ts ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.LocalStrategy = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const passport_1 = __webpack_require__(/*! @nestjs/passport */ "@nestjs/passport");
const passport_local_1 = __webpack_require__(/*! passport-local */ "passport-local");
const auth_service_1 = __webpack_require__(/*! ../services/auth.service */ "./src/modules/auth/services/auth.service.ts");
let LocalStrategy = class LocalStrategy extends (0, passport_1.PassportStrategy)(passport_local_1.Strategy) {
    authService;
    constructor(authService) {
        super({
            usernameField: 'email',
            passwordField: 'password',
        });
        this.authService = authService;
    }
    async validate(email, password) {
        try {
            const result = await this.authService.login({ email, password });
            if (!result) {
                throw new common_1.UnauthorizedException('Invalid credentials');
            }
            return result;
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
    }
};
exports.LocalStrategy = LocalStrategy;
exports.LocalStrategy = LocalStrategy = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof auth_service_1.AuthService !== "undefined" && auth_service_1.AuthService) === "function" ? _a : Object])
], LocalStrategy);


/***/ }),

/***/ "./src/modules/health/health.controller.ts":
/*!*************************************************!*\
  !*** ./src/modules/health/health.controller.ts ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.HealthController = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const terminus_1 = __webpack_require__(/*! @nestjs/terminus */ "@nestjs/terminus");
const swagger_1 = __webpack_require__(/*! @nestjs/swagger */ "@nestjs/swagger");
const health_service_1 = __webpack_require__(/*! ./health.service */ "./src/modules/health/health.service.ts");
const prisma_service_1 = __webpack_require__(/*! ../prisma/prisma.service */ "./src/modules/prisma/prisma.service.ts");
let HealthController = class HealthController {
    health;
    healthService;
    prismaHealth;
    memoryHealth;
    diskHealth;
    prismaService;
    constructor(health, healthService, prismaHealth, memoryHealth, diskHealth, prismaService) {
        this.health = health;
        this.healthService = healthService;
        this.prismaHealth = prismaHealth;
        this.memoryHealth = memoryHealth;
        this.diskHealth = diskHealth;
        this.prismaService = prismaService;
    }
    async check() {
        return this.health.check([
            () => this.memoryHealth.checkHeap('memory_heap', 150 * 1024 * 1024),
            () => this.memoryHealth.checkRSS('memory_rss', 150 * 1024 * 1024),
        ]);
    }
    async checkDatabase() {
        return this.health.check([
            () => this.healthService.databaseCheck(),
        ]);
    }
    async checkRedis() {
        return this.health.check([
            () => this.healthService.redisCheck(),
        ]);
    }
    async checkVectorDb() {
        return this.health.check([
            () => this.healthService.vectorDbCheck(),
        ]);
    }
    async checkExternal() {
        return this.health.check([
            () => this.healthService.externalServicesCheck(),
        ]);
    }
    async checkAll() {
        return this.health.check([
            () => this.memoryHealth.checkHeap('memory_heap', 200 * 1024 * 1024),
            () => this.memoryHealth.checkRSS('memory_rss', 200 * 1024 * 1024),
            () => this.diskHealth.checkStorage('storage', {
                path: '/',
                thresholdPercent: 0.9,
            }),
            () => this.healthService.databaseCheck(),
            () => this.healthService.redisCheck(),
            () => this.healthService.vectorDbCheck(),
            () => this.healthService.externalServicesCheck(),
        ]);
    }
    async getMetrics() {
        return {
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            memory: process.memoryUsage(),
            cpu: process.cpuUsage(),
            platform: process.platform,
            nodeVersion: process.version,
        };
    }
};
exports.HealthController = HealthController;
__decorate([
    (0, common_1.Get)(),
    (0, terminus_1.HealthCheck)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Basic health check',
        description: 'Check if the service is running and healthy',
    }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Service is healthy',
        schema: {
            type: 'object',
            properties: {
                status: { type: 'string', example: 'ok' },
                info: { type: 'object' },
                error: { type: 'object' },
                details: { type: 'object' },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: 503,
        description: 'Service is unhealthy',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], HealthController.prototype, "check", null);
__decorate([
    (0, common_1.Get)('database'),
    (0, terminus_1.HealthCheck)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Database health check',
        description: 'Check PostgreSQL database connectivity and performance',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], HealthController.prototype, "checkDatabase", null);
__decorate([
    (0, common_1.Get)('redis'),
    (0, terminus_1.HealthCheck)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Redis health check',
        description: 'Check Redis connectivity and performance',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_j = typeof Promise !== "undefined" && Promise) === "function" ? _j : Object)
], HealthController.prototype, "checkRedis", null);
__decorate([
    (0, common_1.Get)('vector-db'),
    (0, terminus_1.HealthCheck)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Vector database health check',
        description: 'Check Pinecone/vector database connectivity',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], HealthController.prototype, "checkVectorDb", null);
__decorate([
    (0, common_1.Get)('external'),
    (0, terminus_1.HealthCheck)(),
    (0, swagger_1.ApiOperation)({
        summary: 'External dependencies health check',
        description: 'Check external API dependencies (OpenAI, etc.)',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_l = typeof Promise !== "undefined" && Promise) === "function" ? _l : Object)
], HealthController.prototype, "checkExternal", null);
__decorate([
    (0, common_1.Get)('comprehensive'),
    (0, terminus_1.HealthCheck)(),
    (0, swagger_1.ApiOperation)({
        summary: 'Comprehensive health check',
        description: 'Check all system components and dependencies',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_m = typeof Promise !== "undefined" && Promise) === "function" ? _m : Object)
], HealthController.prototype, "checkAll", null);
__decorate([
    (0, common_1.Get)('metrics'),
    (0, swagger_1.ApiOperation)({
        summary: 'System metrics',
        description: 'Get detailed system metrics and performance data',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], HealthController.prototype, "getMetrics", null);
exports.HealthController = HealthController = __decorate([
    (0, swagger_1.ApiTags)('Health'),
    (0, common_1.Controller)('health'),
    __metadata("design:paramtypes", [typeof (_a = typeof terminus_1.HealthCheckService !== "undefined" && terminus_1.HealthCheckService) === "function" ? _a : Object, typeof (_b = typeof health_service_1.HealthService !== "undefined" && health_service_1.HealthService) === "function" ? _b : Object, typeof (_c = typeof terminus_1.PrismaHealthIndicator !== "undefined" && terminus_1.PrismaHealthIndicator) === "function" ? _c : Object, typeof (_d = typeof terminus_1.MemoryHealthIndicator !== "undefined" && terminus_1.MemoryHealthIndicator) === "function" ? _d : Object, typeof (_e = typeof terminus_1.DiskHealthIndicator !== "undefined" && terminus_1.DiskHealthIndicator) === "function" ? _e : Object, typeof (_f = typeof prisma_service_1.PrismaService !== "undefined" && prisma_service_1.PrismaService) === "function" ? _f : Object])
], HealthController);


/***/ }),

/***/ "./src/modules/health/health.module.ts":
/*!*********************************************!*\
  !*** ./src/modules/health/health.module.ts ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.HealthModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const terminus_1 = __webpack_require__(/*! @nestjs/terminus */ "@nestjs/terminus");
const axios_1 = __webpack_require__(/*! @nestjs/axios */ "@nestjs/axios");
const health_controller_1 = __webpack_require__(/*! ./health.controller */ "./src/modules/health/health.controller.ts");
const health_service_1 = __webpack_require__(/*! ./health.service */ "./src/modules/health/health.service.ts");
const prisma_module_1 = __webpack_require__(/*! ../prisma/prisma.module */ "./src/modules/prisma/prisma.module.ts");
const redis_module_1 = __webpack_require__(/*! ../redis/redis.module */ "./src/modules/redis/redis.module.ts");
const auth_module_1 = __webpack_require__(/*! ../auth/auth.module */ "./src/modules/auth/auth.module.ts");
let HealthModule = class HealthModule {
};
exports.HealthModule = HealthModule;
exports.HealthModule = HealthModule = __decorate([
    (0, common_1.Module)({
        imports: [
            terminus_1.TerminusModule,
            axios_1.HttpModule.register({
                timeout: 5000,
                maxRedirects: 3,
            }),
            prisma_module_1.PrismaModule,
            redis_module_1.RedisModule,
            auth_module_1.AuthModule,
        ],
        controllers: [health_controller_1.HealthController],
        providers: [health_service_1.HealthService],
        exports: [health_service_1.HealthService],
    })
], HealthModule);


/***/ }),

/***/ "./src/modules/health/health.service.ts":
/*!**********************************************!*\
  !*** ./src/modules/health/health.service.ts ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var HealthService_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.HealthService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const prisma_service_1 = __webpack_require__(/*! ../prisma/prisma.service */ "./src/modules/prisma/prisma.service.ts");
const redis_service_1 = __webpack_require__(/*! ../redis/redis.service */ "./src/modules/redis/redis.service.ts");
let HealthService = HealthService_1 = class HealthService {
    prismaService;
    redisService;
    logger = new common_1.Logger(HealthService_1.name);
    constructor(prismaService, redisService) {
        this.prismaService = prismaService;
        this.redisService = redisService;
    }
    async databaseCheck() {
        const key = 'database';
        try {
            const isHealthy = await this.prismaService.healthCheck();
            if (isHealthy) {
                return {
                    [key]: {
                        status: 'up',
                        message: 'PostgreSQL is healthy',
                    },
                };
            }
            return {
                [key]: {
                    status: 'down',
                    message: 'PostgreSQL is unhealthy',
                },
            };
        }
        catch (error) {
            this.logger.error('Database health check failed', error);
            return {
                [key]: {
                    status: 'down',
                    message: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }
    async redisCheck() {
        const key = 'redis';
        try {
            const isHealthy = await this.redisService.healthCheck();
            if (isHealthy) {
                return {
                    [key]: {
                        status: 'up',
                        message: 'Redis is healthy',
                    },
                };
            }
            return {
                [key]: {
                    status: 'down',
                    message: 'Redis is unhealthy',
                },
            };
        }
        catch (error) {
            this.logger.error('Redis health check failed', error);
            return {
                [key]: {
                    status: 'down',
                    message: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }
    async vectorDbCheck() {
        const key = 'vectorDb';
        try {
            return {
                [key]: {
                    status: 'up',
                    message: 'Vector database is healthy',
                },
            };
        }
        catch (error) {
            this.logger.error('Vector database health check failed', error);
            return {
                [key]: {
                    status: 'down',
                    message: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }
    async externalServicesCheck() {
        const key = 'externalServices';
        try {
            return {
                [key]: {
                    status: 'up',
                    message: 'External services are healthy',
                },
            };
        }
        catch (error) {
            this.logger.error('External services health check failed', error);
            return {
                [key]: {
                    status: 'down',
                    message: error instanceof Error ? error.message : 'Unknown error',
                },
            };
        }
    }
};
exports.HealthService = HealthService;
exports.HealthService = HealthService = HealthService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof prisma_service_1.PrismaService !== "undefined" && prisma_service_1.PrismaService) === "function" ? _a : Object, typeof (_b = typeof redis_service_1.RedisService !== "undefined" && redis_service_1.RedisService) === "function" ? _b : Object])
], HealthService);


/***/ }),

/***/ "./src/modules/prisma/prisma.module.ts":
/*!*********************************************!*\
  !*** ./src/modules/prisma/prisma.module.ts ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.PrismaModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const prisma_service_1 = __webpack_require__(/*! ./prisma.service */ "./src/modules/prisma/prisma.service.ts");
let PrismaModule = class PrismaModule {
};
exports.PrismaModule = PrismaModule;
exports.PrismaModule = PrismaModule = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({
        providers: [prisma_service_1.PrismaService],
        exports: [prisma_service_1.PrismaService],
    })
], PrismaModule);


/***/ }),

/***/ "./src/modules/prisma/prisma.service.ts":
/*!**********************************************!*\
  !*** ./src/modules/prisma/prisma.service.ts ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PrismaService_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.PrismaService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const client_1 = __webpack_require__(/*! .prisma/client */ ".prisma/client");
let PrismaService = PrismaService_1 = class PrismaService extends client_1.PrismaClient {
    configService;
    logger = new common_1.Logger(PrismaService_1.name);
    constructor(configService) {
        const databaseUrl = configService.get('DATABASE_URL');
        const nodeEnv = configService.get('NODE_ENV', 'development');
        super({
            datasources: {
                db: {
                    url: databaseUrl,
                },
            },
            log: nodeEnv === 'development'
                ? ['query', 'info', 'warn', 'error']
                : ['warn', 'error'],
            errorFormat: nodeEnv === 'development' ? 'pretty' : 'minimal',
        });
        this.configService = configService;
    }
    async onModuleInit() {
        try {
            this.logger.log('Connecting to PostgreSQL database...');
            await this.$connect();
            this.logger.log('✅ Database connected successfully');
        }
        catch (error) {
            this.logger.error('❌ Failed to connect to database', error);
            throw error;
        }
        if (this.configService.get('NODE_ENV') === 'development') {
            this.$on('query', (e) => {
                this.logger.debug(`Query: ${e.query}`);
                this.logger.debug(`Duration: ${e.duration}ms`);
            });
        }
    }
    async onModuleDestroy() {
        try {
            this.logger.log('Disconnecting from database...');
            await this.$disconnect();
            this.logger.log('✅ Database disconnected successfully');
        }
        catch (error) {
            this.logger.error('❌ Error during database disconnection', error);
        }
    }
    async healthCheck() {
        try {
            await this.$queryRaw `SELECT 1`;
            return true;
        }
        catch (error) {
            this.logger.error('Database health check failed', error);
            return false;
        }
    }
    async cleanDatabase() {
        if (this.configService.get('NODE_ENV') === 'production') {
            throw new Error('Cannot clean database in production!');
        }
        const models = Object.keys(this).filter((key) => {
            return key && typeof key === 'string' && key.length > 0 &&
                key.charAt(0) !== '_' &&
                key.charAt(0) === key.charAt(0).toLowerCase() &&
                key !== '$' &&
                key !== 'user';
        });
        for (const model of models) {
            try {
                await this[model].deleteMany();
                this.logger.log(`Cleaned model: ${model}`);
            }
            catch (error) {
                this.logger.error(`Failed to clean model ${model}`, error);
            }
        }
    }
    enableShutdownHooks(app) {
        this.$on('beforeExit', async () => {
            await app.close();
        });
    }
};
exports.PrismaService = PrismaService;
exports.PrismaService = PrismaService = PrismaService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], PrismaService);


/***/ }),

/***/ "./src/modules/redis/redis.module.ts":
/*!*******************************************!*\
  !*** ./src/modules/redis/redis.module.ts ***!
  \*******************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var RedisModule_1;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RedisModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const redis_service_1 = __webpack_require__(/*! ./redis.service */ "./src/modules/redis/redis.service.ts");
let RedisModule = RedisModule_1 = class RedisModule {
    static forRootAsync(options) {
        return {
            module: RedisModule_1,
            imports: [config_1.ConfigModule, ...(options.imports || [])],
            providers: [
                {
                    provide: 'REDIS_OPTIONS',
                    useFactory: options.useFactory,
                    inject: options.inject || [],
                },
                redis_service_1.RedisService,
            ],
            exports: [redis_service_1.RedisService],
        };
    }
    static forRoot(options) {
        return {
            module: RedisModule_1,
            imports: [config_1.ConfigModule],
            providers: [
                {
                    provide: 'REDIS_OPTIONS',
                    useValue: options,
                },
                redis_service_1.RedisService,
            ],
            exports: [redis_service_1.RedisService],
        };
    }
};
exports.RedisModule = RedisModule;
exports.RedisModule = RedisModule = RedisModule_1 = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({})
], RedisModule);


/***/ }),

/***/ "./src/modules/redis/redis.service.ts":
/*!********************************************!*\
  !*** ./src/modules/redis/redis.service.ts ***!
  \********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var RedisService_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.RedisService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const ioredis_1 = __importDefault(__webpack_require__(/*! ioredis */ "ioredis"));
const redis_module_1 = __webpack_require__(/*! ./redis.module */ "./src/modules/redis/redis.module.ts");
let RedisService = RedisService_1 = class RedisService {
    options;
    logger = new common_1.Logger(RedisService_1.name);
    client = null;
    subscriber = null;
    publisher = null;
    constructor(options) {
        this.options = options;
    }
    async keys(pattern) {
        if (!this.client) {
            throw new Error('Redis client not initialized');
        }
        return this.client.keys(pattern);
    }
    async onModuleInit() {
        try {
            this.logger.log('Connecting to Redis...');
            this.client = new ioredis_1.default({
                host: this.options.host,
                port: this.options.port,
                password: this.options.password,
                db: this.options.db || 0,
                maxRetriesPerRequest: this.options.maxRetriesPerRequest || 3,
                lazyConnect: true,
            });
            this.subscriber = new ioredis_1.default({
                host: this.options.host,
                port: this.options.port,
                password: this.options.password,
                db: this.options.db || 0,
                lazyConnect: true,
            });
            this.publisher = new ioredis_1.default({
                host: this.options.host,
                port: this.options.port,
                password: this.options.password,
                db: this.options.db || 0,
                lazyConnect: true,
            });
            await Promise.all([
                this.client.connect(),
                this.subscriber.connect(),
                this.publisher.connect(),
            ]);
            await this.client.ping();
            this.logger.log('✅ Redis connected successfully');
            this.setupErrorHandlers();
        }
        catch (error) {
            this.logger.error('❌ Failed to connect to Redis', error);
            throw error;
        }
    }
    async onModuleDestroy() {
        try {
            this.logger.log('Disconnecting from Redis...');
            if (this.client) {
                await this.client.quit();
            }
            if (this.subscriber) {
                await this.subscriber.quit();
            }
            if (this.publisher) {
                await this.publisher.quit();
            }
            this.logger.log('✅ Redis disconnected successfully');
        }
        catch (error) {
            this.logger.error('❌ Error during Redis disconnection', error);
        }
    }
    setupErrorHandlers() {
        if (this.client) {
            this.client.on('error', (error) => {
                this.logger.error('Redis client error', error);
            });
            this.client.on('reconnecting', () => {
                this.logger.warn('Redis client reconnecting...');
            });
        }
        if (this.subscriber) {
            this.subscriber.on('error', (error) => {
                this.logger.error('Redis subscriber error', error);
            });
        }
        if (this.publisher) {
            this.publisher.on('error', (error) => {
                this.logger.error('Redis publisher error', error);
            });
        }
    }
    getClient() {
        if (!this.client) {
            throw new Error('Redis client not initialized');
        }
        return this.client;
    }
    getPubSubClient() {
        if (!this.subscriber) {
            throw new Error('Redis subscriber not initialized');
        }
        return this.subscriber;
    }
    getSubscriber() {
        if (!this.subscriber) {
            throw new Error('Redis subscriber not initialized');
        }
        return this.subscriber;
    }
    getPublisher() {
        if (!this.publisher) {
            throw new Error('Redis publisher not initialized');
        }
        return this.publisher;
    }
    async setSession(sessionId, data, ttl = 3600) {
        await this.getClient().setex(`session:${sessionId}`, ttl, JSON.stringify(data));
    }
    async getSession(sessionId) {
        const data = await this.getClient().get(`session:${sessionId}`);
        return data ? JSON.parse(data) : null;
    }
    async deleteSession(sessionId) {
        await this.getClient().del(`session:${sessionId}`);
    }
    async extendSession(sessionId, ttl = 3600) {
        await this.getClient().expire(`session:${sessionId}`, ttl);
    }
    async set(key, value, ttl) {
        const serialized = JSON.stringify(value);
        if (ttl) {
            await this.getClient().setex(key, ttl, serialized);
        }
        else {
            await this.getClient().set(key, serialized);
        }
    }
    async get(key) {
        const data = await this.getClient().get(key);
        return data ? JSON.parse(data) : null;
    }
    async del(key) {
        await this.getClient().del(key);
    }
    async exists(key) {
        const result = await this.getClient().exists(key);
        return result === 1;
    }
    async expire(key, ttl) {
        await this.getClient().expire(key, ttl);
    }
    async publish(channel, message) {
        await this.getPublisher().publish(channel, JSON.stringify(message));
    }
    async subscribe(channel, callback) {
        await this.getSubscriber().subscribe(channel);
        this.getSubscriber().on('message', (receivedChannel, message) => {
            if (receivedChannel === channel) {
                try {
                    const parsed = JSON.parse(message);
                    callback(parsed);
                }
                catch (error) {
                    this.logger.error('Failed to parse pub/sub message', error);
                }
            }
        });
    }
    async unsubscribe(channel) {
        await this.getSubscriber().unsubscribe(channel);
    }
    async checkRateLimit(key, limit, windowMs) {
        const now = Date.now();
        const window = Math.floor(now / windowMs);
        const windowKey = `rate_limit:${key}:${window}`;
        const client = this.getClient();
        const current = await client.incr(windowKey);
        if (current === 1) {
            await client.expire(windowKey, Math.ceil(windowMs / 1000));
        }
        const allowed = current <= limit;
        const remaining = Math.max(0, limit - current);
        const resetTime = (window + 1) * windowMs;
        return { allowed, remaining, resetTime };
    }
    async healthCheck() {
        try {
            const result = await this.getClient().ping();
            return result === 'PONG';
        }
        catch (error) {
            this.logger.error('Redis health check failed', error);
            return false;
        }
    }
    async getInfo() {
        return await this.getClient().info();
    }
    async flushAll() {
        await this.getClient().flushall();
    }
};
exports.RedisService = RedisService;
exports.RedisService = RedisService = RedisService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('REDIS_OPTIONS')),
    __metadata("design:paramtypes", [typeof (_a = typeof redis_module_1.RedisModuleOptions !== "undefined" && redis_module_1.RedisModuleOptions) === "function" ? _a : Object])
], RedisService);


/***/ }),

/***/ "./src/modules/session/controllers/session.controller.ts":
/*!***************************************************************!*\
  !*** ./src/modules/session/controllers/session.controller.ts ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SessionController_1;
var _a, _b, _c, _d, _e, _f, _g, _h, _j;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SessionController = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const swagger_1 = __webpack_require__(/*! @nestjs/swagger */ "@nestjs/swagger");
const express_1 = __webpack_require__(/*! express */ "express");
const session_service_1 = __webpack_require__(/*! ../services/session.service */ "./src/modules/session/services/session.service.ts");
const session_analytics_service_1 = __webpack_require__(/*! ../services/session-analytics.service */ "./src/modules/session/services/session-analytics.service.ts");
const jwt_auth_guard_1 = __webpack_require__(/*! ../../auth/guards/jwt-auth.guard */ "./src/modules/auth/guards/jwt-auth.guard.ts");
const roles_decorator_1 = __webpack_require__(/*! ../../auth/decorators/roles.decorator */ "./src/modules/auth/decorators/roles.decorator.ts");
const public_decorator_1 = __webpack_require__(/*! ../../auth/decorators/public.decorator */ "./src/modules/auth/decorators/public.decorator.ts");
let SessionController = SessionController_1 = class SessionController {
    sessionService;
    sessionAnalyticsService;
    logger = new common_1.Logger(SessionController_1.name);
    constructor(sessionService, sessionAnalyticsService) {
        this.sessionService = sessionService;
        this.sessionAnalyticsService = sessionAnalyticsService;
    }
    async createSession(req, body) {
        try {
            const user = req['user'];
            if (!user || !user.sub || !user.organizationId) {
                throw new common_1.HttpException('User context not found', common_1.HttpStatus.UNAUTHORIZED);
            }
            const session = await this.sessionService.createSession(user.sub, user.organizationId, body.agentId, body.metadata);
            await this.sessionAnalyticsService.trackSessionEvent(session.id, 'session.created', { agentId: body.agentId });
            return session;
        }
        catch (error) {
            this.logger.error('Error creating session', error);
            throw new common_1.HttpException(error instanceof Error ? error.message : 'Failed to create session', error instanceof common_1.HttpException ? error.getStatus() : common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getSession(id, req) {
        try {
            const session = await this.sessionService.getSession(id);
            if (!session) {
                throw new common_1.HttpException('Session not found', common_1.HttpStatus.NOT_FOUND);
            }
            const user = req['user'];
            if (session.organizationId !== user.organizationId) {
                throw new common_1.HttpException('Access denied', common_1.HttpStatus.FORBIDDEN);
            }
            return session;
        }
        catch (error) {
            this.logger.error(`Error retrieving session: ${id}`, error);
            throw new common_1.HttpException(error instanceof Error ? error.message : 'Failed to retrieve session', error instanceof common_1.HttpException ? error.getStatus() : common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async addMessage(id, message, req) {
        try {
            const session = await this.sessionService.getSession(id);
            if (!session) {
                throw new common_1.HttpException('Session not found', common_1.HttpStatus.NOT_FOUND);
            }
            const user = req['user'];
            if (session.organizationId !== user.organizationId) {
                throw new common_1.HttpException('Access denied', common_1.HttpStatus.FORBIDDEN);
            }
            const updatedSession = await this.sessionService.addMessage(id, message);
            await this.sessionAnalyticsService.trackSessionEvent(id, 'message.added', { role: message.role });
            return updatedSession;
        }
        catch (error) {
            this.logger.error(`Error adding message to session: ${id}`, error);
            throw new common_1.HttpException(error instanceof Error ? error.message : 'Failed to add message', error instanceof common_1.HttpException ? error.getStatus() : common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getUserSessions(req, limit = 10, offset = 0) {
        try {
            const user = req['user'];
            if (!user || !user.sub) {
                throw new common_1.HttpException('User context not found', common_1.HttpStatus.UNAUTHORIZED);
            }
            const sessions = await this.sessionService.getUserSessions(user.sub);
            return sessions;
        }
        catch (error) {
            this.logger.error('Error retrieving user sessions', error);
            throw new common_1.HttpException(error instanceof Error ? error.message : 'Failed to retrieve sessions', error instanceof common_1.HttpException ? error.getStatus() : common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async deleteSession(id, req) {
        try {
            const session = await this.sessionService.getSession(id);
            if (!session) {
                throw new common_1.HttpException('Session not found', common_1.HttpStatus.NOT_FOUND);
            }
            const user = req['user'];
            if (session.organizationId !== user.organizationId) {
                throw new common_1.HttpException('Access denied', common_1.HttpStatus.FORBIDDEN);
            }
            await this.sessionService.deleteSession(id);
            await this.sessionAnalyticsService.trackSessionEvent(id, 'session.deleted');
            return { success: true, message: 'Session deleted successfully' };
        }
        catch (error) {
            this.logger.error(`Error deleting session: ${id}`, error);
            throw new common_1.HttpException(error instanceof Error ? error.message : 'Failed to delete session', error instanceof common_1.HttpException ? error.getStatus() : common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async getSessionMetrics(req, days = 7) {
        try {
            const user = req['user'];
            if (!user || !user.organizationId) {
                throw new common_1.HttpException('User context not found', common_1.HttpStatus.UNAUTHORIZED);
            }
            const metrics = await this.sessionAnalyticsService.getSessionMetrics(user.organizationId, days);
            return metrics;
        }
        catch (error) {
            this.logger.error('Error retrieving session metrics', error);
            throw new common_1.HttpException(error instanceof Error ? error.message : 'Failed to retrieve metrics', error instanceof common_1.HttpException ? error.getStatus() : common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async extendSession(id, seconds, req) {
        try {
            const session = await this.sessionService.getSession(id);
            if (!session) {
                throw new common_1.HttpException('Session not found', common_1.HttpStatus.NOT_FOUND);
            }
            const user = req['user'];
            if (session.organizationId !== user.organizationId) {
                throw new common_1.HttpException('Access denied', common_1.HttpStatus.FORBIDDEN);
            }
            const updatedSession = await this.sessionService.extendSession(id, seconds);
            await this.sessionAnalyticsService.trackSessionEvent(id, 'session.extended', { seconds });
            return updatedSession;
        }
        catch (error) {
            this.logger.error(`Error extending session: ${id}`, error);
            throw new common_1.HttpException(error instanceof Error ? error.message : 'Failed to extend session', error instanceof common_1.HttpException ? error.getStatus() : common_1.HttpStatus.INTERNAL_SERVER_ERROR);
        }
    }
    async healthCheck() {
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
        };
    }
};
exports.SessionController = SessionController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new session' }),
    (0, swagger_1.ApiBody)({ type: Object, description: 'Session creation parameters' }),
    (0, swagger_1.ApiResponse)({ status: 201, description: 'Session created successfully' }),
    (0, swagger_1.ApiResponse)({ status: 400, description: 'Invalid request' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_c = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _c : Object, Object]),
    __metadata("design:returntype", Promise)
], SessionController.prototype, "createSession", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get session by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Session ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Session retrieved successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Session not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_d = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _d : Object]),
    __metadata("design:returntype", Promise)
], SessionController.prototype, "getSession", null);
__decorate([
    (0, common_1.Post)(':id/messages'),
    (0, swagger_1.ApiOperation)({ summary: 'Add message to session' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Session ID' }),
    (0, swagger_1.ApiBody)({ type: Object, description: 'Message to add' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Message added successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Session not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Object, typeof (_e = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _e : Object]),
    __metadata("design:returntype", Promise)
], SessionController.prototype, "addMessage", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get user sessions' }),
    (0, swagger_1.ApiQuery)({ name: 'limit', required: false, description: 'Maximum number of sessions to return' }),
    (0, swagger_1.ApiQuery)({ name: 'offset', required: false, description: 'Number of sessions to skip' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Sessions retrieved successfully' }),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('offset')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_f = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _f : Object, Number, Number]),
    __metadata("design:returntype", Promise)
], SessionController.prototype, "getUserSessions", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete session' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Session ID' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Session deleted successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Session not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, typeof (_g = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _g : Object]),
    __metadata("design:returntype", Promise)
], SessionController.prototype, "deleteSession", null);
__decorate([
    (0, common_1.Get)('metrics/organization'),
    (0, swagger_1.ApiOperation)({ summary: 'Get session metrics for organization' }),
    (0, swagger_1.ApiQuery)({ name: 'days', required: false, description: 'Number of days to include in metrics' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Metrics retrieved successfully' }),
    (0, roles_decorator_1.Roles)('ORG_ADMIN', 'SUPER_ADMIN'),
    __param(0, (0, common_1.Req)()),
    __param(1, (0, common_1.Query)('days')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_h = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _h : Object, Number]),
    __metadata("design:returntype", Promise)
], SessionController.prototype, "getSessionMetrics", null);
__decorate([
    (0, common_1.Post)(':id/extend'),
    (0, swagger_1.ApiOperation)({ summary: 'Extend session expiration' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Session ID' }),
    (0, swagger_1.ApiQuery)({ name: 'seconds', required: false, description: 'Seconds to extend session by' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Session extended successfully' }),
    (0, swagger_1.ApiResponse)({ status: 404, description: 'Session not found' }),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('seconds')),
    __param(2, (0, common_1.Req)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, typeof (_j = typeof express_1.Request !== "undefined" && express_1.Request) === "function" ? _j : Object]),
    __metadata("design:returntype", Promise)
], SessionController.prototype, "extendSession", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, public_decorator_1.Public)(),
    (0, swagger_1.ApiOperation)({ summary: 'Health check for session service' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Service is healthy' }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SessionController.prototype, "healthCheck", null);
exports.SessionController = SessionController = SessionController_1 = __decorate([
    (0, swagger_1.ApiTags)('Sessions'),
    (0, swagger_1.ApiBearerAuth)(),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, common_1.Controller)('api/v1/sessions'),
    __metadata("design:paramtypes", [typeof (_a = typeof session_service_1.SessionService !== "undefined" && session_service_1.SessionService) === "function" ? _a : Object, typeof (_b = typeof session_analytics_service_1.SessionAnalyticsService !== "undefined" && session_analytics_service_1.SessionAnalyticsService) === "function" ? _b : Object])
], SessionController);


/***/ }),

/***/ "./src/modules/session/services/session-analytics.service.ts":
/*!*******************************************************************!*\
  !*** ./src/modules/session/services/session-analytics.service.ts ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SessionAnalyticsService_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SessionAnalyticsService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const prisma_service_1 = __webpack_require__(/*! ../../prisma/prisma.service */ "./src/modules/prisma/prisma.service.ts");
let SessionAnalyticsService = SessionAnalyticsService_1 = class SessionAnalyticsService {
    prismaService;
    logger = new common_1.Logger(SessionAnalyticsService_1.name);
    constructor(prismaService) {
        this.prismaService = prismaService;
    }
    async getActiveSessionCount(organizationId) {
        return this.prismaService.session.count({
            where: {
                organizationId,
                status: 'ACTIVE',
            },
        });
    }
    async getSessionMetrics(organizationId, days = 7) {
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);
        const sessionsByDay = await this.prismaService.session.groupBy({
            by: ['createdAt'],
            _count: {
                id: true,
            },
            where: {
                organizationId,
                createdAt: {
                    gte: startDate,
                },
            },
            orderBy: {
                createdAt: 'asc',
            },
        });
        const sessionsByStatus = await this.prismaService.session.groupBy({
            by: ['status'],
            _count: {
                id: true,
            },
            where: {
                organizationId,
                createdAt: {
                    gte: startDate,
                },
            },
        });
        const avgDuration = await this.prismaService.$queryRaw `
      SELECT AVG(EXTRACT(EPOCH FROM ("updatedAt" - "createdAt"))) as avg_duration
      FROM "Session"
      WHERE "organizationId" = ${organizationId}
      AND "createdAt" >= ${startDate}
    `;
        const userEngagement = await this.prismaService.$queryRaw `
      SELECT 
        "userId", 
        COUNT(*) as session_count,
        AVG(EXTRACT(EPOCH FROM ("updatedAt" - "createdAt"))) as avg_duration
      FROM "Session"
      WHERE "organizationId" = ${organizationId}
      AND "createdAt" >= ${startDate}
      GROUP BY "userId"
      ORDER BY COUNT(*) DESC
      LIMIT 10
    `;
        return {
            sessionsByDay,
            sessionsByStatus,
            avgDuration,
            userEngagement,
            totalSessions: await this.prismaService.session.count({
                where: {
                    organizationId,
                    createdAt: {
                        gte: startDate,
                    },
                },
            }),
            activeSessions: await this.getActiveSessionCount(organizationId),
        };
    }
    async trackSessionEvent(sessionId, eventType, metadata = {}) {
        try {
            const session = await this.prismaService.session.findUnique({
                where: { id: sessionId },
                select: {
                    id: true,
                    userId: true,
                    organizationId: true,
                },
            });
            if (!session) {
                this.logger.warn(`Attempted to track event for non-existent session: ${sessionId}`);
                return;
            }
            this.logger.log(`Session event: ${eventType}`, {
                sessionId,
                userId: session.userId,
                organizationId: session.organizationId,
                eventType,
                metadata,
                timestamp: new Date(),
            });
        }
        catch (error) {
            this.logger.error(`Error tracking session event: ${eventType}`, error);
        }
    }
    async getSessionUsageReport(organizationId, startDate, endDate) {
        const totalSessions = await this.prismaService.session.count({
            where: {
                organizationId,
                createdAt: {
                    gte: startDate,
                    lte: endDate,
                },
            },
        });
        const totalDurationResult = await this.prismaService.$queryRaw `
      SELECT SUM(EXTRACT(EPOCH FROM ("updatedAt" - "createdAt"))) as total_duration
      FROM "Session"
      WHERE "organizationId" = ${organizationId}
      AND "createdAt" >= ${startDate}
      AND "createdAt" <= ${endDate}
      AND "status" != 'ACTIVE'
    `;
        const sessionsByAgent = await this.prismaService.$queryRaw `
      SELECT 
        "agentId", 
        COUNT(*) as count
      FROM "Session"
      WHERE "organizationId" = ${organizationId}
      AND "createdAt" >= ${startDate}
      AND "createdAt" <= ${endDate}
      AND "agentId" IS NOT NULL
      GROUP BY "agentId"
    `;
        const sessionsByDay = await this.prismaService.$queryRaw `
      SELECT 
        DATE_TRUNC('day', "createdAt") as day,
        COUNT(*) as count
      FROM "Session"
      WHERE "organizationId" = ${organizationId}
      AND "createdAt" >= ${startDate}
      AND "createdAt" <= ${endDate}
      GROUP BY DATE_TRUNC('day', "createdAt")
      ORDER BY day ASC
    `;
        return {
            organizationId,
            period: {
                start: startDate,
                end: endDate,
            },
            totalSessions,
            totalDuration: totalDurationResult,
            sessionsByAgent,
            sessionsByDay,
        };
    }
};
exports.SessionAnalyticsService = SessionAnalyticsService;
exports.SessionAnalyticsService = SessionAnalyticsService = SessionAnalyticsService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof prisma_service_1.PrismaService !== "undefined" && prisma_service_1.PrismaService) === "function" ? _a : Object])
], SessionAnalyticsService);


/***/ }),

/***/ "./src/modules/session/services/session-cleanup.service.ts":
/*!*****************************************************************!*\
  !*** ./src/modules/session/services/session-cleanup.service.ts ***!
  \*****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SessionCleanupService_1;
var _a, _b, _c;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SessionCleanupService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const schedule_1 = __webpack_require__(/*! @nestjs/schedule */ "@nestjs/schedule");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const prisma_service_1 = __webpack_require__(/*! ../../prisma/prisma.service */ "./src/modules/prisma/prisma.service.ts");
const redis_service_1 = __webpack_require__(/*! ../../redis/redis.service */ "./src/modules/redis/redis.service.ts");
const client_1 = __webpack_require__(/*! .prisma/client */ ".prisma/client");
let SessionCleanupService = SessionCleanupService_1 = class SessionCleanupService {
    prismaService;
    redisService;
    configService;
    logger = new common_1.Logger(SessionCleanupService_1.name);
    SESSION_PREFIX = 'session:';
    constructor(prismaService, redisService, configService) {
        this.prismaService = prismaService;
        this.redisService = redisService;
        this.configService = configService;
    }
    async cleanupExpiredSessions() {
        this.logger.log('Running expired session cleanup');
        try {
            const expiredSessions = await this.prismaService.session.findMany({
                where: {
                    status: client_1.SessionStatus.ACTIVE,
                    expiresAt: {
                        lt: new Date(),
                    },
                },
                select: {
                    id: true,
                },
            });
            if (expiredSessions.length === 0) {
                this.logger.log('No expired sessions found');
                return;
            }
            this.logger.log(`Found ${expiredSessions.length} expired sessions to clean up`);
            await this.prismaService.session.updateMany({
                where: {
                    id: {
                        in: expiredSessions.map(s => s.id),
                    },
                },
                data: {
                    status: client_1.SessionStatus.EXPIRED,
                },
            });
            const redisKeys = expiredSessions.map(s => `${this.SESSION_PREFIX}${s.id}`);
            for (const key of redisKeys) {
                await this.redisService.del(key);
            }
            this.logger.log(`Cleaned up ${expiredSessions.length} expired sessions`);
        }
        catch (error) {
            this.logger.error('Error cleaning up expired sessions', error);
        }
    }
    async cleanupOrphanedSessions() {
        this.logger.log('Running orphaned session cleanup');
        try {
            const activeSessions = await this.prismaService.session.findMany({
                where: {
                    status: client_1.SessionStatus.ACTIVE,
                },
                select: {
                    id: true,
                },
            });
            let orphanedCount = 0;
            for (const session of activeSessions) {
                const exists = await this.redisService.exists(`${this.SESSION_PREFIX}${session.id}`);
                if (!exists) {
                    await this.prismaService.session.update({
                        where: {
                            id: session.id,
                        },
                        data: {
                            status: client_1.SessionStatus.EXPIRED,
                        },
                    });
                    orphanedCount++;
                }
            }
            if (orphanedCount > 0) {
                this.logger.log(`Cleaned up ${orphanedCount} orphaned sessions`);
            }
            else {
                this.logger.log('No orphaned sessions found');
            }
        }
        catch (error) {
            this.logger.error('Error cleaning up orphaned sessions', error);
        }
    }
    async generateSessionAnalytics() {
        this.logger.log('Generating session analytics');
        try {
            const oneWeekAgo = new Date();
            oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
            const sessionsByStatus = await this.prismaService.session.groupBy({
                by: ['status'],
                _count: {
                    id: true,
                },
                where: {
                    createdAt: {
                        gte: oneWeekAgo,
                    },
                },
            });
            const sessionsByOrg = await this.prismaService.session.groupBy({
                by: ['organizationId'],
                _count: {
                    id: true,
                },
                where: {
                    createdAt: {
                        gte: oneWeekAgo,
                    },
                },
            });
            const avgDurationResult = await this.prismaService.$queryRaw `
        SELECT AVG(EXTRACT(EPOCH FROM ("updatedAt" - "createdAt"))) as avg_duration
        FROM sessions
        WHERE "createdAt" >= ${oneWeekAgo}
        AND "status" != 'ACTIVE'
      `;
            this.logger.log('Session analytics generated', {
                sessionsByStatus,
                sessionsByOrg,
                avgDuration: avgDurationResult,
            });
        }
        catch (error) {
            this.logger.error('Error generating session analytics', error);
        }
    }
};
exports.SessionCleanupService = SessionCleanupService;
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_HOUR),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SessionCleanupService.prototype, "cleanupExpiredSessions", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_DAY_AT_MIDNIGHT),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SessionCleanupService.prototype, "cleanupOrphanedSessions", null);
__decorate([
    (0, schedule_1.Cron)(schedule_1.CronExpression.EVERY_WEEK),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], SessionCleanupService.prototype, "generateSessionAnalytics", null);
exports.SessionCleanupService = SessionCleanupService = SessionCleanupService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof prisma_service_1.PrismaService !== "undefined" && prisma_service_1.PrismaService) === "function" ? _a : Object, typeof (_b = typeof redis_service_1.RedisService !== "undefined" && redis_service_1.RedisService) === "function" ? _b : Object, typeof (_c = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _c : Object])
], SessionCleanupService);


/***/ }),

/***/ "./src/modules/session/services/session-memory.service.ts":
/*!****************************************************************!*\
  !*** ./src/modules/session/services/session-memory.service.ts ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SessionMemoryService_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SessionMemoryService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
let SessionMemoryService = SessionMemoryService_1 = class SessionMemoryService {
    configService;
    logger = new common_1.Logger(SessionMemoryService_1.name);
    TOKENS_PER_CHAR = 0.25;
    SYSTEM_MESSAGES_WEIGHT = 2;
    RECENT_MESSAGES_COUNT = 4;
    constructor(configService) {
        this.configService = configService;
    }
    estimateTokens(content) {
        if (!content)
            return 0;
        return Math.ceil(content.length * this.TOKENS_PER_CHAR);
    }
    truncateMemory(memory) {
        const { messages, maxTokens } = memory;
        if (!messages.length)
            return memory;
        if (memory.totalTokens <= (maxTokens || 4000))
            return memory;
        this.logger.log(`Truncating memory from ${memory.totalTokens} tokens to fit ${maxTokens || 4000} tokens`);
        const systemMessages = messages.filter(m => m.role === 'system');
        const nonSystemMessages = messages.filter(m => m.role !== 'system');
        const recentMessages = nonSystemMessages.slice(-this.RECENT_MESSAGES_COUNT);
        const systemTokens = systemMessages.reduce((sum, m) => sum + (m.tokens || 0), 0);
        const recentTokens = recentMessages.reduce((sum, m) => sum + (m.tokens || 0), 0);
        const preservedTokens = systemTokens + recentTokens;
        if (preservedTokens > (maxTokens || 4000)) {
            const sortedSystemMessages = [...systemMessages].sort((a, b) => (b.metadata?.importance || 0) - (a.metadata?.importance || 0));
            const truncatedMemory = this.fitMessagesInTokenLimit([...sortedSystemMessages, ...recentMessages.slice(-2)], maxTokens || 4000);
            return {
                ...memory,
                messages: truncatedMemory,
                totalTokens: truncatedMemory.reduce((sum, m) => sum + (m.tokens || 0), 0),
            };
        }
        const remainingTokens = (maxTokens || 4000) - preservedTokens;
        const oldMessages = nonSystemMessages.slice(0, -this.RECENT_MESSAGES_COUNT);
        const scoredMessages = oldMessages.map((message, index) => {
            const recencyScore = index / oldMessages.length;
            const importanceScore = message.metadata?.importance || 0.5;
            return {
                message,
                score: (recencyScore * 0.7) + (importanceScore * 0.3),
            };
        });
        scoredMessages.sort((a, b) => b.score - a.score);
        let currentTokens = 0;
        const additionalMessages = [];
        for (const { message } of scoredMessages) {
            const messageTokens = message.tokens || 0;
            if (currentTokens + messageTokens <= remainingTokens) {
                additionalMessages.push(message);
                currentTokens += messageTokens;
            }
        }
        const allMessages = [...systemMessages, ...additionalMessages, ...recentMessages]
            .sort((a, b) => (a.timestamp?.getTime() || 0) - (b.timestamp?.getTime() || 0));
        return {
            ...memory,
            messages: allMessages,
            totalTokens: preservedTokens + currentTokens,
        };
    }
    fitMessagesInTokenLimit(messages, tokenLimit) {
        let totalTokens = 0;
        const result = [];
        if (!messages)
            return [];
        for (let i = messages.length - 1; i >= 0; i--) {
            const message = messages[i];
            if (!message)
                continue;
            const messageTokens = message.tokens || 0;
            if (message.role === 'system' && totalTokens + messageTokens <= tokenLimit) {
                result.unshift(message);
                totalTokens += messageTokens;
            }
            else if (message.role !== 'system' && totalTokens + messageTokens <= tokenLimit) {
                result.unshift(message);
                totalTokens += messageTokens;
            }
            if (totalTokens >= tokenLimit)
                break;
        }
        if (!result)
            return [];
        return result;
    }
    summarizeMemory(memory) {
        if (!memory)
            return '';
        const messageCount = memory.messages.length;
        const userMessages = memory.messages.filter(m => m.role === 'user').length;
        const assistantMessages = memory.messages.filter(m => m.role === 'assistant').length;
        const systemMessages = memory.messages.filter(m => m.role === 'system').length;
        const toolMessages = memory.messages.filter(m => m.role === 'tool').length;
        return `Conversation with ${userMessages} user messages, ${assistantMessages} assistant responses, ${systemMessages} system messages, and ${toolMessages} tool calls. Total tokens: ${memory.totalTokens}.`;
    }
};
exports.SessionMemoryService = SessionMemoryService;
exports.SessionMemoryService = SessionMemoryService = SessionMemoryService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], SessionMemoryService);


/***/ }),

/***/ "./src/modules/session/services/session.service.ts":
/*!*********************************************************!*\
  !*** ./src/modules/session/services/session.service.ts ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var SessionService_1;
var _a, _b;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SessionService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const prisma_service_1 = __webpack_require__(/*! ../../prisma/prisma.service */ "./src/modules/prisma/prisma.service.ts");
const redis_service_1 = __webpack_require__(/*! ../../redis/redis.service */ "./src/modules/redis/redis.service.ts");
let SessionService = SessionService_1 = class SessionService {
    prismaService;
    redisService;
    logger = new common_1.Logger(SessionService_1.name);
    constructor(prismaService, redisService) {
        this.prismaService = prismaService;
        this.redisService = redisService;
    }
    async createSession(userId, organizationId, agentId, metadata = {}) {
        try {
            const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
            const session = await this.prismaService.session.create({
                data: {
                    sessionId,
                    userId,
                    organizationId,
                    metadata: {
                        ...metadata,
                        agentId,
                    },
                    status: 'ACTIVE',
                    expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
                },
            });
            return session;
        }
        catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.logger.error(`Failed to create session: ${err.message}`, err.stack);
            throw error;
        }
    }
    async getSession(sessionId, userId, organizationId) {
        try {
            const whereClause = { sessionId };
            if (userId)
                whereClause.userId = userId;
            if (organizationId)
                whereClause.organizationId = organizationId;
            const session = await this.prismaService.session.findFirst({
                where: whereClause,
            });
            if (!session) {
                throw new common_1.NotFoundException('Session not found');
            }
            return session;
        }
        catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.logger.error(`Failed to get session: ${err.message}`, err.stack);
            throw error;
        }
    }
    async updateSession(sessionId, userId, organizationId, data) {
        try {
            const session = await this.prismaService.session.update({
                where: { sessionId },
                data,
            });
            return session;
        }
        catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.logger.error(`Failed to update session: ${err.message}`, err.stack);
            throw error;
        }
    }
    async deleteSession(sessionId, userId, organizationId) {
        try {
            await this.prismaService.session.delete({
                where: { sessionId },
            });
            this.logger.log(`Session deleted: ${sessionId}`);
        }
        catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.logger.error(`Failed to delete session: ${err.message}`, err.stack);
            throw error;
        }
    }
    async addMessage(sessionId, message) {
        try {
            const updatedSession = await this.prismaService.session.update({
                where: { sessionId },
                data: {
                    metadata: message,
                },
            });
            return updatedSession;
        }
        catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.logger.error(`Failed to add message: ${err.message}`, err.stack);
            throw error;
        }
    }
    async getUserSessions(userId) {
        try {
            const sessions = await this.prismaService.session.findMany({
                where: { userId },
                orderBy: { createdAt: 'desc' },
            });
            return sessions;
        }
        catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.logger.error(`Failed to get user sessions: ${err.message}`, err.stack);
            throw error;
        }
    }
    async extendSession(sessionId, seconds) {
        try {
            const session = await this.prismaService.session.update({
                where: { sessionId },
                data: {
                    expiresAt: new Date(Date.now() + seconds * 1000),
                },
            });
            return session;
        }
        catch (error) {
            const err = error instanceof Error ? error : new Error(String(error));
            this.logger.error(`Failed to extend session: ${err.message}`, err.stack);
            throw error;
        }
    }
};
exports.SessionService = SessionService;
exports.SessionService = SessionService = SessionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof prisma_service_1.PrismaService !== "undefined" && prisma_service_1.PrismaService) === "function" ? _a : Object, typeof (_b = typeof redis_service_1.RedisService !== "undefined" && redis_service_1.RedisService) === "function" ? _b : Object])
], SessionService);


/***/ }),

/***/ "./src/modules/session/session.module.ts":
/*!***********************************************!*\
  !*** ./src/modules/session/session.module.ts ***!
  \***********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.SessionModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const redis_module_1 = __webpack_require__(/*! ../redis/redis.module */ "./src/modules/redis/redis.module.ts");
const prisma_module_1 = __webpack_require__(/*! ../prisma/prisma.module */ "./src/modules/prisma/prisma.module.ts");
const session_service_1 = __webpack_require__(/*! ./services/session.service */ "./src/modules/session/services/session.service.ts");
const session_controller_1 = __webpack_require__(/*! ./controllers/session.controller */ "./src/modules/session/controllers/session.controller.ts");
const session_memory_service_1 = __webpack_require__(/*! ./services/session-memory.service */ "./src/modules/session/services/session-memory.service.ts");
const session_cleanup_service_1 = __webpack_require__(/*! ./services/session-cleanup.service */ "./src/modules/session/services/session-cleanup.service.ts");
const session_analytics_service_1 = __webpack_require__(/*! ./services/session-analytics.service */ "./src/modules/session/services/session-analytics.service.ts");
let SessionModule = class SessionModule {
};
exports.SessionModule = SessionModule;
exports.SessionModule = SessionModule = __decorate([
    (0, common_1.Module)({
        imports: [
            redis_module_1.RedisModule,
            prisma_module_1.PrismaModule,
        ],
        controllers: [session_controller_1.SessionController],
        providers: [
            session_service_1.SessionService,
            session_memory_service_1.SessionMemoryService,
            session_cleanup_service_1.SessionCleanupService,
            session_analytics_service_1.SessionAnalyticsService,
        ],
        exports: [
            session_service_1.SessionService,
            session_memory_service_1.SessionMemoryService,
        ],
    })
], SessionModule);


/***/ }),

/***/ "./src/modules/vector-database/controllers/vector-database.controller.ts":
/*!*******************************************************************************!*\
  !*** ./src/modules/vector-database/controllers/vector-database.controller.ts ***!
  \*******************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var VectorDatabaseController_1;
var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.VectorDatabaseController = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const swagger_1 = __webpack_require__(/*! @nestjs/swagger */ "@nestjs/swagger");
const vector_database_service_1 = __webpack_require__(/*! ../services/vector-database.service */ "./src/modules/vector-database/services/vector-database.service.ts");
const vector_database_types_1 = __webpack_require__(/*! @shared/types/vector-database.types */ "../shared/src/types/vector-database.types.ts");
let VectorDatabaseController = VectorDatabaseController_1 = class VectorDatabaseController {
    vectorDatabaseService;
    logger = new common_1.Logger(VectorDatabaseController_1.name);
    constructor(vectorDatabaseService) {
        this.vectorDatabaseService = vectorDatabaseService;
    }
    async search(request) {
        this.logger.log(`Vector search request: ${JSON.stringify(request, null, 2)}`);
        return this.vectorDatabaseService.search(request);
    }
    async upsert(request) {
        this.logger.log(`Vector upsert request for ${request.vectors.length} vectors`);
        return this.vectorDatabaseService.upsert(request);
    }
    async deleteVectors(request) {
        this.logger.log(`Vector delete request: ${JSON.stringify(request, null, 2)}`);
        return this.vectorDatabaseService.delete(request);
    }
    async getIndexStats(namespace) {
        this.logger.log(`Getting index stats for namespace: ${namespace || 'default'}`);
        return this.vectorDatabaseService.getIndexStats(namespace);
    }
    async batch(request) {
        this.logger.log(`Batch request with ${request.operations.length} operations`);
        return this.vectorDatabaseService.batch(request);
    }
    async similaritySearch(request) {
        this.logger.log(`Similarity search: ${request.query.substring(0, 100)}...`);
        return this.vectorDatabaseService.similaritySearch(request);
    }
    async addKnowledgeDocument(body) {
        this.logger.log(`Adding knowledge document: ${body.document.title}`);
        return this.vectorDatabaseService.addKnowledgeDocument(body.document, body.namespace);
    }
    async searchKnowledge(body) {
        this.logger.log(`Knowledge search: ${body.query.substring(0, 100)}...`);
        return this.vectorDatabaseService.searchKnowledge(body.query, body.options);
    }
    async healthCheck() {
        this.logger.log('Vector database health check requested');
        return this.vectorDatabaseService.healthCheck();
    }
    async deleteNamespace(namespace) {
        this.logger.log(`Deleting namespace: ${namespace}`);
        return this.vectorDatabaseService.delete({
            deleteAll: true,
            namespace,
        });
    }
    async listNamespaces() {
        this.logger.log('Listing all namespaces');
        return this.vectorDatabaseService.getIndexStats();
    }
};
exports.VectorDatabaseController = VectorDatabaseController;
__decorate([
    (0, common_1.Post)('search'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Search for similar vectors',
        description: 'Perform vector similarity search using either a query string or vector array',
    }),
    (0, swagger_1.ApiBody)({
        type: 'object',
        description: 'Vector search request',
        examples: {
            textQuery: {
                summary: 'Search with text query',
                value: {
                    query: 'machine learning algorithms',
                    topK: 10,
                    includeMetadata: true,
                    namespace: 'knowledge-base',
                },
            },
            vectorQuery: {
                summary: 'Search with vector',
                value: {
                    vector: [0.1, 0.2, 0.3],
                    topK: 5,
                    filter: { category: 'technology' },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Search results returned successfully',
        type: 'object',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid search request',
    }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_b = typeof vector_database_types_1.VectorSearchRequest !== "undefined" && vector_database_types_1.VectorSearchRequest) === "function" ? _b : Object]),
    __metadata("design:returntype", typeof (_c = typeof Promise !== "undefined" && Promise) === "function" ? _c : Object)
], VectorDatabaseController.prototype, "search", null);
__decorate([
    (0, common_1.Post)('upsert'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({
        summary: 'Upsert vectors',
        description: 'Insert or update vectors in the database',
    }),
    (0, swagger_1.ApiBody)({
        type: 'object',
        description: 'Vector upsert request',
        examples: {
            withEmbeddings: {
                summary: 'Upsert with pre-computed embeddings',
                value: {
                    vectors: [
                        {
                            id: 'doc_1',
                            values: [0.1, 0.2, 0.3],
                            metadata: { title: 'Document 1', category: 'AI' },
                        },
                    ],
                    namespace: 'documents',
                },
            },
            withText: {
                summary: 'Upsert with text (auto-embedding)',
                value: {
                    vectors: [
                        {
                            id: 'doc_2',
                            metadata: {
                                text: 'This is a document about machine learning',
                                title: 'ML Guide',
                                category: 'AI',
                            },
                        },
                    ],
                    embeddingModel: 'text-embedding-ada-002',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Vectors upserted successfully',
        type: 'object',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid upsert request',
    }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_d = typeof vector_database_types_1.VectorUpsertRequest !== "undefined" && vector_database_types_1.VectorUpsertRequest) === "function" ? _d : Object]),
    __metadata("design:returntype", typeof (_e = typeof Promise !== "undefined" && Promise) === "function" ? _e : Object)
], VectorDatabaseController.prototype, "upsert", null);
__decorate([
    (0, common_1.Delete)('vectors'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete vectors',
        description: 'Delete vectors by IDs or delete all vectors in a namespace',
    }),
    (0, swagger_1.ApiBody)({
        type: 'object',
        description: 'Vector delete request',
        examples: {
            deleteByIds: {
                summary: 'Delete specific vectors',
                value: {
                    ids: ['doc_1', 'doc_2'],
                    namespace: 'documents',
                },
            },
            deleteAll: {
                summary: 'Delete all vectors in namespace',
                value: {
                    deleteAll: true,
                    namespace: 'temp-documents',
                },
            },
            deleteByFilter: {
                summary: 'Delete vectors matching filter',
                value: {
                    filter: { category: 'outdated' },
                    namespace: 'documents',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Vectors deleted successfully',
        type: 'object',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.BAD_REQUEST,
        description: 'Invalid delete request',
    }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_f = typeof vector_database_types_1.VectorDeleteRequest !== "undefined" && vector_database_types_1.VectorDeleteRequest) === "function" ? _f : Object]),
    __metadata("design:returntype", typeof (_g = typeof Promise !== "undefined" && Promise) === "function" ? _g : Object)
], VectorDatabaseController.prototype, "deleteVectors", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({
        summary: 'Get index statistics',
        description: 'Retrieve statistics about the vector index',
    }),
    (0, swagger_1.ApiQuery)({
        name: 'namespace',
        required: false,
        description: 'Namespace to get stats for',
        type: String,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Index statistics retrieved successfully',
        type: 'object',
    }),
    __param(0, (0, common_1.Query)('namespace')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_h = typeof Promise !== "undefined" && Promise) === "function" ? _h : Object)
], VectorDatabaseController.prototype, "getIndexStats", null);
__decorate([
    (0, common_1.Post)('batch'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Batch operations',
        description: 'Execute multiple vector operations in a single request',
    }),
    (0, swagger_1.ApiBody)({
        type: 'object',
        description: 'Batch request with multiple operations',
        examples: {
            mixedOperations: {
                summary: 'Multiple operation types',
                value: {
                    operations: [
                        {
                            id: 'op_1',
                            type: 'upsert',
                            namespace: 'docs',
                            data: {
                                vectors: [
                                    {
                                        id: 'doc_1',
                                        metadata: { text: 'Sample document' },
                                    },
                                ],
                            },
                        },
                        {
                            id: 'op_2',
                            type: 'search',
                            namespace: 'docs',
                            data: {
                                query: 'sample query',
                                topK: 5,
                            },
                        },
                    ],
                    embeddingModel: 'text-embedding-ada-002',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Batch operations completed',
        type: 'object',
    }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_j = typeof vector_database_types_1.VectorBatchRequest !== "undefined" && vector_database_types_1.VectorBatchRequest) === "function" ? _j : Object]),
    __metadata("design:returntype", typeof (_k = typeof Promise !== "undefined" && Promise) === "function" ? _k : Object)
], VectorDatabaseController.prototype, "batch", null);
__decorate([
    (0, common_1.Post)('similarity-search'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Advanced similarity search',
        description: 'Perform similarity search with advanced filtering and ranking',
    }),
    (0, swagger_1.ApiBody)({
        type: 'object',
        description: 'Similarity search request',
        examples: {
            advancedSearch: {
                summary: 'Advanced similarity search',
                value: {
                    query: 'artificial intelligence machine learning',
                    topK: 10,
                    minScore: 0.7,
                    rerank: true,
                    filter: { category: 'AI', published: true },
                    namespace: 'knowledge-base',
                    includeValues: false,
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Similarity search results',
        type: 'object',
    }),
    (0, common_1.UsePipes)(new common_1.ValidationPipe({ transform: true })),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [typeof (_l = typeof vector_database_types_1.VectorSimilaritySearchRequest !== "undefined" && vector_database_types_1.VectorSimilaritySearchRequest) === "function" ? _l : Object]),
    __metadata("design:returntype", typeof (_m = typeof Promise !== "undefined" && Promise) === "function" ? _m : Object)
], VectorDatabaseController.prototype, "similaritySearch", null);
__decorate([
    (0, common_1.Post)('knowledge/documents'),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    (0, swagger_1.ApiOperation)({
        summary: 'Add knowledge document',
        description: 'Add a knowledge document to the vector database with automatic chunking',
    }),
    (0, swagger_1.ApiBody)({
        type: 'object',
        description: 'Knowledge document to add',
        examples: {
            document: {
                summary: 'Add knowledge document',
                value: {
                    document: {
                        id: 'kb_doc_1',
                        title: 'Introduction to Machine Learning',
                        content: 'Machine learning is a subset of artificial intelligence...',
                        source: 'https://example.com/ml-guide',
                        type: 'article',
                        metadata: {
                            author: 'AI Expert',
                            category: 'AI',
                            tags: ['machine-learning', 'AI', 'tutorial'],
                        },
                    },
                    namespace: 'knowledge-base',
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.CREATED,
        description: 'Knowledge document added successfully',
        type: 'object',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_o = typeof Promise !== "undefined" && Promise) === "function" ? _o : Object)
], VectorDatabaseController.prototype, "addKnowledgeDocument", null);
__decorate([
    (0, common_1.Post)('knowledge/search'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Search knowledge base',
        description: 'Search through knowledge documents with semantic understanding',
    }),
    (0, swagger_1.ApiBody)({
        type: 'object',
        description: 'Knowledge search request',
        examples: {
            knowledgeSearch: {
                summary: 'Search knowledge base',
                value: {
                    query: 'How does machine learning work?',
                    options: {
                        topK: 5,
                        minScore: 0.7,
                        filter: { category: 'AI' },
                        namespace: 'knowledge-base',
                        includeContent: true,
                    },
                },
            },
        },
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Knowledge search results',
        type: 'object',
    }),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", typeof (_p = typeof Promise !== "undefined" && Promise) === "function" ? _p : Object)
], VectorDatabaseController.prototype, "searchKnowledge", null);
__decorate([
    (0, common_1.Get)('health'),
    (0, swagger_1.ApiOperation)({
        summary: 'Health check',
        description: 'Check the health status of the vector database',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Health check results',
        type: 'object',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_q = typeof Promise !== "undefined" && Promise) === "function" ? _q : Object)
], VectorDatabaseController.prototype, "healthCheck", null);
__decorate([
    (0, common_1.Delete)('namespace/:namespace'),
    (0, common_1.HttpCode)(common_1.HttpStatus.OK),
    (0, swagger_1.ApiOperation)({
        summary: 'Delete namespace',
        description: 'Delete all vectors in a specific namespace',
    }),
    (0, swagger_1.ApiParam)({
        name: 'namespace',
        description: 'Namespace to delete',
        type: String,
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Namespace deleted successfully',
        type: 'object',
    }),
    __param(0, (0, common_1.Param)('namespace')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", typeof (_r = typeof Promise !== "undefined" && Promise) === "function" ? _r : Object)
], VectorDatabaseController.prototype, "deleteNamespace", null);
__decorate([
    (0, common_1.Get)('namespaces'),
    (0, swagger_1.ApiOperation)({
        summary: 'List namespaces',
        description: 'Get statistics for all namespaces',
    }),
    (0, swagger_1.ApiResponse)({
        status: common_1.HttpStatus.OK,
        description: 'Namespace statistics',
        type: 'object',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", typeof (_s = typeof Promise !== "undefined" && Promise) === "function" ? _s : Object)
], VectorDatabaseController.prototype, "listNamespaces", null);
exports.VectorDatabaseController = VectorDatabaseController = VectorDatabaseController_1 = __decorate([
    (0, swagger_1.ApiTags)('Vector Database'),
    (0, common_1.Controller)('vector-database'),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [typeof (_a = typeof vector_database_service_1.VectorDatabaseService !== "undefined" && vector_database_service_1.VectorDatabaseService) === "function" ? _a : Object])
], VectorDatabaseController);


/***/ }),

/***/ "./src/modules/vector-database/providers/pinecone.provider.ts":
/*!********************************************************************!*\
  !*** ./src/modules/vector-database/providers/pinecone.provider.ts ***!
  \********************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var PineconeProvider_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.PineconeProvider = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
let PineconeProvider = PineconeProvider_1 = class PineconeProvider {
    configService;
    logger = new common_1.Logger(PineconeProvider_1.name);
    constructor(configService) {
        this.configService = configService;
    }
    async initialize() {
        this.logger.log('Pinecone provider initialized (stub implementation)');
    }
    async search() {
        return { matches: [] };
    }
    async upsert() {
        return { success: true };
    }
    async delete() {
        return { success: true };
    }
};
exports.PineconeProvider = PineconeProvider;
exports.PineconeProvider = PineconeProvider = PineconeProvider_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], PineconeProvider);


/***/ }),

/***/ "./src/modules/vector-database/services/embedding.service.ts":
/*!*******************************************************************!*\
  !*** ./src/modules/vector-database/services/embedding.service.ts ***!
  \*******************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
var EmbeddingService_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.EmbeddingService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const openai_1 = __importDefault(__webpack_require__(/*! openai */ "openai"));
const vector_database_types_1 = __webpack_require__(/*! @shared/types/vector-database.types */ "../shared/src/types/vector-database.types.ts");
let EmbeddingService = EmbeddingService_1 = class EmbeddingService {
    configService;
    logger = new common_1.Logger(EmbeddingService_1.name);
    openaiClient = null;
    config = null;
    isInitialized = false;
    requestQueue = [];
    isProcessingQueue = false;
    lastRequestTime = 0;
    requestCount = 0;
    windowStart = Date.now();
    constructor(configService) {
        this.configService = configService;
    }
    async initialize(config) {
        try {
            this.config = config;
            this.logger.log(`Initializing embedding service with provider: ${config.provider}`);
            switch (config.provider) {
                case 'openai':
                    await this.initializeOpenAI(config);
                    break;
                case 'azure':
                    await this.initializeAzureOpenAI(config);
                    break;
                case 'huggingface':
                    await this.initializeHuggingFace(config);
                    break;
                case 'cohere':
                    await this.initializeCohere(config);
                    break;
                default:
                    throw new Error(`Unsupported embedding provider: ${config.provider}`);
            }
            this.isInitialized = true;
            this.logger.log(`Embedding service initialized successfully with ${config.provider}`);
        }
        catch (error) {
            this.logger.error('Failed to initialize embedding service', error);
            throw new vector_database_types_1.EmbeddingError('Failed to initialize embedding service', config.provider, config.model, false);
        }
    }
    async generateEmbedding(text) {
        try {
            this.ensureInitialized();
            if (!text || text.trim().length === 0) {
                throw new vector_database_types_1.EmbeddingError('Text cannot be empty', this.config.provider, this.config.model, false);
            }
            if (this.exceedsTokenLimit(text)) {
                throw new vector_database_types_1.EmbeddingError(`Text exceeds maximum token limit of ${this.config.maxTokens}`, this.config.provider, this.config.model, false);
            }
            await this.applyRateLimit();
            let embedding;
            switch (this.config.provider) {
                case 'openai':
                case 'azure':
                    embedding = await this.generateOpenAIEmbedding(text);
                    break;
                case 'huggingface':
                    embedding = await this.generateHuggingFaceEmbedding(text);
                    break;
                case 'cohere':
                    embedding = await this.generateCohereEmbedding(text);
                    break;
                default:
                    throw new vector_database_types_1.EmbeddingError(`Unsupported provider: ${this.config.provider}`, this.config.provider, this.config.model, false);
            }
            this.logger.debug(`Generated embedding for text of length ${text.length}`);
            return embedding;
        }
        catch (error) {
            if (error instanceof vector_database_types_1.EmbeddingError) {
                throw error;
            }
            this.logger.error('Failed to generate embedding', error);
            throw new vector_database_types_1.EmbeddingError('Failed to generate embedding', this.config?.provider || 'unknown', this.config?.model || 'unknown', this.isRetryableError(error));
        }
    }
    async generateEmbeddings(texts) {
        try {
            this.ensureInitialized();
            if (!texts || texts.length === 0) {
                throw new vector_database_types_1.EmbeddingError('Texts array cannot be empty', this.config.provider, this.config.model, false);
            }
            for (const text of texts) {
                if (!text || text.trim().length === 0) {
                    throw new vector_database_types_1.EmbeddingError('All texts must be non-empty', this.config.provider, this.config.model, false);
                }
                if (this.exceedsTokenLimit(text)) {
                    throw new vector_database_types_1.EmbeddingError(`Text exceeds maximum token limit of ${this.config.maxTokens}`, this.config.provider, this.config.model, false);
                }
            }
            const batchSize = this.config.batchSize || 100;
            const embeddings = [];
            for (let i = 0; i < texts.length; i += batchSize) {
                const batch = texts.slice(i, i + batchSize);
                await this.applyRateLimit();
                let batchEmbeddings;
                switch (this.config.provider) {
                    case 'openai':
                    case 'azure':
                        batchEmbeddings = await this.generateOpenAIEmbeddings(batch);
                        break;
                    case 'huggingface':
                        batchEmbeddings = await this.generateHuggingFaceEmbeddings(batch);
                        break;
                    case 'cohere':
                        batchEmbeddings = await this.generateCohereEmbeddings(batch);
                        break;
                    default:
                        throw new vector_database_types_1.EmbeddingError(`Unsupported provider: ${this.config.provider}`, this.config.provider, this.config.model, false);
                }
                embeddings.push(...batchEmbeddings);
                this.logger.debug(`Generated ${batchEmbeddings.length} embeddings in batch`);
                if (i + batchSize < texts.length) {
                    await this.delay(100);
                }
            }
            this.logger.log(`Generated ${embeddings.length} embeddings for ${texts.length} texts`);
            return embeddings;
        }
        catch (error) {
            if (error instanceof vector_database_types_1.EmbeddingError) {
                throw error;
            }
            this.logger.error('Failed to generate batch embeddings', error);
            throw new vector_database_types_1.EmbeddingError('Failed to generate batch embeddings', this.config?.provider || 'unknown', this.config?.model || 'unknown', this.isRetryableError(error));
        }
    }
    getDimension() {
        this.ensureInitialized();
        return this.config.dimension;
    }
    getMaxTokens() {
        this.ensureInitialized();
        return this.config.maxTokens || 8192;
    }
    async healthCheck() {
        try {
            if (!this.isInitialized || !this.config) {
                return false;
            }
            const testText = 'Health check test';
            await this.generateEmbedding(testText);
            return true;
        }
        catch (error) {
            this.logger.error('Embedding service health check failed', error);
            return false;
        }
    }
    async initializeOpenAI(config) {
        this.openaiClient = new openai_1.default({
            apiKey: config.apiKey || this.configService.get('OPENAI_API_KEY'),
        });
        try {
            await this.openaiClient.embeddings.create({
                model: config.model,
                input: 'test',
            });
        }
        catch (error) {
            throw new Error(`Failed to connect to OpenAI: ${error}`);
        }
    }
    async initializeAzureOpenAI(config) {
        if (!config.endpoint) {
            throw new Error('Azure OpenAI endpoint is required');
        }
        this.openaiClient = new openai_1.default({
            apiKey: config.apiKey || this.configService.get('AZURE_OPENAI_API_KEY'),
            baseURL: config.endpoint,
            defaultQuery: { 'api-version': '2023-05-15' },
            defaultHeaders: {
                'api-key': config.apiKey || this.configService.get('AZURE_OPENAI_API_KEY'),
            },
        });
    }
    async initializeHuggingFace(config) {
        this.logger.warn('HuggingFace embedding provider not fully implemented');
    }
    async initializeCohere(config) {
        this.logger.warn('Cohere embedding provider not fully implemented');
    }
    async generateOpenAIEmbedding(text) {
        try {
            const response = await this.openaiClient.embeddings.create({
                model: this.config.model,
                input: text,
            });
            return response.data[0]?.embedding || [];
        }
        catch (error) {
            throw new vector_database_types_1.EmbeddingError(`OpenAI embedding failed: ${error}`, 'openai', this.config.model, this.isRetryableError(error));
        }
    }
    async generateOpenAIEmbeddings(texts) {
        try {
            const response = await this.openaiClient.embeddings.create({
                model: this.config.model,
                input: texts,
            });
            return response.data.map((item) => item.embedding);
        }
        catch (error) {
            throw new vector_database_types_1.EmbeddingError(`OpenAI batch embedding failed: ${error}`, 'openai', this.config.model, this.isRetryableError(error));
        }
    }
    async generateHuggingFaceEmbedding(text) {
        throw new vector_database_types_1.EmbeddingError('HuggingFace embedding not implemented', 'huggingface', this.config.model, false);
    }
    async generateHuggingFaceEmbeddings(texts) {
        throw new vector_database_types_1.EmbeddingError('HuggingFace batch embedding not implemented', 'huggingface', this.config.model, false);
    }
    async generateCohereEmbedding(text) {
        throw new vector_database_types_1.EmbeddingError('Cohere embedding not implemented', 'cohere', this.config.model, false);
    }
    async generateCohereEmbeddings(texts) {
        throw new vector_database_types_1.EmbeddingError('Cohere batch embedding not implemented', 'cohere', this.config.model, false);
    }
    ensureInitialized() {
        if (!this.isInitialized || !this.config) {
            throw new vector_database_types_1.EmbeddingError('Embedding service is not initialized', 'unknown', 'unknown', false);
        }
    }
    exceedsTokenLimit(text) {
        const estimatedTokens = Math.ceil(text.length / 4);
        return estimatedTokens > this.getMaxTokens();
    }
    async applyRateLimit() {
        if (!this.config?.rateLimitRpm) {
            return;
        }
        const now = Date.now();
        const windowDuration = 60000;
        if (now - this.windowStart >= windowDuration) {
            this.windowStart = now;
            this.requestCount = 0;
        }
        if (this.requestCount >= this.config.rateLimitRpm) {
            const waitTime = windowDuration - (now - this.windowStart);
            this.logger.warn(`Rate limit exceeded, waiting ${waitTime}ms`);
            await this.delay(waitTime);
            this.windowStart = Date.now();
            this.requestCount = 0;
        }
        this.requestCount++;
    }
    isRetryableError(error) {
        if (error?.response?.status) {
            const status = error.response.status;
            return status === 429 || status === 500 || status === 502 || status === 503 || status === 504;
        }
        if (error?.code) {
            return ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND'].includes(error.code);
        }
        return false;
    }
    async delay(ms) {
        return new Promise((resolve) => setTimeout(resolve, ms));
    }
};
exports.EmbeddingService = EmbeddingService;
exports.EmbeddingService = EmbeddingService = EmbeddingService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof config_1.ConfigService !== "undefined" && config_1.ConfigService) === "function" ? _a : Object])
], EmbeddingService);


/***/ }),

/***/ "./src/modules/vector-database/services/vector-database.service.ts":
/*!*************************************************************************!*\
  !*** ./src/modules/vector-database/services/vector-database.service.ts ***!
  \*************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var VectorDatabaseService_1;
var _a;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.VectorDatabaseService = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const prisma_service_1 = __webpack_require__(/*! ../../prisma/prisma.service */ "./src/modules/prisma/prisma.service.ts");
let VectorDatabaseService = VectorDatabaseService_1 = class VectorDatabaseService {
    prismaService;
    logger = new common_1.Logger(VectorDatabaseService_1.name);
    constructor(prismaService) {
        this.prismaService = prismaService;
    }
    async search(request) {
        this.logger.log(`Vector search: ${JSON.stringify(request)}`);
        return {
            matches: []
        };
    }
    async upsert(request) {
        this.logger.log(`Vector upsert: ${request.vectors.length} vectors`);
        return {
            upsertedCount: request.vectors.length
        };
    }
    async delete(request) {
        this.logger.log(`Vector delete: ${JSON.stringify(request)}`);
        return {
            deletedCount: request.ids?.length || 0
        };
    }
    async getIndexStats(namespace) {
        this.logger.log(`Getting index stats for namespace: ${namespace || 'default'}`);
        return {
            dimension: 1536,
            indexFullness: 0,
            totalVectorCount: 0,
            namespaces: {
                [namespace || 'default']: {
                    vectorCount: 0
                }
            }
        };
    }
    async batch(request) {
        this.logger.log(`Batch request with ${request.operations.length} operations`);
        return {
            results: request.operations.map(op => ({
                operationId: op.id,
                success: true,
                result: {}
            })),
            totalOperations: request.operations.length,
            successfulOperations: request.operations.length,
            failedOperations: 0
        };
    }
    async similaritySearch(request) {
        this.logger.log(`Similarity search: ${request.query}`);
        return {
            matches: [],
            totalResults: 0,
            filteredResults: 0
        };
    }
    async addKnowledgeDocument(document, namespace) {
        this.logger.log(`Adding knowledge document: ${document.title}`);
        return {
            upsertedCount: 1
        };
    }
    async searchKnowledge(query, options) {
        this.logger.log(`Knowledge search: ${query}`);
        return {
            matches: [],
            totalResults: 0,
            filteredResults: 0
        };
    }
    async healthCheck() {
        this.logger.log('Vector database health check');
        return {
            status: 'healthy',
            details: { connected: true }
        };
    }
};
exports.VectorDatabaseService = VectorDatabaseService;
exports.VectorDatabaseService = VectorDatabaseService = VectorDatabaseService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeof (_a = typeof prisma_service_1.PrismaService !== "undefined" && prisma_service_1.PrismaService) === "function" ? _a : Object])
], VectorDatabaseService);


/***/ }),

/***/ "./src/modules/vector-database/vector-database.module.ts":
/*!***************************************************************!*\
  !*** ./src/modules/vector-database/vector-database.module.ts ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {


var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var VectorDatabaseModule_1;
Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.VectorDatabaseModule = void 0;
const common_1 = __webpack_require__(/*! @nestjs/common */ "@nestjs/common");
const config_1 = __webpack_require__(/*! @nestjs/config */ "@nestjs/config");
const pinecone_provider_1 = __webpack_require__(/*! ./providers/pinecone.provider */ "./src/modules/vector-database/providers/pinecone.provider.ts");
const embedding_service_1 = __webpack_require__(/*! ./services/embedding.service */ "./src/modules/vector-database/services/embedding.service.ts");
const vector_database_service_1 = __webpack_require__(/*! ./services/vector-database.service */ "./src/modules/vector-database/services/vector-database.service.ts");
const vector_database_controller_1 = __webpack_require__(/*! ./controllers/vector-database.controller */ "./src/modules/vector-database/controllers/vector-database.controller.ts");
let VectorDatabaseModule = VectorDatabaseModule_1 = class VectorDatabaseModule {
    static forRootAsync(options) {
        return {
            module: VectorDatabaseModule_1,
            imports: [
                config_1.ConfigModule,
                ...(options.imports || []),
            ],
            providers: [
                {
                    provide: 'VECTOR_DATABASE_CONFIG',
                    useFactory: options.useFactory || this.createDefaultConfig,
                    inject: options.inject || [config_1.ConfigService],
                },
                pinecone_provider_1.PineconeProvider,
                embedding_service_1.EmbeddingService,
                vector_database_service_1.VectorDatabaseService,
                {
                    provide: 'VECTOR_DATABASE_PROVIDERS',
                    useFactory: (pineconeProvider) => {
                        return {
                            pinecone: pineconeProvider,
                        };
                    },
                    inject: [pinecone_provider_1.PineconeProvider],
                },
            ],
            controllers: [vector_database_controller_1.VectorDatabaseController],
            exports: [
                vector_database_service_1.VectorDatabaseService,
                embedding_service_1.EmbeddingService,
                pinecone_provider_1.PineconeProvider,
                'VECTOR_DATABASE_CONFIG',
                'VECTOR_DATABASE_PROVIDERS',
            ],
        };
    }
    static forRoot(config) {
        return {
            module: VectorDatabaseModule_1,
            imports: [config_1.ConfigModule],
            providers: [
                {
                    provide: 'VECTOR_DATABASE_CONFIG',
                    useValue: config,
                },
                pinecone_provider_1.PineconeProvider,
                embedding_service_1.EmbeddingService,
                vector_database_service_1.VectorDatabaseService,
                {
                    provide: 'VECTOR_DATABASE_PROVIDERS',
                    useFactory: (pineconeProvider) => {
                        return {
                            pinecone: pineconeProvider,
                        };
                    },
                    inject: [pinecone_provider_1.PineconeProvider],
                },
            ],
            controllers: [vector_database_controller_1.VectorDatabaseController],
            exports: [
                vector_database_service_1.VectorDatabaseService,
                embedding_service_1.EmbeddingService,
                pinecone_provider_1.PineconeProvider,
                'VECTOR_DATABASE_CONFIG',
                'VECTOR_DATABASE_PROVIDERS',
            ],
        };
    }
    static createDefaultConfig(configService) {
        return {
            providers: {
                pinecone: {
                    provider: 'pinecone',
                    apiKey: configService.get('PINECONE_API_KEY') || '',
                    environment: configService.get('PINECONE_ENVIRONMENT') || 'us-east1-gcp',
                    indexName: configService.get('PINECONE_INDEX_NAME') || 'synapseai-vectors',
                    dimension: parseInt(configService.get('PINECONE_DIMENSION') || '1536', 10),
                    metric: 'cosine',
                    serverless: configService.get('PINECONE_SERVERLESS') === 'true',
                    cloudRegion: configService.get('PINECONE_CLOUD_REGION') || 'us-east-1',
                    maxRetries: parseInt(configService.get('PINECONE_MAX_RETRIES') || '3', 10),
                    timeout: parseInt(configService.get('PINECONE_TIMEOUT') || '30000', 10),
                },
            },
            defaultProvider: 'pinecone',
            embedding: {
                provider: configService.get('EMBEDDING_PROVIDER') || 'openai',
                config: {
                    model: configService.get('EMBEDDING_MODEL') || 'text-embedding-ada-002',
                    apiKey: configService.get('OPENAI_API_KEY'),
                    dimension: parseInt(configService.get('EMBEDDING_DIMENSION') || '1536', 10),
                    maxTokens: parseInt(configService.get('EMBEDDING_MAX_TOKENS') || '8192', 10),
                    batchSize: parseInt(configService.get('EMBEDDING_BATCH_SIZE') || '100', 10),
                    rateLimitRpm: parseInt(configService.get('EMBEDDING_RATE_LIMIT_RPM') || '3500', 10),
                    retryConfig: {
                        maxRetries: parseInt(configService.get('EMBEDDING_MAX_RETRIES') || '3', 10),
                        baseDelay: parseInt(configService.get('EMBEDDING_BASE_DELAY') || '1000', 10),
                        maxDelay: parseInt(configService.get('EMBEDDING_MAX_DELAY') || '10000', 10),
                    },
                },
            },
            processing: {
                chunkSize: parseInt(configService.get('CHUNK_SIZE') || '1000', 10),
                chunkOverlap: parseInt(configService.get('CHUNK_OVERLAP') || '200', 10),
                maxTokens: parseInt(configService.get('MAX_TOKENS_PER_CHUNK') || '8192', 10),
                batchSize: parseInt(configService.get('PROCESSING_BATCH_SIZE') || '10', 10),
            },
            monitoring: {
                enabled: configService.get('VECTOR_DB_MONITORING_ENABLED') === 'true',
                metricsInterval: parseInt(configService.get('METRICS_INTERVAL') || '60000', 10),
                healthCheckInterval: parseInt(configService.get('HEALTH_CHECK_INTERVAL') || '30000', 10),
            },
            cache: {
                enabled: configService.get('VECTOR_DB_CACHE_ENABLED') === 'true',
                ttl: parseInt(configService.get('CACHE_TTL') || '3600', 10),
                maxSize: parseInt(configService.get('CACHE_MAX_SIZE') || '1000', 10),
            },
        };
    }
};
exports.VectorDatabaseModule = VectorDatabaseModule;
exports.VectorDatabaseModule = VectorDatabaseModule = VectorDatabaseModule_1 = __decorate([
    (0, common_1.Global)(),
    (0, common_1.Module)({})
], VectorDatabaseModule);


/***/ }),

/***/ "../shared/src/types/vector-database.types.ts":
/*!****************************************************!*\
  !*** ../shared/src/types/vector-database.types.ts ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {


Object.defineProperty(exports, "__esModule", ({ value: true }));
exports.VectorDatabaseSchemas = exports.VectorError = exports.isVectorDatabaseConfig = exports.isSemanticSearchQuery = exports.isVectorMetadata = exports.EmbeddingConfigSchema = exports.VectorDatabaseConfigSchema = exports.SemanticSearchQuerySchema = exports.VectorSearchQuerySchema = exports.VectorMetadataSchema = exports.EmbeddingError = exports.VectorDatabaseError = void 0;
const zod_1 = __webpack_require__(/*! zod */ "zod");
class VectorDatabaseError extends Error {
    code;
    statusCode;
    details;
    constructor(message, code, statusCode, details) {
        super(message);
        this.code = code;
        this.statusCode = statusCode;
        this.details = details;
        this.name = 'VectorDatabaseError';
    }
}
exports.VectorDatabaseError = VectorDatabaseError;
class EmbeddingError extends Error {
    provider;
    model;
    retryable;
    constructor(message, provider, model, retryable = false) {
        super(message);
        this.provider = provider;
        this.model = model;
        this.retryable = retryable;
        this.name = 'EmbeddingError';
    }
}
exports.EmbeddingError = EmbeddingError;
exports.VectorMetadataSchema = zod_1.z.object({
    documentId: zod_1.z.string().uuid(),
    organizationId: zod_1.z.string().uuid(),
    userId: zod_1.z.string().uuid(),
    source: zod_1.z.string().min(1),
    sourceType: zod_1.z.enum(['document', 'url', 'text', 'api', 'upload']),
    timestamp: zod_1.z.date(),
    chunkIndex: zod_1.z.number().int().min(0),
    totalChunks: zod_1.z.number().int().min(1),
    contentType: zod_1.z.string().min(1),
    language: zod_1.z.string().optional(),
    title: zod_1.z.string().optional(),
    summary: zod_1.z.string().optional(),
    tags: zod_1.z.array(zod_1.z.string()).optional(),
    category: zod_1.z.string().optional(),
    version: zod_1.z.string().optional(),
    author: zod_1.z.string().optional(),
    confidence: zod_1.z.number().min(0).max(1).optional(),
});
exports.VectorSearchQuerySchema = zod_1.z.object({
    vector: zod_1.z.array(zod_1.z.number()).optional(),
    sparseVector: zod_1.z.object({
        indices: zod_1.z.array(zod_1.z.number().int()),
        values: zod_1.z.array(zod_1.z.number()),
    }).optional(),
    topK: zod_1.z.number().int().min(1).max(1000),
    filter: zod_1.z.record(zod_1.z.any()).optional(),
    includeMetadata: zod_1.z.boolean().optional(),
    includeValues: zod_1.z.boolean().optional(),
    namespace: zod_1.z.string().optional(),
});
exports.SemanticSearchQuerySchema = zod_1.z.object({
    query: zod_1.z.string().min(1).max(10000),
    knowledgeBaseId: zod_1.z.string().uuid(),
    organizationId: zod_1.z.string().uuid(),
    topK: zod_1.z.number().int().min(1).max(100).optional(),
    minScore: zod_1.z.number().min(0).max(1).optional(),
    filter: zod_1.z.object({
        documentIds: zod_1.z.array(zod_1.z.string().uuid()).optional(),
        sourceTypes: zod_1.z.array(zod_1.z.enum(['document', 'url', 'text', 'api', 'upload'])).optional(),
        tags: zod_1.z.array(zod_1.z.string()).optional(),
        dateRange: zod_1.z.object({
            start: zod_1.z.date(),
            end: zod_1.z.date(),
        }).optional(),
        metadata: zod_1.z.record(zod_1.z.any()).optional(),
    }).optional(),
    rerank: zod_1.z.boolean().optional(),
    includeContent: zod_1.z.boolean().optional(),
    hybridSearch: zod_1.z.object({
        enabled: zod_1.z.boolean(),
        alpha: zod_1.z.number().min(0).max(1).optional(),
    }).optional(),
});
exports.VectorDatabaseConfigSchema = zod_1.z.object({
    provider: zod_1.z.enum(['pinecone', 'weaviate', 'chroma', 'qdrant']),
    apiKey: zod_1.z.string().min(1),
    environment: zod_1.z.string().optional(),
    indexName: zod_1.z.string().min(1),
    dimension: zod_1.z.number().int().min(1).max(50000),
    metric: zod_1.z.enum(['cosine', 'euclidean', 'dotproduct']),
    namespace: zod_1.z.string().optional(),
    cloudRegion: zod_1.z.string().optional(),
    serverless: zod_1.z.boolean().optional(),
    replicas: zod_1.z.number().int().min(1).optional(),
    podType: zod_1.z.string().optional(),
    maxRetries: zod_1.z.number().int().min(0).max(10).optional(),
    timeout: zod_1.z.number().int().min(1000).optional(),
});
exports.EmbeddingConfigSchema = zod_1.z.object({
    provider: zod_1.z.enum(['openai', 'huggingface', 'cohere', 'azure', 'local']),
    model: zod_1.z.string().min(1),
    apiKey: zod_1.z.string().optional(),
    endpoint: zod_1.z.string().url().optional(),
    dimension: zod_1.z.number().int().min(1).max(50000),
    maxTokens: zod_1.z.number().int().min(1).optional(),
    batchSize: zod_1.z.number().int().min(1).max(2048).optional(),
    rateLimitRpm: zod_1.z.number().int().min(1).optional(),
    retryConfig: zod_1.z.object({
        maxRetries: zod_1.z.number().int().min(0).max(10),
        baseDelay: zod_1.z.number().int().min(100),
        maxDelay: zod_1.z.number().int().min(1000),
    }).optional(),
});
const isVectorMetadata = (obj) => {
    return exports.VectorMetadataSchema.safeParse(obj).success;
};
exports.isVectorMetadata = isVectorMetadata;
const isSemanticSearchQuery = (obj) => {
    return exports.SemanticSearchQuerySchema.safeParse(obj).success;
};
exports.isSemanticSearchQuery = isSemanticSearchQuery;
const isVectorDatabaseConfig = (obj) => {
    return exports.VectorDatabaseConfigSchema.safeParse(obj).success;
};
exports.isVectorDatabaseConfig = isVectorDatabaseConfig;
class VectorError extends Error {
    code;
    details;
    constructor(message, code, details) {
        super(message);
        this.code = code;
        this.details = details;
        this.name = 'VectorError';
    }
}
exports.VectorError = VectorError;
exports.VectorDatabaseSchemas = {
    VectorMetadataSchema: exports.VectorMetadataSchema,
    VectorSearchQuerySchema: exports.VectorSearchQuerySchema,
    SemanticSearchQuerySchema: exports.SemanticSearchQuerySchema,
    VectorDatabaseConfigSchema: exports.VectorDatabaseConfigSchema,
    EmbeddingConfigSchema: exports.EmbeddingConfigSchema,
};


/***/ }),

/***/ ".prisma/client":
/*!*********************************!*\
  !*** external ".prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require(".prisma/client");

/***/ }),

/***/ "@casl/ability":
/*!********************************!*\
  !*** external "@casl/ability" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@casl/ability");

/***/ }),

/***/ "@nestjs/axios":
/*!********************************!*\
  !*** external "@nestjs/axios" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("@nestjs/axios");

/***/ }),

/***/ "@nestjs/common":
/*!*********************************!*\
  !*** external "@nestjs/common" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@nestjs/common");

/***/ }),

/***/ "@nestjs/config":
/*!*********************************!*\
  !*** external "@nestjs/config" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@nestjs/config");

/***/ }),

/***/ "@nestjs/core":
/*!*******************************!*\
  !*** external "@nestjs/core" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("@nestjs/core");

/***/ }),

/***/ "@nestjs/jwt":
/*!******************************!*\
  !*** external "@nestjs/jwt" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("@nestjs/jwt");

/***/ }),

/***/ "@nestjs/passport":
/*!***********************************!*\
  !*** external "@nestjs/passport" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("@nestjs/passport");

/***/ }),

/***/ "@nestjs/platform-ws":
/*!**************************************!*\
  !*** external "@nestjs/platform-ws" ***!
  \**************************************/
/***/ ((module) => {

module.exports = require("@nestjs/platform-ws");

/***/ }),

/***/ "@nestjs/schedule":
/*!***********************************!*\
  !*** external "@nestjs/schedule" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("@nestjs/schedule");

/***/ }),

/***/ "@nestjs/swagger":
/*!**********************************!*\
  !*** external "@nestjs/swagger" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("@nestjs/swagger");

/***/ }),

/***/ "@nestjs/terminus":
/*!***********************************!*\
  !*** external "@nestjs/terminus" ***!
  \***********************************/
/***/ ((module) => {

module.exports = require("@nestjs/terminus");

/***/ }),

/***/ "@nestjs/throttler":
/*!************************************!*\
  !*** external "@nestjs/throttler" ***!
  \************************************/
/***/ ((module) => {

module.exports = require("@nestjs/throttler");

/***/ }),

/***/ "@nestjs/websockets":
/*!*************************************!*\
  !*** external "@nestjs/websockets" ***!
  \*************************************/
/***/ ((module) => {

module.exports = require("@nestjs/websockets");

/***/ }),

/***/ "bcrypt":
/*!*************************!*\
  !*** external "bcrypt" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("bcrypt");

/***/ }),

/***/ "class-validator":
/*!**********************************!*\
  !*** external "class-validator" ***!
  \**********************************/
/***/ ((module) => {

module.exports = require("class-validator");

/***/ }),

/***/ "compression":
/*!******************************!*\
  !*** external "compression" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("compression");

/***/ }),

/***/ "express":
/*!**************************!*\
  !*** external "express" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("express");

/***/ }),

/***/ "express-rate-limit":
/*!*************************************!*\
  !*** external "express-rate-limit" ***!
  \*************************************/
/***/ ((module) => {

module.exports = require("express-rate-limit");

/***/ }),

/***/ "helmet":
/*!*************************!*\
  !*** external "helmet" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("helmet");

/***/ }),

/***/ "ioredis":
/*!**************************!*\
  !*** external "ioredis" ***!
  \**************************/
/***/ ((module) => {

module.exports = require("ioredis");

/***/ }),

/***/ "openai":
/*!*************************!*\
  !*** external "openai" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("openai");

/***/ }),

/***/ "passport-jwt":
/*!*******************************!*\
  !*** external "passport-jwt" ***!
  \*******************************/
/***/ ((module) => {

module.exports = require("passport-jwt");

/***/ }),

/***/ "passport-local":
/*!*********************************!*\
  !*** external "passport-local" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("passport-local");

/***/ }),

/***/ "socket.io":
/*!****************************!*\
  !*** external "socket.io" ***!
  \****************************/
/***/ ((module) => {

module.exports = require("socket.io");

/***/ }),

/***/ "uuid":
/*!***********************!*\
  !*** external "uuid" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("uuid");

/***/ }),

/***/ "zod":
/*!**********************!*\
  !*** external "zod" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("zod");

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module is referenced by other modules so it can't be inlined
/******/ 	var __webpack_exports__ = __webpack_require__("./src/main.ts");
/******/ 	
/******/ })()
;