@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Custom Properties for theming */
@layer base {
  :root {
    /* Brand colors */
    --color-brand-50: 240 249 255;
    --color-brand-100: 224 242 254;
    --color-brand-200: 186 230 253;
    --color-brand-300: 125 211 252;
    --color-brand-400: 56 189 248;
    --color-brand-500: 14 165 233;
    --color-brand-600: 2 132 199;
    --color-brand-700: 3 105 161;
    --color-brand-800: 7 89 133;
    --color-brand-900: 12 74 110;
    --color-brand-950: 8 47 73;

    /* Semantic colors */
    --color-success-50: 240 253 244;
    --color-success-500: 34 197 94;
    --color-success-600: 22 163 74;
    --color-success-700: 21 128 61;

    --color-warning-50: 255 251 235;
    --color-warning-500: 245 158 11;
    --color-warning-600: 217 119 6;
    --color-warning-700: 180 83 9;

    --color-error-50: 254 242 242;
    --color-error-500: 239 68 68;
    --color-error-600: 220 38 38;
    --color-error-700: 185 28 28;

    /* Gray scale */
    --color-gray-50: 249 250 251;
    --color-gray-100: 243 244 246;
    --color-gray-200: 229 231 235;
    --color-gray-300: 209 213 219;
    --color-gray-400: 156 163 175;
    --color-gray-500: 107 114 128;
    --color-gray-600: 75 85 99;
    --color-gray-700: 55 65 81;
    --color-gray-800: 31 41 55;
    --color-gray-900: 17 24 39;
    --color-gray-950: 10 10 10;

    /* Background colors */
    --bg-primary: rgb(var(--color-gray-50));
    --bg-secondary: rgb(var(--color-gray-100));
    --bg-tertiary: rgb(var(--color-gray-200));
    --bg-surface: rgb(255 255 255);
    --bg-overlay: rgba(0 0 0 / 0.5);

    /* Text colors */
    --text-primary: rgb(var(--color-gray-900));
    --text-secondary: rgb(var(--color-gray-600));
    --text-tertiary: rgb(var(--color-gray-500));
    --text-inverse: rgb(255 255 255);

    /* Border colors */
    --border-primary: rgb(var(--color-gray-200));
    --border-secondary: rgb(var(--color-gray-300));
    --border-focus: rgb(var(--color-brand-500));

    /* Shadow variables */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

    /* Toast colors */
    --toast-bg: rgb(255 255 255);
    --toast-color: rgb(var(--color-gray-900));
    --toast-border: rgb(var(--color-gray-200));

    /* Fonts */
    --font-inter: var(--font-inter);
    --font-mono: var(--font-jetbrains-mono);

    /* Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Z-index scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
  }

  .dark {
    /* Dark mode overrides */
    --bg-primary: rgb(var(--color-gray-950));
    --bg-secondary: rgb(var(--color-gray-900));
    --bg-tertiary: rgb(var(--color-gray-800));
    --bg-surface: rgb(var(--color-gray-900));
    --bg-overlay: rgba(0 0 0 / 0.8);

    --text-primary: rgb(var(--color-gray-50));
    --text-secondary: rgb(var(--color-gray-300));
    --text-tertiary: rgb(var(--color-gray-400));
    --text-inverse: rgb(var(--color-gray-900));

    --border-primary: rgb(var(--color-gray-800));
    --border-secondary: rgb(var(--color-gray-700));

    --toast-bg: rgb(var(--color-gray-800));
    --toast-color: rgb(var(--color-gray-50));
    --toast-border: rgb(var(--color-gray-700));
  }

  /* Base styles */
  * {
    @apply border-gray-200 dark:border-gray-800;
  }

  html {
    scroll-behavior: smooth;
    text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  body {
    @apply bg-gray-50 text-gray-900 dark:bg-gray-950 dark:text-gray-50;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-variant-ligatures: common-ligatures;
  }

  /* Typography */
  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl lg:text-5xl;
  }

  h2 {
    @apply text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-2xl lg:text-3xl;
  }

  h4 {
    @apply text-xl lg:text-2xl;
  }

  h5 {
    @apply text-lg lg:text-xl;
  }

  h6 {
    @apply text-base lg:text-lg;
  }

  /* Focus styles */
  [tabindex="-1"]:focus:not(:focus-visible) {
    outline: 0 !important;
  }

  /* Selection styles */
  ::selection {
    @apply bg-brand-200 text-brand-900;
  }

  .dark ::selection {
    @apply bg-brand-800 text-brand-100;
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-md;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }

  /* Autofill styles */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  textarea:-webkit-autofill,
  textarea:-webkit-autofill:hover,
  textarea:-webkit-autofill:focus,
  select:-webkit-autofill,
  select:-webkit-autofill:hover,
  select:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 1000px rgb(var(--color-gray-50)) inset;
    -webkit-text-fill-color: rgb(var(--color-gray-900));
  }

  .dark input:-webkit-autofill,
  .dark input:-webkit-autofill:hover,
  .dark input:-webkit-autofill:focus,
  .dark textarea:-webkit-autofill,
  .dark textarea:-webkit-autofill:hover,
  .dark textarea:-webkit-autofill:focus,
  .dark select:-webkit-autofill,
  .dark select:-webkit-autofill:hover,
  .dark select:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 1000px rgb(var(--color-gray-800)) inset;
    -webkit-text-fill-color: rgb(var(--color-gray-50));
  }

  /* Print styles */
  @media print {
    *,
    *::before,
    *::after {
      background: transparent !important;
      color: black !important;
      box-shadow: none !important;
      text-shadow: none !important;
    }

    a,
    a:visited {
      text-decoration: underline;
    }

    abbr[title]::after {
      content: " (" attr(title) ")";
    }

    pre {
      white-space: pre-wrap !important;
    }

    pre,
    blockquote {
      border: 1px solid #999;
      page-break-inside: avoid;
    }

    thead {
      display: table-header-group;
    }

    tr,
    img {
      page-break-inside: avoid;
    }

    p,
    h2,
    h3 {
      orphans: 3;
      widows: 3;
    }

    h2,
    h3 {
      page-break-after: avoid;
    }
  }
}

/* Component styles */
@layer components {
  /* Loading spinner */
  .spinner {
    @apply inline-block w-4 h-4 border-2 border-gray-300 border-t-brand-600 rounded-full animate-spin;
  }

  .spinner-lg {
    @apply w-8 h-8 border-4;
  }

  /* Focus ring utility */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-brand-500 focus:ring-offset-2 dark:focus:ring-offset-gray-900;
  }

  /* Truncate text with tooltip */
  .truncate-tooltip {
    @apply truncate cursor-help;
  }

  /* Glass morphism effect */
  .glass-effect {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .glass-effect {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Gradient text */
  .gradient-text {
    @apply bg-gradient-to-r from-brand-600 to-brand-800 bg-clip-text text-transparent;
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-gray-200 dark:bg-gray-700 rounded;
  }

  /* Divider */
  .divider {
    @apply border-t border-gray-200 dark:border-gray-700;
  }

  .divider-vertical {
    @apply border-l border-gray-200 dark:border-gray-700;
  }

  /* Code blocks */
  .code-block {
    @apply bg-gray-100 dark:bg-gray-800 rounded-lg p-4 font-mono text-sm overflow-x-auto;
  }

  /* Badge variants */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-brand-100 text-brand-800 dark:bg-brand-900 dark:text-brand-200;
  }

  .badge-success {
    @apply bg-success-100 text-success-800 dark:bg-success-900 dark:text-success-200;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800 dark:bg-warning-900 dark:text-warning-200;
  }

  .badge-error {
    @apply bg-error-100 text-error-800 dark:bg-error-900 dark:text-error-200;
  }

  .badge-gray {
    @apply bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200;
  }

  /* Form validation states */
  .input-error {
    @apply border-error-500 focus:border-error-500 focus:ring-error-500;
  }

  .input-success {
    @apply border-success-500 focus:border-success-500 focus:ring-success-500;
  }

  .input-warning {
    @apply border-warning-500 focus:border-warning-500 focus:ring-warning-500;
  }

  /* Animation utilities */
  .animate-in {
    animation: animateIn 0.2s ease-out forwards;
  }

  .animate-out {
    animation: animateOut 0.2s ease-in forwards;
  }

  @keyframes animateIn {
    from {
      opacity: 0;
      transform: translateY(-8px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  @keyframes animateOut {
    from {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
    to {
      opacity: 0;
      transform: translateY(-8px) scale(0.95);
    }
  }

  /* Progress bar */
  .progress-bar {
    @apply w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2;
  }

  .progress-fill {
    @apply bg-brand-600 h-2 rounded-full transition-all duration-300 ease-out;
  }

  /* Hover states for interactive elements */
  .interactive {
    @apply transition-colors duration-200 hover:bg-gray-50 dark:hover:bg-gray-800;
  }

  .interactive-danger {
    @apply transition-colors duration-200 hover:bg-error-50 dark:hover:bg-error-900/20;
  }

  /* Data table styles */
  .data-table {
    @apply w-full border-collapse;
  }

  .data-table th {
    @apply bg-gray-50 dark:bg-gray-800 px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider;
  }

  .data-table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100;
  }

  .data-table tr {
    @apply border-b border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800/50;
  }
}

/* Utility classes */
@layer utilities {
  /* Accessibility utilities */
  .sr-only-focusable:not(:focus):not(:focus-within) {
    @apply sr-only;
  }

  /* Safe area utilities for mobile */
  .safe-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-right {
    padding-right: env(safe-area-inset-right);
  }

  /* Text balance for better typography */
  .text-balance {
    text-wrap: balance;
  }

  /* Smooth scrolling for sections */
  .scroll-smooth {
    scroll-behavior: smooth;
  }

  /* Debug utilities (development only) */
  .debug-grid {
    background-image: 
      linear-gradient(rgba(255, 0, 0, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .debug-outline * {
    outline: 1px solid red !important;
  }

  /* Performance utilities */
  .gpu-accelerated {
    transform: translateZ(0);
    will-change: transform;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  /* Layout utilities */
  .container-fluid {
    @apply w-full max-w-none px-4 sm:px-6 lg:px-8;
  }

  .container-narrow {
    @apply max-w-4xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .container-wide {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
} 