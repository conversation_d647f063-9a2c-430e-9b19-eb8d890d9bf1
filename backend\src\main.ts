import { NestFactory } from '@nestjs/core';
import { ConfigService } from '@nestjs/config';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { WsAdapter } from '@nestjs/platform-ws';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { AppModule } from './app.module';

async function bootstrap() {
  const logger = new Logger('Bootstrap');
  
  try {
    // Create NestJS application
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log', 'debug', 'verbose'],
    });

    // Get configuration service
    const configService = app.get(ConfigService);
    const port = configService.get<number>('PORT', 3001);
    const nodeEnv = configService.get<string>('NODE_ENV', 'development');
    const corsOrigins = configService.get<string>('CORS_ORIGINS', 'http://localhost:3000').split(',');

    // Security middleware
    app.use(helmet({
      crossOriginEmbedderPolicy: false,
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          scriptSrc: ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'", "wss:", "ws:"],
        },
      },
    }));

    // Compression
    app.use(compression());

    // Rate limiting
    app.use(rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000, // limit each IP to 1000 requests per windowMs
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false,
    }));

    // CORS configuration
    app.enableCors({
      origin: nodeEnv === 'production' ? corsOrigins : true,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Organization-ID'],
    });

    // Global prefix
    app.setGlobalPrefix('api/v1');

    // Global validation pipe
    app.useGlobalPipes(new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      disableErrorMessages: nodeEnv === 'production',
      validationError: {
        target: false,
        value: false,
      },
    }));

    // WebSocket adapter for real-time features
    app.useWebSocketAdapter(new WsAdapter(app));

    // Swagger documentation (only in development)
    if (nodeEnv !== 'production') {
      const config = new DocumentBuilder()
        .setTitle('SynapseAI Platform API')
        .setDescription('Production-ready SaaS platform for AI agents, tools, and hybrid workflows')
        .setVersion('1.0.0')
        .addBearerAuth()
        .addTag('Authentication', 'User authentication and authorization')
        .addTag('Vector Database', 'Vector database operations for RAG and knowledge management')
        .addTag('Agents', 'AI agent management and execution')
        .addTag('Tools', 'Tool creation and execution')
        .addTag('Workflows', 'Hybrid workflow management')
        .addTag('Analytics', 'Usage analytics and reporting')
        .addTag('Admin', 'Administrative operations')
        .build();

      const document = SwaggerModule.createDocument(app, config);
      SwaggerModule.setup('api/docs', app, document, {
        swaggerOptions: {
          persistAuthorization: true,
        },
      });

      logger.log(`Swagger documentation available at http://localhost:${port}/api/docs`);
    }

    // Graceful shutdown
    const signals = ['SIGTERM', 'SIGINT', 'SIGUSR2'];
    signals.forEach((signal) => {
      process.on(signal, async () => {
        logger.log(`Received ${signal}, shutting down gracefully...`);
        await app.close();
        process.exit(0);
      });
    });

    // Start the application
    await app.listen(port, '0.0.0.0');
    
    logger.log(`🚀 SynapseAI Platform API started successfully`);
    logger.log(`🌍 Environment: ${nodeEnv}`);
    logger.log(`🔗 Server running on: http://localhost:${port}/api/v1`);
    logger.log(`📚 Health check: http://localhost:${port}/api/v1/health`);
    
    if (nodeEnv !== 'production') {
      logger.log(`📖 API Documentation: http://localhost:${port}/api/docs`);
    }

  } catch (error) {
    logger.error('Failed to start application', error);
    process.exit(1);
  }
}

bootstrap(); 