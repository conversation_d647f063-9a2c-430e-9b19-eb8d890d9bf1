import { Injectable, Lo<PERSON>, OnModuleInit, OnModuleDestroy, Inject } from '@nestjs/common';
import Redis from 'ioredis';
import { RedisModuleOptions } from './redis.module';

/**
 * Redis Service
 * 
 * Provides Redis operations for:
 * - Session storage
 * - Caching
 * - Pub/Sub messaging
 * - Rate limiting
 * - Queue operations
 */
@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);
  private client: Redis | null = null;
  private subscriber: Redis | null = null;
  private publisher: Redis | null = null;

  constructor(
    @Inject('REDIS_OPTIONS')
    private readonly options: RedisModuleOptions,
  ) {}

  /**
   * Get keys matching a pattern
   */
  async keys(pattern: string): Promise<string[]> {
    if (!this.client) {
      throw new Error('Redis client not initialized');
    }
    return this.client.keys(pattern);
  }

  /**
   * Initialize Redis connections
   */
  async onModuleInit() {
    try {
      this.logger.log('Connecting to Redis...');

      // Main Redis client
      this.client = new Redis({
        host: this.options.host,
        port: this.options.port,
        password: this.options.password,
        db: this.options.db || 0,
        maxRetriesPerRequest: this.options.maxRetriesPerRequest || 3,
        lazyConnect: true,
      });

      // Dedicated subscriber client for pub/sub
      this.subscriber = new Redis({
        host: this.options.host,
        port: this.options.port,
        password: this.options.password,
        db: this.options.db || 0,
        lazyConnect: true,
      });

      // Dedicated publisher client for pub/sub
      this.publisher = new Redis({
        host: this.options.host,
        port: this.options.port,
        password: this.options.password,
        db: this.options.db || 0,
        lazyConnect: true,
      });

      // Connect all clients
      await Promise.all([
        this.client.connect(),
        this.subscriber.connect(),
        this.publisher.connect(),
      ]);

      // Test connection
      await this.client.ping();

      this.logger.log('✅ Redis connected successfully');

      // Setup error handlers
      this.setupErrorHandlers();

    } catch (error) {
      this.logger.error('❌ Failed to connect to Redis', error);
      throw error;
    }
  }

  /**
   * Clean up Redis connections
   */
  async onModuleDestroy() {
    try {
      this.logger.log('Disconnecting from Redis...');
      
      if (this.client) {
        await this.client.quit();
      }
      if (this.subscriber) {
        await this.subscriber.quit();
      }
      if (this.publisher) {
        await this.publisher.quit();
      }

      this.logger.log('✅ Redis disconnected successfully');
    } catch (error) {
      this.logger.error('❌ Error during Redis disconnection', error);
    }
  }

  /**
   * Setup error handlers for Redis clients
   */
  private setupErrorHandlers() {
    if (this.client) {
      this.client.on('error', (error) => {
        this.logger.error('Redis client error', error);
      });
      
      this.client.on('reconnecting', () => {
        this.logger.warn('Redis client reconnecting...');
      });
    }

    if (this.subscriber) {
      this.subscriber.on('error', (error) => {
        this.logger.error('Redis subscriber error', error);
      });
    }

    if (this.publisher) {
      this.publisher.on('error', (error) => {
        this.logger.error('Redis publisher error', error);
      });
    }
  }

  /**
   * Get the main Redis client
   */
  getClient(): Redis {
    if (!this.client) {
      throw new Error('Redis client not initialized');
    }
    return this.client;
  }

  /**
   * Get pub/sub client
   */
  getPubSubClient(): Redis {
    if (!this.subscriber) {
      throw new Error('Redis subscriber not initialized');
    }
    return this.subscriber;
  }

  /**
   * Get the subscriber client
   */
  getSubscriber(): Redis {
    if (!this.subscriber) {
      throw new Error('Redis subscriber not initialized');
    }
    return this.subscriber;
  }

  /**
   * Get the publisher client
   */
  getPublisher(): Redis {
    if (!this.publisher) {
      throw new Error('Redis publisher not initialized');
    }
    return this.publisher;
  }

  // === Session Management ===

  /**
   * Set session data
   */
  async setSession(sessionId: string, data: any, ttl: number = 3600): Promise<void> {
    await this.getClient().setex(`session:${sessionId}`, ttl, JSON.stringify(data));
  }

  /**
   * Get session data
   */
  async getSession<T = any>(sessionId: string): Promise<T | null> {
    const data = await this.getClient().get(`session:${sessionId}`);
    return data ? JSON.parse(data) : null;
  }

  /**
   * Delete session
   */
  async deleteSession(sessionId: string): Promise<void> {
    await this.getClient().del(`session:${sessionId}`);
  }

  /**
   * Extend session TTL
   */
  async extendSession(sessionId: string, ttl: number = 3600): Promise<void> {
    await this.getClient().expire(`session:${sessionId}`, ttl);
  }

  // === Caching ===

  /**
   * Set cache value
   */
  async set(key: string, value: any, ttl?: number): Promise<void> {
    const serialized = JSON.stringify(value);
    if (ttl) {
      await this.getClient().setex(key, ttl, serialized);
    } else {
      await this.getClient().set(key, serialized);
    }
  }

  /**
   * Get cache value
   */
  async get<T = any>(key: string): Promise<T | null> {
    const data = await this.getClient().get(key);
    return data ? JSON.parse(data) : null;
  }

  /**
   * Delete cache key
   */
  async del(key: string): Promise<void> {
    await this.getClient().del(key);
  }

  /**
   * Check if key exists
   */
  async exists(key: string): Promise<boolean> {
    const result = await this.getClient().exists(key);
    return result === 1;
  }

  /**
   * Set key expiration
   */
  async expire(key: string, ttl: number): Promise<void> {
    await this.getClient().expire(key, ttl);
  }

  // === Pub/Sub ===

  /**
   * Publish message
   */
  async publish(channel: string, message: any): Promise<void> {
    await this.getPublisher().publish(channel, JSON.stringify(message));
  }

  /**
   * Subscribe to channel
   */
  async subscribe(channel: string, callback: (message: any) => void): Promise<void> {
    await this.getSubscriber().subscribe(channel);
    this.getSubscriber().on('message', (receivedChannel, message) => {
      if (receivedChannel === channel) {
        try {
          const parsed = JSON.parse(message);
          callback(parsed);
        } catch (error) {
          this.logger.error('Failed to parse pub/sub message', error);
        }
      }
    });
  }

  /**
   * Unsubscribe from channel
   */
  async unsubscribe(channel: string): Promise<void> {
    await this.getSubscriber().unsubscribe(channel);
  }

  // === Rate Limiting ===

  /**
   * Implement sliding window rate limiting
   */
  async checkRateLimit(
    key: string,
    limit: number,
    windowMs: number
  ): Promise<{ allowed: boolean; remaining: number; resetTime: number }> {
    const now = Date.now();
    const window = Math.floor(now / windowMs);
    const windowKey = `rate_limit:${key}:${window}`;

    const client = this.getClient();
    const current = await client.incr(windowKey);
    
    if (current === 1) {
      await client.expire(windowKey, Math.ceil(windowMs / 1000));
    }

    const allowed = current <= limit;
    const remaining = Math.max(0, limit - current);
    const resetTime = (window + 1) * windowMs;

    return { allowed, remaining, resetTime };
  }

  // === Health Check ===

  /**
   * Health check for Redis connection
   */
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.getClient().ping();
      return result === 'PONG';
    } catch (error) {
      this.logger.error('Redis health check failed', error);
      return false;
    }
  }

  // === Utility Methods ===

  /**
   * Get Redis info
   */
  async getInfo(): Promise<string> {
    return await this.getClient().info();
  }

  /**
   * Flush all data (use with caution!)
   */
  async flushAll(): Promise<void> {
    await this.getClient().flushall();
  }
} 