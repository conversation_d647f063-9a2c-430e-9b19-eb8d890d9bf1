import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { RedisModule } from '../redis/redis.module';
import { ApixGateway } from './apix.gateway';
import { ApixService } from './apix.service';
import { AuthModule } from '../auth/auth.module';
import { PrismaModule } from '../prisma/prisma.module';

/**
 * APIX Real-Time Module
 * 
 * Provides WebSocket gateway for real-time communication
 * across all platform modules.
 */
@Module({
  imports: [
    ConfigModule,
    RedisModule,
    AuthModule,
    PrismaModule,
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'default-secret',
      signOptions: { expiresIn: '7d' },
    }),
  ],
  providers: [
    ApixGateway,
    ApixService,
  ],
  exports: [
    ApixService,
  ],
})
export class ApixModule {}
