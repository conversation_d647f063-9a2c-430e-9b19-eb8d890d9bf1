[{"C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\agents\\route.ts": "1", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\agents\\[id]\\clone\\route.ts": "2", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\agents\\[id]\\route.ts": "3", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\agents\\index.tsx": "4", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\agents\\page.tsx": "5", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\layout.tsx": "6", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\page.tsx": "7", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\layout.tsx": "8", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\analytics.tsx": "9", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\error-fallback.tsx": "10", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\auth.ts": "11", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\config.ts": "12", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\providers.tsx": "13", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\toast.tsx": "14", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\utils.ts": "15", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\toaster.tsx": "16", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\usetoast.ts": "17", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\toast.ts": "18", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\agents\\test\\route.ts": "19", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\templates\\route.ts": "20", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\tools\\route.ts": "21", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\tools\\[id]\\activate\\route.ts": "22", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\tools\\[id]\\analytics\\route.ts": "23", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\tools\\[id]\\deactivate\\route.ts": "24", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\tools\\[id]\\execute\\route.ts": "25", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\tools\\[id]\\route.ts": "26", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\auth\\login\\page.tsx": "27", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\auth\\register\\page.tsx": "28", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\agents\\new\\page.tsx": "29", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\agents\\[id]\\analytics\\page.tsx": "30", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\agents\\[id]\\execute\\page.tsx": "31", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\agents\\[id]\\page.tsx": "32", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\tools\\create\\page.tsx": "33", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\tools\\page.tsx": "34", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\tools\\[id]\\analytics\\page.tsx": "35", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\tools\\[id]\\edit\\page.tsx": "36", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\tools\\[id]\\page.tsx": "37", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\tools\\[id]\\test\\page.tsx": "38", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\page.tsx": "39", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\agents\\agent-analytics.tsx": "40", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\agents\\agent-builder.tsx": "41", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\agents\\agent-dashboard.tsx": "42", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\agents\\agent-execution.tsx": "43", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\tools\\tool-dashboard.tsx": "44", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\tools\\tool-form.tsx": "45", "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\usetemplate.ts": "46"}, {"size": 2336, "mtime": 1752948487876, "results": "47", "hashOfConfig": "48"}, {"size": 1329, "mtime": 1752947571018, "results": "49", "hashOfConfig": "48"}, {"size": 3563, "mtime": 1752947787297, "results": "50", "hashOfConfig": "48"}, {"size": 16328, "mtime": 1752949304563, "results": "51", "hashOfConfig": "48"}, {"size": 141, "mtime": 1752954298497, "results": "52", "hashOfConfig": "48"}, {"size": 4483, "mtime": 1752937380843, "results": "53", "hashOfConfig": "48"}, {"size": 9872, "mtime": 1752937165237, "results": "54", "hashOfConfig": "48"}, {"size": 2590, "mtime": 1752949169597, "results": "55", "hashOfConfig": "48"}, {"size": 793, "mtime": 1752948940026, "results": "56", "hashOfConfig": "48"}, {"size": 2099, "mtime": 1752928849950, "results": "57", "hashOfConfig": "48"}, {"size": 2621, "mtime": 1752948272079, "results": "58", "hashOfConfig": "48"}, {"size": 1063, "mtime": 1752948576195, "results": "59", "hashOfConfig": "48"}, {"size": 2981, "mtime": 1752949019362, "results": "60", "hashOfConfig": "48"}, {"size": 439, "mtime": 1752949091188, "results": "61", "hashOfConfig": "48"}, {"size": 1320, "mtime": 1752948230508, "results": "62", "hashOfConfig": "48"}, {"size": 446, "mtime": 1752948623042, "results": "63", "hashOfConfig": "48"}, {"size": 222, "mtime": 1752949521522, "results": "64", "hashOfConfig": "48"}, {"size": 1513, "mtime": 1752948847014, "results": "65", "hashOfConfig": "48"}, {"size": 1272, "mtime": 1752949766368, "results": "66", "hashOfConfig": "48"}, {"size": 1245, "mtime": 1752949786815, "results": "67", "hashOfConfig": "48"}, {"size": 1886, "mtime": 1752957640807, "results": "68", "hashOfConfig": "48"}, {"size": 1270, "mtime": 1752957697604, "results": "69", "hashOfConfig": "48"}, {"size": 1488, "mtime": 1752958348052, "results": "70", "hashOfConfig": "48"}, {"size": 1276, "mtime": 1752957723085, "results": "71", "hashOfConfig": "48"}, {"size": 1378, "mtime": 1752958102324, "results": "72", "hashOfConfig": "48"}, {"size": 2210, "mtime": 1752957672420, "results": "73", "hashOfConfig": "48"}, {"size": 10603, "mtime": 1752955627555, "results": "74", "hashOfConfig": "48"}, {"size": 27438, "mtime": 1752955713789, "results": "75", "hashOfConfig": "48"}, {"size": 150, "mtime": 1752954303605, "results": "76", "hashOfConfig": "48"}, {"size": 302, "mtime": 1752954389187, "results": "77", "hashOfConfig": "48"}, {"size": 302, "mtime": 1752954479882, "results": "78", "hashOfConfig": "48"}, {"size": 303, "mtime": 1752954310571, "results": "79", "hashOfConfig": "48"}, {"size": 126, "mtime": 1752957964810, "results": "80", "hashOfConfig": "48"}, {"size": 136, "mtime": 1752957618394, "results": "81", "hashOfConfig": "48"}, {"size": 20874, "mtime": 1752958321328, "results": "82", "hashOfConfig": "48"}, {"size": 294, "mtime": 1752957981612, "results": "83", "hashOfConfig": "48"}, {"size": 22777, "mtime": 1752958216305, "results": "84", "hashOfConfig": "48"}, {"size": 13908, "mtime": 1752958068419, "results": "85", "hashOfConfig": "48"}, {"size": 23341, "mtime": 1752959003423, "results": "86", "hashOfConfig": "48"}, {"size": 21016, "mtime": 1752954381873, "results": "87", "hashOfConfig": "48"}, {"size": 36641, "mtime": 1752953919004, "results": "88", "hashOfConfig": "48"}, {"size": 42518, "mtime": 1752954312750, "results": "89", "hashOfConfig": "48"}, {"size": 24845, "mtime": 1752954472000, "results": "90", "hashOfConfig": "48"}, {"size": 19094, "mtime": 1752957594757, "results": "91", "hashOfConfig": "48"}, {"size": 28210, "mtime": 1752958779247, "results": "92", "hashOfConfig": "48"}, {"size": 2675, "mtime": 1752949824439, "results": "93", "hashOfConfig": "48"}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1mxlxiw", {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\agents\\route.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\agents\\[id]\\clone\\route.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\agents\\[id]\\route.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\agents\\index.tsx", ["232"], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\agents\\page.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\layout.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\page.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\layout.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\analytics.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\error-fallback.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\auth.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\config.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\providers.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\toast.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\utils.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\toaster.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\usetoast.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\toast.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\agents\\test\\route.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\templates\\route.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\tools\\route.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\tools\\[id]\\activate\\route.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\tools\\[id]\\analytics\\route.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\tools\\[id]\\deactivate\\route.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\tools\\[id]\\execute\\route.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\api\\v1\\tools\\[id]\\route.ts", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\auth\\login\\page.tsx", ["233"], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\auth\\register\\page.tsx", ["234"], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\agents\\new\\page.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\agents\\[id]\\analytics\\page.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\agents\\[id]\\execute\\page.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\agents\\[id]\\page.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\tools\\create\\page.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\tools\\page.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\tools\\[id]\\analytics\\page.tsx", ["235", "236", "237"], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\tools\\[id]\\edit\\page.tsx", [], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\tools\\[id]\\page.tsx", ["238", "239", "240", "241"], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\dashboard\\tools\\[id]\\test\\page.tsx", ["242", "243", "244", "245"], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\app\\page.tsx", ["246", "247", "248"], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\agents\\agent-analytics.tsx", ["249"], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\agents\\agent-builder.tsx", ["250"], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\agents\\agent-dashboard.tsx", ["251", "252"], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\agents\\agent-execution.tsx", ["253"], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\tools\\tool-dashboard.tsx", ["254"], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\components\\tools\\tool-form.tsx", ["255"], [], "C:\\laragon\\www\\max\\cursorpro\\frontend\\src\\lib\\usetemplate.ts", [], [], {"ruleId": "256", "severity": 1, "message": "257", "line": 54, "column": 6, "nodeType": "258", "endLine": 54, "endColumn": 33, "suggestions": "259"}, {"ruleId": "260", "severity": 2, "message": "261", "line": 257, "column": 48, "nodeType": "262", "messageId": "263", "suggestions": "264"}, {"ruleId": "260", "severity": 2, "message": "261", "line": 553, "column": 24, "nodeType": "262", "messageId": "263", "suggestions": "265"}, {"ruleId": "256", "severity": 1, "message": "266", "line": 78, "column": 6, "nodeType": "258", "endLine": 78, "endColumn": 14, "suggestions": "267"}, {"ruleId": "260", "severity": 2, "message": "261", "line": 159, "column": 75, "nodeType": "262", "messageId": "263", "suggestions": "268"}, {"ruleId": "260", "severity": 2, "message": "261", "line": 159, "column": 96, "nodeType": "262", "messageId": "263", "suggestions": "269"}, {"ruleId": "256", "severity": 1, "message": "270", "line": 63, "column": 6, "nodeType": "258", "endLine": 63, "endColumn": 8, "suggestions": "271"}, {"ruleId": "260", "severity": 2, "message": "261", "line": 186, "column": 57, "nodeType": "262", "messageId": "263", "suggestions": "272"}, {"ruleId": "260", "severity": 2, "message": "261", "line": 186, "column": 78, "nodeType": "262", "messageId": "263", "suggestions": "273"}, {"ruleId": "260", "severity": 2, "message": "261", "line": 186, "column": 97, "nodeType": "262", "messageId": "263", "suggestions": "274"}, {"ruleId": "256", "severity": 1, "message": "270", "line": 58, "column": 6, "nodeType": "258", "endLine": 58, "endColumn": 8, "suggestions": "275"}, {"ruleId": "260", "severity": 2, "message": "261", "line": 165, "column": 57, "nodeType": "262", "messageId": "263", "suggestions": "276"}, {"ruleId": "260", "severity": 2, "message": "261", "line": 165, "column": 78, "nodeType": "262", "messageId": "263", "suggestions": "277"}, {"ruleId": "260", "severity": 2, "message": "261", "line": 165, "column": 97, "nodeType": "262", "messageId": "263", "suggestions": "278"}, {"ruleId": "260", "severity": 2, "message": "261", "line": 349, "column": 17, "nodeType": "262", "messageId": "263", "suggestions": "279"}, {"ruleId": "260", "severity": 2, "message": "280", "line": 412, "column": 19, "nodeType": "262", "messageId": "263", "suggestions": "281"}, {"ruleId": "260", "severity": 2, "message": "280", "line": 412, "column": 41, "nodeType": "262", "messageId": "263", "suggestions": "282"}, {"ruleId": "256", "severity": 1, "message": "283", "line": 107, "column": 6, "nodeType": "258", "endLine": 107, "endColumn": 26, "suggestions": "284"}, {"ruleId": "256", "severity": 1, "message": "285", "line": 156, "column": 6, "nodeType": "258", "endLine": 156, "endColumn": 21, "suggestions": "286"}, {"ruleId": "256", "severity": 1, "message": "257", "line": 122, "column": 6, "nodeType": "258", "endLine": 122, "endColumn": 71, "suggestions": "287"}, {"ruleId": "256", "severity": 1, "message": "257", "line": 133, "column": 6, "nodeType": "258", "endLine": 133, "endColumn": 19, "suggestions": "288"}, {"ruleId": "256", "severity": 1, "message": "289", "line": 94, "column": 6, "nodeType": "258", "endLine": 94, "endColumn": 15, "suggestions": "290"}, {"ruleId": "256", "severity": 1, "message": "291", "line": 70, "column": 6, "nodeType": "258", "endLine": 70, "endColumn": 60, "suggestions": "292"}, {"ruleId": "256", "severity": 1, "message": "270", "line": 95, "column": 6, "nodeType": "258", "endLine": 95, "endColumn": 25, "suggestions": "293"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAgents'. Either include it or remove the dependency array.", "ArrayExpression", ["294"], "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["295", "296", "297", "298"], ["299", "300", "301", "302"], "React Hook useEffect has a missing dependency: 'fetchToolAndAnalytics'. Either include it or remove the dependency array.", ["303"], ["304", "305", "306", "307"], ["308", "309", "310", "311"], "React Hook useEffect has a missing dependency: 'fetchTool'. Either include it or remove the dependency array.", ["312"], ["313", "314", "315", "316"], ["317", "318", "319", "320"], ["321", "322", "323", "324"], ["325"], ["326", "327", "328", "329"], ["330", "331", "332", "333"], ["334", "335", "336", "337"], ["338", "339", "340", "341"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["342", "343", "344", "345"], ["346", "347", "348", "349"], "React Hook useEffect has missing dependencies: 'fetchAgent' and 'fetchAnalytics'. Either include them or remove the dependency array.", ["350"], "React Hook useEffect has a missing dependency: 'loadAgent'. Either include it or remove the dependency array.", ["351"], ["352"], ["353"], "React Hook useEffect has missing dependencies: 'fetchAgent' and 'fetchSessions'. Either include them or remove the dependency array.", ["354"], "React Hook useEffect has a missing dependency: 'fetchTools'. Either include it or remove the dependency array.", ["355"], ["356"], {"desc": "357", "fix": "358"}, {"messageId": "359", "data": "360", "fix": "361", "desc": "362"}, {"messageId": "359", "data": "363", "fix": "364", "desc": "365"}, {"messageId": "359", "data": "366", "fix": "367", "desc": "368"}, {"messageId": "359", "data": "369", "fix": "370", "desc": "371"}, {"messageId": "359", "data": "372", "fix": "373", "desc": "362"}, {"messageId": "359", "data": "374", "fix": "375", "desc": "365"}, {"messageId": "359", "data": "376", "fix": "377", "desc": "368"}, {"messageId": "359", "data": "378", "fix": "379", "desc": "371"}, {"desc": "380", "fix": "381"}, {"messageId": "359", "data": "382", "fix": "383", "desc": "362"}, {"messageId": "359", "data": "384", "fix": "385", "desc": "365"}, {"messageId": "359", "data": "386", "fix": "387", "desc": "368"}, {"messageId": "359", "data": "388", "fix": "389", "desc": "371"}, {"messageId": "359", "data": "390", "fix": "391", "desc": "362"}, {"messageId": "359", "data": "392", "fix": "393", "desc": "365"}, {"messageId": "359", "data": "394", "fix": "395", "desc": "368"}, {"messageId": "359", "data": "396", "fix": "397", "desc": "371"}, {"desc": "398", "fix": "399"}, {"messageId": "359", "data": "400", "fix": "401", "desc": "362"}, {"messageId": "359", "data": "402", "fix": "403", "desc": "365"}, {"messageId": "359", "data": "404", "fix": "405", "desc": "368"}, {"messageId": "359", "data": "406", "fix": "407", "desc": "371"}, {"messageId": "359", "data": "408", "fix": "409", "desc": "362"}, {"messageId": "359", "data": "410", "fix": "411", "desc": "365"}, {"messageId": "359", "data": "412", "fix": "413", "desc": "368"}, {"messageId": "359", "data": "414", "fix": "415", "desc": "371"}, {"messageId": "359", "data": "416", "fix": "417", "desc": "362"}, {"messageId": "359", "data": "418", "fix": "419", "desc": "365"}, {"messageId": "359", "data": "420", "fix": "421", "desc": "368"}, {"messageId": "359", "data": "422", "fix": "423", "desc": "371"}, {"desc": "398", "fix": "424"}, {"messageId": "359", "data": "425", "fix": "426", "desc": "362"}, {"messageId": "359", "data": "427", "fix": "428", "desc": "365"}, {"messageId": "359", "data": "429", "fix": "430", "desc": "368"}, {"messageId": "359", "data": "431", "fix": "432", "desc": "371"}, {"messageId": "359", "data": "433", "fix": "434", "desc": "362"}, {"messageId": "359", "data": "435", "fix": "436", "desc": "365"}, {"messageId": "359", "data": "437", "fix": "438", "desc": "368"}, {"messageId": "359", "data": "439", "fix": "440", "desc": "371"}, {"messageId": "359", "data": "441", "fix": "442", "desc": "362"}, {"messageId": "359", "data": "443", "fix": "444", "desc": "365"}, {"messageId": "359", "data": "445", "fix": "446", "desc": "368"}, {"messageId": "359", "data": "447", "fix": "448", "desc": "371"}, {"messageId": "359", "data": "449", "fix": "450", "desc": "362"}, {"messageId": "359", "data": "451", "fix": "452", "desc": "365"}, {"messageId": "359", "data": "453", "fix": "454", "desc": "368"}, {"messageId": "359", "data": "455", "fix": "456", "desc": "371"}, {"messageId": "359", "data": "457", "fix": "458", "desc": "459"}, {"messageId": "359", "data": "460", "fix": "461", "desc": "462"}, {"messageId": "359", "data": "463", "fix": "464", "desc": "465"}, {"messageId": "359", "data": "466", "fix": "467", "desc": "468"}, {"messageId": "359", "data": "469", "fix": "470", "desc": "459"}, {"messageId": "359", "data": "471", "fix": "472", "desc": "462"}, {"messageId": "359", "data": "473", "fix": "474", "desc": "465"}, {"messageId": "359", "data": "475", "fix": "476", "desc": "468"}, {"desc": "477", "fix": "478"}, {"desc": "479", "fix": "480"}, {"desc": "481", "fix": "482"}, {"desc": "483", "fix": "484"}, {"desc": "485", "fix": "486"}, {"desc": "487", "fix": "488"}, {"desc": "489", "fix": "490"}, "Update the dependencies array to be: [currentPage, fetchAgents, statusFilter]", {"range": "491", "text": "492"}, "replaceWithAlt", {"alt": "493"}, {"range": "494", "text": "495"}, "Replace with `&apos;`.", {"alt": "496"}, {"range": "497", "text": "498"}, "Replace with `&lsquo;`.", {"alt": "499"}, {"range": "500", "text": "501"}, "Replace with `&#39;`.", {"alt": "502"}, {"range": "503", "text": "504"}, "Replace with `&rsquo;`.", {"alt": "493"}, {"range": "505", "text": "506"}, {"alt": "496"}, {"range": "507", "text": "508"}, {"alt": "499"}, {"range": "509", "text": "510"}, {"alt": "502"}, {"range": "511", "text": "512"}, "Update the dependencies array to be: [fetchToolAndAnalytics, period]", {"range": "513", "text": "514"}, {"alt": "493"}, {"range": "515", "text": "516"}, {"alt": "496"}, {"range": "517", "text": "518"}, {"alt": "499"}, {"range": "519", "text": "520"}, {"alt": "502"}, {"range": "521", "text": "522"}, {"alt": "493"}, {"range": "523", "text": "524"}, {"alt": "496"}, {"range": "525", "text": "526"}, {"alt": "499"}, {"range": "527", "text": "528"}, {"alt": "502"}, {"range": "529", "text": "530"}, "Update the dependencies array to be: [fetchTool]", {"range": "531", "text": "532"}, {"alt": "493"}, {"range": "533", "text": "534"}, {"alt": "496"}, {"range": "535", "text": "536"}, {"alt": "499"}, {"range": "537", "text": "538"}, {"alt": "502"}, {"range": "539", "text": "540"}, {"alt": "493"}, {"range": "541", "text": "542"}, {"alt": "496"}, {"range": "543", "text": "544"}, {"alt": "499"}, {"range": "545", "text": "546"}, {"alt": "502"}, {"range": "547", "text": "548"}, {"alt": "493"}, {"range": "549", "text": "550"}, {"alt": "496"}, {"range": "551", "text": "552"}, {"alt": "499"}, {"range": "553", "text": "554"}, {"alt": "502"}, {"range": "555", "text": "556"}, {"range": "557", "text": "532"}, {"alt": "493"}, {"range": "558", "text": "534"}, {"alt": "496"}, {"range": "559", "text": "536"}, {"alt": "499"}, {"range": "560", "text": "538"}, {"alt": "502"}, {"range": "561", "text": "540"}, {"alt": "493"}, {"range": "562", "text": "542"}, {"alt": "496"}, {"range": "563", "text": "544"}, {"alt": "499"}, {"range": "564", "text": "546"}, {"alt": "502"}, {"range": "565", "text": "548"}, {"alt": "493"}, {"range": "566", "text": "550"}, {"alt": "496"}, {"range": "567", "text": "552"}, {"alt": "499"}, {"range": "568", "text": "554"}, {"alt": "502"}, {"range": "569", "text": "556"}, {"alt": "493"}, {"range": "570", "text": "571"}, {"alt": "496"}, {"range": "572", "text": "573"}, {"alt": "499"}, {"range": "574", "text": "575"}, {"alt": "502"}, {"range": "576", "text": "577"}, {"alt": "578"}, {"range": "579", "text": "580"}, "Replace with `&quot;`.", {"alt": "581"}, {"range": "582", "text": "583"}, "Replace with `&ldquo;`.", {"alt": "584"}, {"range": "585", "text": "586"}, "Replace with `&#34;`.", {"alt": "587"}, {"range": "588", "text": "589"}, "Replace with `&rdquo;`.", {"alt": "578"}, {"range": "590", "text": "591"}, {"alt": "581"}, {"range": "592", "text": "593"}, {"alt": "584"}, {"range": "594", "text": "595"}, {"alt": "587"}, {"range": "596", "text": "597"}, "Update the dependencies array to be: [agentId, fetchAgent, fetchAnalytics, timeRange]", {"range": "598", "text": "599"}, "Update the dependencies array to be: [agentId, loadAgent, mode]", {"range": "600", "text": "601"}, "Update the dependencies array to be: [currentPage, statusFilter, categoryFilter, sortField, sortOrder, fetchAgents]", {"range": "602", "text": "603"}, "Update the dependencies array to be: [fetchAgents, searchQuery]", {"range": "604", "text": "605"}, "Update the dependencies array to be: [agentId, fetchAgent, fetchSessions]", {"range": "606", "text": "607"}, "Update the dependencies array to be: [search, status, type, sortBy, sortOrder, page, limit, fetchTools]", {"range": "608", "text": "609"}, "Update the dependencies array to be: [fetchTool, isEditing, toolId]", {"range": "610", "text": "611"}, [1379, 1406], "[currentPage, fetchAgents, statusFilter]", "&apos;", [9697, 9720], "Don&apos;t have an account? ", "&lsquo;", [9697, 9720], "Don&lsquo;t have an account? ", "&#39;", [9697, 9720], "Don&#39;t have an account? ", "&rsquo;", [9697, 9720], "Don&rsquo;t have an account? ", [23505, 23615], "\r\n                      I&apos;d like to receive product updates and marketing communications\r\n                    ", [23505, 23615], "\r\n                      I&lsquo;d like to receive product updates and marketing communications\r\n                    ", [23505, 23615], "\r\n                      I&#39;d like to receive product updates and marketing communications\r\n                    ", [23505, 23615], "\r\n                      I&rsquo;d like to receive product updates and marketing communications\r\n                    ", [1764, 1772], "[fetchToolAndAnalytics, period]", [4499, 4559], "The tool or analytics data you&apos;re looking for doesn't exist.", [4499, 4559], "The tool or analytics data you&lsquo;re looking for doesn't exist.", [4499, 4559], "The tool or analytics data you&#39;re looking for doesn't exist.", [4499, 4559], "The tool or analytics data you&rsquo;re looking for doesn't exist.", [4499, 4559], "The tool or analytics data you're looking for doesn&apos;t exist.", [4499, 4559], "The tool or analytics data you're looking for doesn&lsquo;t exist.", [4499, 4559], "The tool or analytics data you're looking for doesn&#39;t exist.", [4499, 4559], "The tool or analytics data you're looking for doesn&rsquo;t exist.", [1497, 1499], "[fetchTool]", [5293, 5366], "The tool you&apos;re looking for doesn't exist or you don't have access to it.", [5293, 5366], "The tool you&lsquo;re looking for doesn't exist or you don't have access to it.", [5293, 5366], "The tool you&#39;re looking for doesn't exist or you don't have access to it.", [5293, 5366], "The tool you&rsquo;re looking for doesn't exist or you don't have access to it.", [5293, 5366], "The tool you're looking for doesn&apos;t exist or you don't have access to it.", [5293, 5366], "The tool you're looking for doesn&lsquo;t exist or you don't have access to it.", [5293, 5366], "The tool you're looking for doesn&#39;t exist or you don't have access to it.", [5293, 5366], "The tool you're looking for doesn&rsquo;t exist or you don't have access to it.", [5293, 5366], "The tool you're looking for doesn't exist or you don&apos;t have access to it.", [5293, 5366], "The tool you're looking for doesn't exist or you don&lsquo;t have access to it.", [5293, 5366], "The tool you're looking for doesn't exist or you don&#39;t have access to it.", [5293, 5366], "The tool you're looking for doesn't exist or you don&rsquo;t have access to it.", [1425, 1427], [4808, 4881], [4808, 4881], [4808, 4881], [4808, 4881], [4808, 4881], [4808, 4881], [4808, 4881], [4808, 4881], [4808, 4881], [4808, 4881], [4808, 4881], [4808, 4881], [13764, 13867], "\r\n              We&apos;ve built the most comprehensive AI agent platform to help you succeed.\r\n            ", [13764, 13867], "\r\n              We&lsquo;ve built the most comprehensive AI agent platform to help you succeed.\r\n            ", [13764, 13867], "\r\n              We&#39;ve built the most comprehensive AI agent platform to help you succeed.\r\n            ", [13764, 13867], "\r\n              We&rsquo;ve built the most comprehensive AI agent platform to help you succeed.\r\n            ", "&quot;", [16363, 16384], "\r\n                  &quot;", "&ldquo;", [16363, 16384], "\r\n                  &ldquo;", "&#34;", [16363, 16384], "\r\n                  &#34;", "&rdquo;", [16363, 16384], "\r\n                  &rdquo;", [16405, 16424], "&quot;\r\n                ", [16405, 16424], "&ldquo;\r\n                ", [16405, 16424], "&#34;\r\n                ", [16405, 16424], "&rdquo;\r\n                ", [2174, 2194], "[agentId, fetchAgent, fetchAnalytics, timeRange]", [3912, 3927], "[agentId, loadAgent, mode]", [3027, 3092], "[currentPage, statusFilter, categoryFilter, sortField, sortOrder, fetchAgents]", [3334, 3347], "[fetchAgents, searchQuery]", [2078, 2087], "[agentId, fetchAgent, fetchSessions]", [1767, 1821], "[search, status, type, sortBy, sortOrder, page, limit, fetchTools]", [2288, 2307], "[fetchTool, isEditing, toolId]"]