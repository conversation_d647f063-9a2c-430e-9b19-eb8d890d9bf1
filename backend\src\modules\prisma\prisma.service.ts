import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaClient } from '.prisma/client';

/**
 * Prisma Database Service
 * 
 * Extends PrismaClient with lifecycle management and
 * production-ready configuration for the SynapseAI platform.
 */
@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PrismaService.name);
  
  constructor(private readonly configService: ConfigService) {
    const databaseUrl = configService.get<string>('DATABASE_URL');
    const nodeEnv = configService.get<string>('NODE_ENV', 'development');
    
    super({
      datasources: {
        db: {
          url: databaseUrl,
        },
      },
      log: nodeEnv === 'development' 
        ? ['query', 'info', 'warn', 'error']
        : ['warn', 'error'],
      errorFormat: nodeEnv === 'development' ? 'pretty' : 'minimal',
    });
  }

  /**
   * Connect to database on module initialization
   */
  async onModuleInit() {
    try {
      this.logger.log('Connecting to PostgreSQL database...');
      await this.$connect();
      this.logger.log('✅ Database connected successfully');
    } catch (error) {
      this.logger.error('❌ Failed to connect to database', error);
      throw error;
    }
    
    // Enable query logging in development
    if (this.configService.get<string>('NODE_ENV') === 'development') {
      this.$on('query' as never, (e: any) => {
        this.logger.debug(`Query: ${e.query}`);
        this.logger.debug(`Duration: ${e.duration}ms`);
      });
    }
  }

  /**
   * Disconnect from database on module destroy
   */
  async onModuleDestroy() {
    try {
      this.logger.log('Disconnecting from database...');
      await this.$disconnect();
      this.logger.log('✅ Database disconnected successfully');
    } catch (error) {
      this.logger.error('❌ Error during database disconnection', error);
    }
  }

  /**
   * Health check for database connectivity
   */
  async healthCheck(): Promise<boolean> {
    try {
      await this.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error('Database health check failed', error);
      return false;
    }
  }

  /**
   * Clean the database (useful for testing)
   * WARNING: This will delete all data!
   */
  async cleanDatabase() {
    if (this.configService.get<string>('NODE_ENV') === 'production') {
      throw new Error('Cannot clean database in production!');
    }

    // Get model names from this instance, filtering out non-model properties
    const models = Object.keys(this).filter((key) => {
      // Safely check if key is a valid string and represents a model
      return key && typeof key === 'string' && key.length > 0 && 
        key.charAt(0) !== '_' && // Not a private property
        key.charAt(0) === key.charAt(0).toLowerCase() && // Starts with lowercase
        key !== '$' && // Not a special Prisma property
        key !== 'user'; // Not the user accessor
    });

    for (const model of models) {
      try {
        await (this as any)[model].deleteMany();
        this.logger.log(`Cleaned model: ${model}`);
      } catch (error) {
        this.logger.error(`Failed to clean model ${model}`, error);
      }
    }
  }

  /**
   * Enable shutdown hooks
   */
  enableShutdownHooks(app: any) {
    this.$on('beforeExit' as never, async () => {
      await app.close();
    });
  }
} 