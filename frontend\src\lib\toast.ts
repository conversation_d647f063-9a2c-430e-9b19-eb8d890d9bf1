import { toast } from 'react-hot-toast';

/**
 * Show a success toast notification
 */
export function showSuccess(message: string): void {
  toast.success(message, {  
    duration: 3000,
    position: 'top-right',
    style: {
      background: '#10B981',
      color: '#FFFFFF',
      fontWeight: 'bold',
    },
  });
}

/**
 * Show an error toast notification
 */
export function showError(message: string): void {
  toast.error(message, {
    duration: 5000,
    position: 'top-right',
    style: {
      background: '#EF4444',
      color: '#FFFFFF',
      fontWeight: 'bold',
    },
  });
}

/**
 * Show an info toast notification
 */
export function showInfo(message: string): void {
  toast(message, {
    duration: 3000,
    position: 'top-right',
    style: {
      background: '#3B82F6',
      color: '#FFFFFF',
      fontWeight: 'bold',
    },
  });
}

/**
 * Show a warning toast notification
 */
export function showWarning(message: string): void {
  toast(message, {
    duration: 4000,
    position: 'top-right',
    style: {
      background: '#F59E0B',
      color: '#FFFFFF',
      fontWeight: 'bold',
    },
    icon: '⚠️',
  });
}

/**
 * Show a loading toast notification
 */
export function showLoading(message: string): void {
  toast.loading(message, {
    duration: 3000,
    position: 'top-right',
    style: {
      background: '#10B981',
      color: '#FFFFFF',
      fontWeight: 'bold',
    },
  });
} 