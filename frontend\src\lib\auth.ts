import { cookies } from 'next/headers';
import { jwtDecode } from 'jwt-decode';

/**
 * Session interface
 */
export interface Session {
  userId: string;
  email: string;
  name?: string | undefined;
  organizationId: string;
  role: string;
  accessToken: string;
  refreshToken?: string;
  expiresAt: number;
}

/**
 * Get the current session from cookies
 */
export async function getServerSession(): Promise<Session | null> {
  try {
    const cookieStore = cookies();
    const accessToken = cookieStore.get('accessToken')?.value;
    
    if (!accessToken) {
      return null;
    }
    
    // Decode the JWT token
    const decoded = jwtDecode<{
      sub: string;
      email: string;
      name?: string;
      organizationId: string;
      role: string;
      exp: number;
    }>(accessToken);
    
    return {
      userId: decoded.sub,
      email: decoded.email,
      name: decoded.name,
      organizationId: decoded.organizationId,
      role: decoded.role,
      accessToken,
      expiresAt: decoded.exp * 1000, // Convert to milliseconds
    };
    } catch (error) {
    console.error('Error getting session:', error);
    return null;
  }
}

/**
 * Get the current session from cookies
 */
export async function getSession(): Promise<Session | null> {
  try {
    const cookieStore = cookies();
    const accessToken = cookieStore.get('accessToken')?.value;
    
    if (!accessToken) {
      return null;
    }
    
    // Decode the JWT token
    const decoded = jwtDecode<{
      sub: string;
      email: string;
      name?: string;
      organizationId: string;
      role: string;
      exp: number;
    }>(accessToken);
    
    return {
      userId: decoded.sub,
      email: decoded.email,
      name: decoded.name,
      organizationId: decoded.organizationId,
      role: decoded.role,
      accessToken,
      expiresAt: decoded.exp * 1000, // Convert to milliseconds
    };
  } catch (error) {
    console.error('Error getting session:', error);
    return null;
  }
}

/**
 * Check if the user is authenticated
 */
export async function isAuthenticated(): Promise<boolean> {
  const session = await getSession();
  return !!session;
}

/**
 * Check if the user has the required role
 */
export async function hasRole(requiredRole: string | string[]): Promise<boolean> {
  const session = await getSession();
  if (!session) {
    return false;
  }
  
  const roles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];
  return roles.includes(session.role);
} 
