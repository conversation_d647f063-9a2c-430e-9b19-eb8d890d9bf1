import {
  VectorPoint,
  VectorSearchQuery,
  VectorSearchResponse,
  BatchUpsertOperation,
  BatchDeleteOperation,
  BatchOperationResult,
  IndexStats,
  VectorDatabaseMetrics,
  VectorDatabaseConfig
} from '@synapseai/shared/types/vector-database.types';

/**
 * Universal interface for vector database operations
 * Provides abstraction over different vector database providers (Pinecone, Weaviate, etc.)
 */
export interface IVectorDatabase {
  /**
   * Initialize the vector database connection and configuration
   */
  initialize(config: VectorDatabaseConfig): Promise<void>;

  /**
   * Check if the service is healthy and connected
   */
  healthCheck(): Promise<boolean>;

  /**
   * Get information about the current index
   */
  getIndexStats(namespace?: string): Promise<IndexStats>;

  /**
   * Create a new index with specified configuration
   */
  createIndex(
    indexName: string,
    dimension: number,
    metric?: 'cosine' | 'euclidean' | 'dotproduct',
    metadata?: Record<string, any>
  ): Promise<void>;

  /**
   * Delete an index
   */
  deleteIndex(indexName: string): Promise<void>;

  /**
   * Check if an index exists
   */
  indexExists(indexName: string): Promise<boolean>;

  /**
   * Upsert (insert or update) a single vector
   */
  upsert(vector: VectorPoint, namespace?: string): Promise<void>;

  /**
   * Upsert multiple vectors in batch
   */
  batchUpsert(operation: BatchUpsertOperation): Promise<BatchOperationResult>;

  /**
   * Search for similar vectors
   */
  search(query: VectorSearchQuery): Promise<VectorSearchResponse>;

  /**
   * Delete vectors by ID
   */
  delete(ids: string[], namespace?: string): Promise<void>;

  /**
   * Delete vectors matching a filter
   */
  deleteByFilter(filter: Record<string, any>, namespace?: string): Promise<void>;

  /**
   * Batch delete operation
   */
  batchDelete(operation: BatchDeleteOperation): Promise<BatchOperationResult>;

  /**
   * Fetch vectors by IDs
   */
  fetch(ids: string[], namespace?: string): Promise<VectorPoint[]>;

  /**
   * Update vector metadata without changing the vector values
   */
  updateMetadata(
    id: string,
    metadata: Record<string, any>,
    namespace?: string
  ): Promise<void>;

  /**
   * List all namespaces in the index
   */
  listNamespaces(): Promise<string[]>;

  /**
   * Get metrics and performance data
   */
  getMetrics(): Promise<VectorDatabaseMetrics>;

  /**
   * Close the connection
   */
  close(): Promise<void>;
}

/**
 * Interface for embedding generation services
 */
export interface IEmbeddingService {
  /**
   * Generate embeddings for a single text
   */
  generateEmbedding(text: string): Promise<number[]>;

  /**
   * Generate embeddings for multiple texts in batch
   */
  generateEmbeddings(texts: string[]): Promise<number[][]>;

  /**
   * Get the dimension of embeddings produced by this service
   */
  getDimension(): number;

  /**
   * Get the maximum token limit for input text
   */
  getMaxTokens(): number;

  /**
   * Check if the service is healthy and available
   */
  healthCheck(): Promise<boolean>;
}

/**
 * Interface for document processing and chunking
 */
export interface IDocumentProcessor {
  /**
   * Process a document and split it into chunks
   */
  processDocument(
    content: string,
    metadata: Record<string, any>
  ): Promise<{
    chunks: Array<{
      content: string;
      metadata: Record<string, any>;
      startIndex: number;
      endIndex: number;
    }>;
    totalTokens: number;
  }>;

  /**
   * Extract text from various file formats
   */
  extractText(
    buffer: Buffer, 
    mimeType: string,
    filename?: string
  ): Promise<string>;

  /**
   * Validate if a file type is supported
   */
  isSupported(mimeType: string): boolean;

  /**
   * Get optimal chunk size for the current configuration
   */
  getOptimalChunkSize(): number;
}

/**
 * Interface for hybrid search capabilities
 */
export interface IHybridSearch {
  /**
   * Perform hybrid search combining semantic and keyword search
   */
  hybridSearch(
    query: string,
    vectorQuery: VectorSearchQuery,
    alpha?: number // Balance between semantic (0) and keyword (1) search
  ): Promise<VectorSearchResponse>;

  /**
   * Index text for keyword search
   */
  indexKeywords(
    id: string,
    text: string,
    metadata?: Record<string, any>
  ): Promise<void>;

  /**
   * Remove keyword index for a document
   */
  removeKeywords(id: string): Promise<void>;
}

/**
 * Configuration interface for vector database module
 */
export interface VectorDatabaseModuleConfig {
  providers: {
    pinecone?: VectorDatabaseConfig;
    weaviate?: VectorDatabaseConfig;
    chroma?: VectorDatabaseConfig;
    qdrant?: VectorDatabaseConfig;
  };
  defaultProvider: 'pinecone' | 'weaviate' | 'chroma' | 'qdrant';
  embedding: {
    provider: 'openai' | 'huggingface' | 'cohere' | 'azure';
    config: Record<string, any>;
  };
  processing: {
    chunkSize: number;
    chunkOverlap: number;
    maxTokens: number;
    batchSize: number;
  };
  monitoring: {
    enabled: boolean;
    metricsInterval: number;
    healthCheckInterval: number;
  };
  cache: {
    enabled: boolean;
    ttl: number;
    maxSize: number;
  };
}

/**
 * Result interface for vector operations
 */
export interface VectorOperationResult {
  success: boolean;
  message: string;
  data?: any;
  error?: Error;
  metrics?: {
    duration: number;
    vectorsProcessed: number;
    tokensUsed: number;
    cost?: number;
  };
}

/**
 * Interface for vector database factory
 */
export interface IVectorDatabaseFactory {
  /**
   * Create a vector database instance for the specified provider
   */
  create(provider: string, config: VectorDatabaseConfig): Promise<IVectorDatabase>;

  /**
   * Get list of supported providers
   */
  getSupportedProviders(): string[];

  /**
   * Validate configuration for a specific provider
   */
  validateConfig(provider: string, config: VectorDatabaseConfig): boolean;
} 