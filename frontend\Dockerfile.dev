# Development Dockerfile for Next.js Frontend
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    curl \
    git \
    libc6-compat \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Copy package files for dependency installation
COPY package*.json ./
COPY frontend/package*.json ./frontend/
COPY shared/package*.json ./shared/

# Install dependencies with npm ci for faster builds
RUN npm ci --only=production --silent && \
    cd frontend && npm ci --silent && \
    cd ../shared && npm ci --silent

# Development stage
FROM base AS development

# Install development dependencies
RUN cd frontend && npm ci --silent && \
    cd ../shared && npm ci --silent

# Copy source code
COPY frontend/ ./frontend/
COPY shared/ ./shared/

# Copy TypeScript configurations
COPY tsconfig*.json ./
COPY frontend/tsconfig*.json ./frontend/
COPY shared/tsconfig*.json ./shared/

# Set working directory to frontend
WORKDIR /app/frontend

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001 -G nodejs

# Create .next directory with proper permissions
RUN mkdir -p .next && \
    chown -R nextjs:nodejs /app

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Start the application in development mode
CMD ["npm", "run", "dev"] 