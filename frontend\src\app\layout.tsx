import type { Metada<PERSON>, Viewport } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';
import { Providers } from '@/lib/providers';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: {
    default: 'SynapseAI - AI Agent Platform',
    template: '%s | SynapseAI',
  },
  description: 'Create AI agents, build tools, manage hybrid workflows, and more with SynapseAI platform.',
  keywords: [
    'AI',
    'Artificial Intelligence',
    'Agent',
    'Tool',
    'Hybrid',
    'Workflow',
    'Platform',
    'SaaS',
  ],
  authors: [
    {
      name: 'SynapseAI Team',
      url: 'https://synapseai.com',
    },
  ],
  creator: '<PERSON>yna<PERSON><PERSON><PERSON>',
  publisher: 'SynapseAI',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://synapseai.com'),
  alternates: {
    canonical: '/',
    languages: {
      'en-US': '/en-US',
    },
  },
  openGraph: {
    title: 'SynapseAI - AI Agent Platform',
    description: 'Create AI agents, build tools, manage hybrid workflows, and more with SynapseAI platform.',
    url: 'https://synapseai.com',
    siteName: 'SynapseAI',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'SynapseAI - AI Agent Platform',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'SynapseAI - AI Agent Platform',
    description: 'Create AI agents, build tools, manage hybrid workflows, and more with SynapseAI platform.',
    creator: '@synapseai',
    images: ['/twitter-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION || '',
  },
  category: 'technology',
};

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#0f172a' },
  ],
  width: 'device-width',
  initialScale: 1,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
} 