'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  <PERSON><PERSON>, 
  ArrowLeft, 
  BarChart2,
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  Clock,
  Zap,
  CheckCircle,
  XCircle,
  Calendar,
  Loader2,
  AlertCircle,
  Server,
  Code,
  Database,
  Cloud,
  MessageSquare,
} from 'lucide-react';
import { useToast } from '@/lib/usetoast';
import axios from 'axios';

interface Tool {
  id: string;
  name: string;
  description: string;
  status: string;
  type: string;
}

interface AnalyticsData {
  period: {
    startDate: string;
    endDate: string;
  };
  metrics: {
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    successRate: number;
    avgDuration: number;
    minDuration: number;
    maxDuration: number;
  };
  dailyExecutions: Array<{
    day: string;
    executions: number;
    successfulExecutions: number;
    failedExecutions: number;
  }>;
  userEngagement?: Array<{
    userId: string;
    executions: number;
    avgDuration: number;
  }>;
}

export default function ToolAnalyticsPage() {
  const params = useParams();
  const toolId = params.id as string;
  const router = useRouter();
  const { showError } = useToast();
  
  const [tool, setTool] = useState<Tool | null>(null);
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [period, setPeriod] = useState<'7d' | '30d' | '90d'>('30d');
  
  // Fetch tool data and analytics
  useEffect(() => {
    fetchToolAndAnalytics();
  }, [period]);
  
  // Fetch tool data and analytics from API
  const fetchToolAndAnalytics = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Get tool details
      const toolResponse = await axios.get(`/api/v1/tools/${toolId}`);
      setTool(toolResponse.data);
      
      // Calculate date range based on period
      const endDate = new Date();
      const startDate = new Date();
      
      switch (period) {
        case '7d':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(startDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(startDate.getDate() - 90);
          break;
      }
      
      // Get analytics data
      const analyticsResponse = await axios.get(`/api/v1/tools/${toolId}/analytics`, {
        params: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        },
      });
      
      setAnalytics(analyticsResponse.data);
    } catch (err: any) {
      console.error('Error fetching tool analytics:', err);
      setError(err.response?.data?.message || 'Failed to load tool analytics');
      showError('Failed to load tool analytics');
    } finally {
      setLoading(false);
    }
  };
  
  // Get tool type icon
  const getToolTypeIcon = (type: string) => {
    switch (type) {
      case 'API':
        return <Server className="w-5 h-5" />;
      case 'FUNCTION':
        return <Code className="w-5 h-5" />;
      case 'DATABASE':
        return <Database className="w-5 h-5" />;
      case 'STORAGE':
        return <Cloud className="w-5 h-5" />;
      case 'MESSAGING':
        return <MessageSquare className="w-5 h-5" />;
      default:
        return <Wrench className="w-5 h-5" />;
    }
  };
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <Loader2 className="w-12 h-12 text-blue-500 animate-spin mb-4" />
          <h2 className="text-xl font-medium text-white">Loading analytics...</h2>
        </div>
      </div>
    );
  }
  
  if (!tool || !analytics) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
          <h2 className="text-xl font-medium text-white mb-2">Data not found</h2>
          <p className="text-gray-400 mb-4">The tool or analytics data you're looking for doesn't exist.</p>
          <button
            onClick={() => router.push('/dashboard/tools')}
            className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-all"
          >
            Back to Tools
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-6xl mx-auto"
      >
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <button
              onClick={() => router.push(`/dashboard/tools/${toolId}`)}
              className="mr-4 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-all text-white"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <div>
              <div className="flex items-center">
                <div className="p-2 bg-white/10 rounded-lg mr-2">
                  {getToolTypeIcon(tool.type)}
                </div>
                <h1 className="text-3xl font-bold text-white">
                  {tool.name}
                </h1>
              </div>
              <p className="text-gray-400 mt-1">Analytics and Performance Metrics</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2 bg-white/10 rounded-lg p-1">
            <button
              onClick={() => setPeriod('7d')}
              className={`px-3 py-1.5 rounded-md text-sm ${
                period === '7d' 
                  ? 'bg-white/20 text-white' 
                  : 'text-gray-400 hover:bg-white/10'
              } transition-all`}
            >
              7 Days
            </button>
            <button
              onClick={() => setPeriod('30d')}
              className={`px-3 py-1.5 rounded-md text-sm ${
                period === '30d' 
                  ? 'bg-white/20 text-white' 
                  : 'text-gray-400 hover:bg-white/10'
              } transition-all`}
            >
              30 Days
            </button>
            <button
              onClick={() => setPeriod('90d')}
              className={`px-3 py-1.5 rounded-md text-sm ${
                period === '90d' 
                  ? 'bg-white/20 text-white' 
                  : 'text-gray-400 hover:bg-white/10'
              } transition-all`}
            >
              90 Days
            </button>
          </div>
        </div>
        
        {error && (
          <div className="p-4 bg-red-500/20 border border-red-500/40 rounded-lg mb-6 flex items-center gap-2 text-white">
            <AlertCircle className="w-5 h-5 flex-shrink-0" />
            <span>{error}</span>
          </div>
        )}
        
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400">Total Executions</span>
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <BarChart2 className="w-5 h-5 text-blue-400" />
              </div>
            </div>
            <div className="text-3xl font-bold text-white">
              {analytics.metrics.totalExecutions.toLocaleString()}
            </div>
            <div className="text-sm text-gray-400 mt-1">
              <Calendar className="w-3.5 h-3.5 inline mr-1" />
              {period === '7d' ? 'Last 7 days' : period === '30d' ? 'Last 30 days' : 'Last 90 days'}
            </div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400">Success Rate</span>
              <div className="p-2 bg-green-500/20 rounded-lg">
                <CheckCircle className="w-5 h-5 text-green-400" />
              </div>
            </div>
            <div className="text-3xl font-bold text-white">
              {analytics.metrics.successRate.toFixed(1)}%
            </div>
            <div className="flex items-center text-sm mt-1">
              <span className="text-green-400">{analytics.metrics.successfulExecutions.toLocaleString()}</span>
              <span className="text-gray-400 mx-1">successful /</span>
              <span className="text-red-400">{analytics.metrics.failedExecutions.toLocaleString()}</span>
              <span className="text-gray-400 ml-1">failed</span>
            </div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400">Avg Response Time</span>
              <div className="p-2 bg-yellow-500/20 rounded-lg">
                <Clock className="w-5 h-5 text-yellow-400" />
              </div>
            </div>
            <div className="text-3xl font-bold text-white">
              {analytics.metrics.avgDuration.toFixed(0)} ms
            </div>
            <div className="flex items-center text-sm text-gray-400 mt-1">
              <span>Min: {analytics.metrics.minDuration.toFixed(0)} ms</span>
              <span className="mx-1">•</span>
              <span>Max: {analytics.metrics.maxDuration.toFixed(0)} ms</span>
            </div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400">Performance Score</span>
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <Zap className="w-5 h-5 text-purple-400" />
              </div>
            </div>
            <div className="text-3xl font-bold text-white">
              {/* Calculate performance score based on success rate and response time */}
              {Math.min(
                100, 
                Math.round(
                  (analytics.metrics.successRate * 0.7) + 
                  (Math.max(0, 100 - (analytics.metrics.avgDuration / 10)) * 0.3)
                )
              )}
            </div>
            <div className="text-sm text-gray-400 mt-1">
              Based on success rate and response time
            </div>
          </div>
        </div>
        
        {/* Execution Trend */}
        <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6 mb-8">
          <h3 className="text-lg font-medium text-white mb-6 flex items-center gap-2">
            <LineChart className="w-5 h-5" />
            Execution Trend
          </h3>
          
          <div className="h-64 relative">
            {/* This is a simplified chart visualization - in a real app, use a chart library */}
            <div className="absolute inset-0 flex items-end">
              {analytics.dailyExecutions.map((day, index) => (
                <div 
                  key={index}
                  className="flex-1 flex flex-col items-center"
                  style={{ height: '100%' }}
                >
                  <div className="w-full px-1 flex flex-col items-center justify-end h-full">
                    <div 
                      className="w-full bg-red-500/50 rounded-t-sm"
                      style={{ 
                        height: `${(day.failedExecutions / Math.max(...analytics.dailyExecutions.map(d => d.executions))) * 100}%`,
                        minHeight: day.failedExecutions > 0 ? '4px' : '0'
                      }}
                    ></div>
                    <div 
                      className="w-full bg-green-500/50 rounded-t-sm"
                      style={{ 
                        height: `${(day.successfulExecutions / Math.max(...analytics.dailyExecutions.map(d => d.executions))) * 100}%`,
                        minHeight: day.successfulExecutions > 0 ? '4px' : '0'
                      }}
                    ></div>
                  </div>
                  <div className="text-xs text-gray-400 mt-2 whitespace-nowrap overflow-hidden text-ellipsis w-full text-center">
                    {new Date(day.day).toLocaleDateString(undefined, { month: 'short', day: 'numeric' })}
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div className="flex justify-center items-center mt-4 gap-6">
            <div className="flex items-center">
              <div className="w-3 h-3 bg-green-500/50 rounded-sm mr-2"></div>
              <span className="text-sm text-gray-400">Successful</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 bg-red-500/50 rounded-sm mr-2"></div>
              <span className="text-sm text-gray-400">Failed</span>
            </div>
          </div>
        </div>
        
        {/* Additional Metrics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Success/Failure Distribution */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <h3 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
              <PieChart className="w-5 h-5" />
              Success/Failure Distribution
            </h3>
            
            <div className="flex items-center justify-center py-6">
              <div className="relative w-48 h-48">
                {/* Simplified pie chart */}
                <svg viewBox="0 0 100 100" className="w-full h-full">
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    fill="transparent"
                    stroke="rgba(74, 222, 128, 0.5)"
                    strokeWidth="20"
                    strokeDasharray={`${analytics.metrics.successRate * 2.51} ${251 - analytics.metrics.successRate * 2.51}`}
                    transform="rotate(-90 50 50)"
                  />
                  <circle
                    cx="50"
                    cy="50"
                    r="40"
                    fill="transparent"
                    stroke="rgba(239, 68, 68, 0.5)"
                    strokeWidth="20"
                    strokeDasharray={`${(100 - analytics.metrics.successRate) * 2.51} ${251 - (100 - analytics.metrics.successRate) * 2.51}`}
                    strokeDashoffset={`-${analytics.metrics.successRate * 2.51}`}
                    transform="rotate(-90 50 50)"
                  />
                </svg>
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <span className="text-2xl font-bold text-white">{analytics.metrics.successRate.toFixed(1)}%</span>
                  <span className="text-sm text-gray-400">Success Rate</span>
                </div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mt-4">
              <div className="bg-white/5 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <CheckCircle className="w-4 h-4 text-green-500" />
                  <span className="text-sm text-gray-300">Successful</span>
                </div>
                <div className="text-xl font-medium text-white">
                  {analytics.metrics.successfulExecutions.toLocaleString()}
                </div>
              </div>
              
              <div className="bg-white/5 rounded-lg p-3">
                <div className="flex items-center gap-2 mb-1">
                  <XCircle className="w-4 h-4 text-red-500" />
                  <span className="text-sm text-gray-300">Failed</span>
                </div>
                <div className="text-xl font-medium text-white">
                  {analytics.metrics.failedExecutions.toLocaleString()}
                </div>
              </div>
            </div>
          </div>
          
          {/* Response Time Distribution */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <h3 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
              <Clock className="w-5 h-5" />
              Response Time
            </h3>
            
            <div className="space-y-6 py-4">
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-400">Average</span>
                  <span className="text-sm text-white">{analytics.metrics.avgDuration.toFixed(0)} ms</span>
                </div>
                <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-blue-500 rounded-full" 
                    style={{ width: `${Math.min(100, (analytics.metrics.avgDuration / 1000) * 100)}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-400">Minimum</span>
                  <span className="text-sm text-white">{analytics.metrics.minDuration.toFixed(0)} ms</span>
                </div>
                <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-green-500 rounded-full" 
                    style={{ width: `${Math.min(100, (analytics.metrics.minDuration / 1000) * 100)}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="flex justify-between mb-1">
                  <span className="text-sm text-gray-400">Maximum</span>
                  <span className="text-sm text-white">{analytics.metrics.maxDuration.toFixed(0)} ms</span>
                </div>
                <div className="h-2 bg-white/10 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-red-500 rounded-full" 
                    style={{ width: `${Math.min(100, (analytics.metrics.maxDuration / 1000) * 100)}%` }}
                  ></div>
                </div>
              </div>
            </div>
            
            <div className="mt-6 pt-4 border-t border-white/10">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-400">Response Time Rating</span>
                <div className="flex">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Zap 
                      key={i}
                      className={`w-4 h-4 ${
                        i < Math.ceil(5 - (analytics.metrics.avgDuration / 500))
                          ? 'text-yellow-400'
                          : 'text-gray-600'
                      }`}
                    />
                  ))}
                </div>
              </div>
              <div className="text-sm text-gray-400 mt-1">
                {analytics.metrics.avgDuration < 100
                  ? 'Excellent - Very fast response time'
                  : analytics.metrics.avgDuration < 300
                  ? 'Good - Fast response time'
                  : analytics.metrics.avgDuration < 500
                  ? 'Average - Acceptable response time'
                  : analytics.metrics.avgDuration < 1000
                  ? 'Slow - Consider optimization'
                  : 'Very slow - Needs immediate optimization'}
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
} 