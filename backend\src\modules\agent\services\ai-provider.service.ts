import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';

export interface AIProviderResponse {
  output: string;
  tokenUsage: {
    prompt: number;
    completion: number;
    total: number;
  };
  metadata: Record<string, any>;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

/**
 * AI Provider Service
 * 
 * Handles integration with multiple AI providers:
 * - OpenAI (GPT-3.5, GPT-4)
 * - Anthropic Claude (via API)
 * - Google Gemini
 * - Fallback and retry logic
 */
@Injectable()
export class AIProviderService {
  private readonly logger = new Logger(AIProviderService.name);
  private readonly openai: OpenAI;

  constructor(private readonly configService: ConfigService) {
    // Initialize OpenAI client
    const openaiApiKey = this.configService.get<string>('OPENAI_API_KEY');
    if (!openaiApiKey) {
      this.logger.warn('OpenAI API key not configured');
    }
    
    this.openai = new OpenAI({
      apiKey: openaiApiKey || 'sk-dummy-key',
    });
  }

  /**
   * Execute AI completion with the specified model and configuration
   */
  async executeCompletion(
    messages: ChatMessage[],
    config: {
      model: string;
      temperature: number;
      maxTokens: number;
      topP?: number;
      frequencyPenalty?: number;
      presencePenalty?: number;
      stopSequences?: string[];
    },
  ): Promise<AIProviderResponse> {
    try {
      // Route to appropriate provider based on model
      if (config.model.startsWith('gpt-')) {
        return await this.executeOpenAICompletion(messages, config);
      } else if (config.model.startsWith('claude-')) {
        return await this.executeClaudeCompletion(messages, config);
      } else if (config.model.startsWith('gemini-')) {
        return await this.executeGeminiCompletion(messages, config);
      } else {
        // Default to OpenAI for unknown models
        this.logger.warn(`Unknown model ${config.model}, falling back to GPT-3.5-Turbo`);
        return await this.executeOpenAICompletion(messages, {
          ...config,
          model: 'gpt-3.5-turbo',
        });
      }
    } catch (error) {
      this.logger.error(`AI provider execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw new BadRequestException(`AI provider execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute OpenAI completion
   */
  private async executeOpenAICompletion(
    messages: ChatMessage[],
    config: {
      model: string;
      temperature: number;
      maxTokens: number;
      topP?: number;
      frequencyPenalty?: number;
      presencePenalty?: number;
      stopSequences?: string[];
    },
  ): Promise<AIProviderResponse> {
    try {
      const startTime = Date.now();

      const completion = await this.openai.chat.completions.create({
        model: config.model,
        messages: messages.map(msg => ({
          role: msg.role,
          content: msg.content,
        })),
        temperature: config.temperature,
        max_tokens: config.maxTokens,
        top_p: config.topP,
        frequency_penalty: config.frequencyPenalty,
        presence_penalty: config.presencePenalty,
        stop: config.stopSequences,
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      if (!completion.choices || completion.choices.length === 0) {
        throw new Error('No completion choices returned from OpenAI');
      }

      const choice = completion.choices[0];
      if (!choice?.message?.content) {
        throw new Error('No content in completion choice');
      }

      const tokenUsage = completion.usage || {
        prompt_tokens: 0,
        completion_tokens: 0,
        total_tokens: 0,
      };

      return {
        output: choice.message.content,
        tokenUsage: {
          prompt: tokenUsage.prompt_tokens,
          completion: tokenUsage.completion_tokens,
          total: tokenUsage.total_tokens,
        },
        metadata: {
          provider: 'openai',
          model: config.model,
          duration,
          finishReason: choice.finish_reason,
          requestId: completion.id,
        },
      };
    } catch (error) {
      this.logger.error(`OpenAI API error: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw error;
    }
  }

  /**
   * Execute Claude completion (simulated for now)
   */
  private async executeClaudeCompletion(
    messages: ChatMessage[],
    config: {
      model: string;
      temperature: number;
      maxTokens: number;
    },
  ): Promise<AIProviderResponse> {
    // For now, simulate Claude response since we don't have API key
    // In production, this would use the Anthropic API
    const startTime = Date.now();
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    const userMessage = messages.filter(m => m.role === 'user').pop()?.content || '';
    const systemPrompt = messages.filter(m => m.role === 'system').pop()?.content || '';
    
    const simulatedResponse = `I'm Claude, an AI assistant created by Anthropic. I received your message: "${userMessage.substring(0, 100)}${userMessage.length > 100 ? '...' : ''}" and I'm operating with the following system prompt context: "${systemPrompt.substring(0, 100)}${systemPrompt.length > 100 ? '...' : ''}". How can I help you today?`;
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Estimate token usage
    const promptTokens = messages.reduce((acc, msg) => acc + Math.ceil(msg.content.length / 4), 0);
    const completionTokens = Math.ceil(simulatedResponse.length / 4);
    
    return {
      output: simulatedResponse,
      tokenUsage: {
        prompt: promptTokens,
        completion: completionTokens,
        total: promptTokens + completionTokens,
      },
      metadata: {
        provider: 'anthropic',
        model: config.model,
        duration,
        finishReason: 'stop',
        simulated: true,
      },
    };
  }

  /**
   * Execute Gemini completion (simulated for now)
   */
  private async executeGeminiCompletion(
    messages: ChatMessage[],
    config: {
      model: string;
      temperature: number;
      maxTokens: number;
    },
  ): Promise<AIProviderResponse> {
    // For now, simulate Gemini response since we don't have API key
    // In production, this would use the Google AI API
    const startTime = Date.now();
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 1500));
    
    const userMessage = messages.filter(m => m.role === 'user').pop()?.content || '';
    const systemPrompt = messages.filter(m => m.role === 'system').pop()?.content || '';
    
    const simulatedResponse = `Hello! I'm Gemini, Google's AI model. I understand you said: "${userMessage.substring(0, 100)}${userMessage.length > 100 ? '...' : ''}" and I'm following this system guidance: "${systemPrompt.substring(0, 100)}${systemPrompt.length > 100 ? '...' : ''}". I'm here to assist you with whatever you need!`;
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    // Estimate token usage
    const promptTokens = messages.reduce((acc, msg) => acc + Math.ceil(msg.content.length / 4), 0);
    const completionTokens = Math.ceil(simulatedResponse.length / 4);
    
    return {
      output: simulatedResponse,
      tokenUsage: {
        prompt: promptTokens,
        completion: completionTokens,
        total: promptTokens + completionTokens,
      },
      metadata: {
        provider: 'google',
        model: config.model,
        duration,
        finishReason: 'stop',
        simulated: true,
      },
    };
  }

  /**
   * Test AI provider connectivity
   */
  async testProvider(model: string): Promise<{ success: boolean; error?: string; latency?: number }> {
    try {
      const startTime = Date.now();
      
      const testMessages: ChatMessage[] = [
        { role: 'system', content: 'You are a helpful assistant.' },
        { role: 'user', content: 'Say "Hello" to test connectivity.' },
      ];

      const result = await this.executeCompletion(testMessages, {
        model,
        temperature: 0.1,
        maxTokens: 50,
      });

      const endTime = Date.now();
      const latency = endTime - startTime;

      return {
        success: true,
        latency,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Get available models for each provider
   */
  getAvailableModels(): Record<string, string[]> {
    return {
      openai: [
        'gpt-4',
        'gpt-4-turbo-preview',
        'gpt-3.5-turbo',
        'gpt-3.5-turbo-16k',
      ],
      anthropic: [
        'claude-3-opus',
        'claude-3-sonnet',
        'claude-3-haiku',
        'claude-2',
      ],
      google: [
        'gemini-pro',
        'gemini-pro-vision',
      ],
    };
  }

  /**
   * Validate model configuration
   */
  validateModelConfig(config: {
    model: string;
    temperature: number;
    maxTokens: number;
  }): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate temperature
    if (config.temperature < 0 || config.temperature > 2) {
      errors.push('Temperature must be between 0 and 2');
    }

    // Validate max tokens
    if (config.maxTokens < 1) {
      errors.push('Max tokens must be at least 1');
    }

    // Validate model-specific limits
    if (config.model.startsWith('gpt-3.5') && config.maxTokens > 4096) {
      errors.push('GPT-3.5 models have a maximum token limit of 4096');
    } else if (config.model === 'gpt-4' && config.maxTokens > 8192) {
      errors.push('GPT-4 has a maximum token limit of 8192');
    } else if (config.model.includes('16k') && config.maxTokens > 16384) {
      errors.push('16k models have a maximum token limit of 16384');
    }

    // Check if model exists
    const allModels = Object.values(this.getAvailableModels()).flat();
    if (!allModels.includes(config.model)) {
      errors.push(`Unknown model: ${config.model}`);
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}