import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { ToolConfiguration } from './tool.service';
import axios from 'axios';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  suggestions: string[];
}

export interface EndpointTestResult {
  success: boolean;
  statusCode?: number;
  responseTime?: number;
  error?: string;
  headers?: Record<string, string>;
}

/**
 * Tool Validation Service
 * 
 * Provides comprehensive validation for tools:
 * - Configuration validation
 * - Endpoint connectivity testing
 * - Authentication validation
 * - Parameter schema validation
 * - Security best practices checking
 */
@Injectable()
export class ToolValidationService {
  private readonly logger = new Logger(ToolValidationService.name);

  /**
   * Validate tool configuration
   */
  async validateToolConfiguration(config: ToolConfiguration): Promise<ValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const suggestions: string[] = [];

    try {
      // Basic configuration validation
      this.validateBasicConfig(config, errors, warnings);

      // Endpoint validation
      if (config.endpoint) {
        this.validateEndpoint(config.endpoint, errors, warnings);
      }

      // Authentication validation
      if (config.authentication) {
        this.validateAuthentication(config.authentication, errors, warnings, suggestions);
      }

      // Parameters validation
      if (config.parameters) {
        this.validateParameters(config.parameters, errors, warnings);
      }

      // Security validation
      this.validateSecurity(config, errors, warnings, suggestions);

      // Performance validation
      this.validatePerformance(config, warnings, suggestions);

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        suggestions,
      };
    } catch (error) {
      this.logger.error('Failed to validate tool configuration', error);
      return {
        isValid: false,
        errors: ['Validation failed due to internal error'],
        warnings: [],
        suggestions: [],
      };
    }
  }

  /**
   * Test endpoint connectivity
   */
  async testEndpoint(
    endpoint: string,
    method: string,
    headers?: Record<string, string>,
    authentication?: any,
    timeout = 10000,
  ): Promise<EndpointTestResult> {
    try {
      const startTime = Date.now();

      // Prepare request configuration
      const config: any = {
        method: method.toLowerCase(),
        url: endpoint,
        timeout,
        headers: {
          'User-Agent': 'SynapseAI-Tool-Validator/1.0',
          ...headers,
        },
        validateStatus: () => true, // Accept any status code
      };

      // Add authentication
      if (authentication) {
        this.addAuthenticationToRequest(config, authentication);
      }

      // Add minimal test data for POST/PUT requests
      if (['post', 'put', 'patch'].includes(method.toLowerCase())) {
        config.data = { test: true };
      }

      const response = await axios(config);
      const responseTime = Date.now() - startTime;

      return {
        success: response.status < 500,
        statusCode: response.status,
        responseTime,
        headers: response.headers as Record<string, string>,
      };

    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Unknown error',
        responseTime: Date.now() - Date.now(),
      };
    }
  }

  /**
   * Validate API key or token
   */
  async validateApiCredentials(
    endpoint: string,
    authType: string,
    credentials: Record<string, string>,
  ): Promise<{ valid: boolean; error?: string }> {
    try {
      const testConfig: any = {
        method: 'get',
        url: endpoint,
        timeout: 5000,
        headers: {
          'User-Agent': 'SynapseAI-Tool-Validator/1.0',
        },
        validateStatus: () => true,
      };

      // Add authentication
      this.addAuthenticationToRequest(testConfig, { type: authType, credentials });

      const response = await axios(testConfig);

      // Consider 401/403 as invalid credentials, 2xx as valid
      if (response.status === 401 || response.status === 403) {
        return { valid: false, error: 'Invalid credentials' };
      } else if (response.status >= 200 && response.status < 300) {
        return { valid: true };
      } else {
        return { valid: true }; // Other errors might not be auth-related
      }

    } catch (error: any) {
      // Network errors don't necessarily mean invalid credentials
      return { valid: true };
    }
  }

  /**
   * Validate basic configuration
   */
  private validateBasicConfig(
    config: ToolConfiguration,
    errors: string[],
    warnings: string[],
  ): void {
    // Method validation
    const validMethods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'HEAD', 'OPTIONS'];
    if (!validMethods.includes(config.method)) {
      errors.push(`Invalid HTTP method: ${config.method}`);
    }

    // Timeout validation
    if (config.timeout !== undefined) {
      if (config.timeout < 1000) {
        warnings.push('Timeout is very low (< 1s), may cause frequent failures');
      } else if (config.timeout > 300000) {
        warnings.push('Timeout is very high (> 5m), may cause long waits');
      }
    }

    // Headers validation
    if (config.headers) {
      Object.keys(config.headers).forEach(header => {
        if (header.toLowerCase() === 'authorization' && config.authentication) {
          warnings.push('Authorization header set manually while authentication is configured');
        }
      });
    }
  }

  /**
   * Validate endpoint URL
   */
  private validateEndpoint(endpoint: string, errors: string[], warnings: string[]): void {
    try {
      const url = new URL(endpoint);

      // Protocol validation
      if (!['http:', 'https:'].includes(url.protocol)) {
        errors.push('Only HTTP and HTTPS protocols are supported');
      }

      // HTTPS recommendation
      if (url.protocol === 'http:') {
        warnings.push('Consider using HTTPS for better security');
      }

      // Localhost/internal network detection
      if (url.hostname === 'localhost' || url.hostname.startsWith('127.') || url.hostname.startsWith('10.') || url.hostname.startsWith('192.168.')) {
        warnings.push('Endpoint appears to be on local/internal network');
      }

    } catch (error) {
      errors.push('Invalid endpoint URL format');
    }
  }

  /**
   * Validate authentication configuration
   */
  private validateAuthentication(
    auth: any,
    errors: string[],
    warnings: string[],
    suggestions: string[],
  ): void {
    switch (auth.type) {
      case 'API_KEY':
        if (!auth.credentials?.apiKey) {
          errors.push('API key is required for API_KEY authentication');
        }
        if (!auth.credentials?.headerName) {
          warnings.push('Header name not specified, will use default "X-API-Key"');
        }
        break;

      case 'BEARER_TOKEN':
        if (!auth.token) {
          errors.push('Token is required for BEARER_TOKEN authentication');
        }
        break;

      case 'BASIC_AUTH':
        if (!auth.username || !auth.password) {
          errors.push('Username and password are required for BASIC_AUTH');
        }
        warnings.push('Basic auth sends credentials in base64, consider using token-based auth');
        break;

      case 'OAUTH2':
        if (!auth.oauth?.clientId || !auth.oauth?.clientSecret) {
          errors.push('Client ID and secret are required for OAuth2');
        }
        if (!auth.oauth?.tokenUrl) {
          warnings.push('Token URL not specified for OAuth2');
        }
        break;

      case 'NONE':
        suggestions.push('Consider adding authentication for better security');
        break;

      default:
        errors.push(`Unsupported authentication type: ${auth.type}`);
    }
  }

  /**
   * Validate parameters
   */
  private validateParameters(parameters: any[], errors: string[], warnings: string[]): void {
    const paramNames = new Set<string>();

    parameters.forEach((param, index) => {
      // Name validation
      if (!param.name || typeof param.name !== 'string') {
        errors.push(`Parameter at index ${index} must have a valid name`);
        return;
      }

      // Duplicate name check
      if (paramNames.has(param.name)) {
        errors.push(`Duplicate parameter name: ${param.name}`);
      }
      paramNames.add(param.name);

      // Type validation
      const validTypes = ['string', 'number', 'boolean', 'object', 'array'];
      if (!validTypes.includes(param.type)) {
        errors.push(`Invalid parameter type for ${param.name}: ${param.type}`);
      }

      // Required validation
      if (typeof param.required !== 'boolean') {
        warnings.push(`Parameter ${param.name} should specify if it's required`);
      }

      // Validation rules
      if (param.validation) {
        this.validateParameterValidation(param, errors, warnings);
      }
    });
  }

  /**
   * Validate parameter validation rules
   */
  private validateParameterValidation(param: any, errors: string[], warnings: string[]): void {
    const validation = param.validation;

    // Type-specific validation
    if (param.type === 'string') {
      if (validation.min !== undefined && typeof validation.min !== 'number') {
        errors.push(`String parameter ${param.name}: min length must be a number`);
      }
      if (validation.max !== undefined && typeof validation.max !== 'number') {
        errors.push(`String parameter ${param.name}: max length must be a number`);
      }
      if (validation.pattern && typeof validation.pattern !== 'string') {
        errors.push(`String parameter ${param.name}: pattern must be a string`);
      }
    }

    if (param.type === 'number') {
      if (validation.min !== undefined && typeof validation.min !== 'number') {
        errors.push(`Number parameter ${param.name}: min value must be a number`);
      }
      if (validation.max !== undefined && typeof validation.max !== 'number') {
        errors.push(`Number parameter ${param.name}: max value must be a number`);
      }
    }

    // Enum validation
    if (validation.enum && !Array.isArray(validation.enum)) {
      errors.push(`Parameter ${param.name}: enum must be an array`);
    }
  }

  /**
   * Validate security best practices
   */
  private validateSecurity(
    config: ToolConfiguration,
    errors: string[],
    warnings: string[],
    suggestions: string[],
  ): void {
    // Check for sensitive data in headers
    if (config.headers) {
      Object.entries(config.headers).forEach(([key, value]) => {
        if (key.toLowerCase().includes('secret') || key.toLowerCase().includes('password')) {
          warnings.push(`Sensitive data detected in header: ${key}`);
        }
        if (typeof value === 'string' && value.length > 100) {
          warnings.push(`Very long header value for ${key}, might contain sensitive data`);
        }
      });
    }

    // Rate limiting recommendations
    if (!config.rateLimit) {
      suggestions.push('Consider adding rate limiting to prevent abuse');
    }

    // Retry configuration
    if (!config.retry || config.retry.maxRetries === 0) {
      suggestions.push('Consider adding retry logic for better reliability');
    }

    // Timeout recommendations
    if (!config.timeout) {
      suggestions.push('Consider setting a timeout to prevent hanging requests');
    }
  }

  /**
   * Validate performance configuration
   */
  private validatePerformance(
    config: ToolConfiguration,
    warnings: string[],
    suggestions: string[],
  ): void {
    // Caching recommendations
    if (!config.caching || !config.caching.enabled) {
      suggestions.push('Consider enabling caching for better performance');
    }

    // Retry configuration performance impact
    if (config.retry && config.retry.maxRetries > 5) {
      warnings.push('High retry count may impact performance');
    }

    // Rate limiting performance
    if (config.rateLimit) {
      if (config.rateLimit.requestsPerMinute > 1000) {
        warnings.push('Very high rate limit may impact system performance');
      }
    }
  }

  /**
   * Add authentication to request
   */
  private addAuthenticationToRequest(config: any, auth: any): void {
    switch (auth.type) {
      case 'API_KEY':
        const headerName = auth.credentials?.headerName || 'X-API-Key';
        config.headers[headerName] = auth.credentials?.apiKey;
        break;

      case 'BEARER_TOKEN':
        config.headers['Authorization'] = `Bearer ${auth.token}`;
        break;

      case 'BASIC_AUTH':
        config.auth = {
          username: auth.username,
          password: auth.password,
        };
        break;

      case 'OAUTH2':
        if (auth.token) {
          config.headers['Authorization'] = `Bearer ${auth.token}`;
        }
        break;
    }
  }
}