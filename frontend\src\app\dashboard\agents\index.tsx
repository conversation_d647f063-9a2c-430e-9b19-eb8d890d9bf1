'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Plus, 
  Search, 
  Filter, 
  Brain,
  MoreVertical,
  Play,
  Edit,
  Trash2,
  Co<PERSON>,
  Loader2,
  AlertCircle,
  CheckCircle2,
  XCircle
} from 'lucide-react';  
import { useRouter } from 'next/navigation';
import axios from 'axios';
import { useToast } from '@/lib/usetoast';

interface Agent {
  id: string;
  name: string;
  description: string;
  status: string;
  model: string;
  temperature: number;
  createdAt: string;
  updatedAt: string;
  executionCount?: number;
  successRate?: number;
  avgResponseTime?: number;
}

export default function AgentsPage() {
  const router = useRouter();
  const [agents, setAgents] = useState<Agent[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { showSuccess, showError } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalAgents, setTotalAgents] = useState(0);
  const [agentsPerPage] = useState(10);
  
  const [actionMenuOpen, setActionMenuOpen] = useState<string | null>(null);

  useEffect(() => {
    fetchAgents();
  }, [currentPage, statusFilter]);

  const fetchAgents = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams();
      params.append('limit', agentsPerPage.toString());
      params.append('offset', ((currentPage - 1) * agentsPerPage).toString());
      
      if (searchQuery) {
        params.append('search', searchQuery);
      }
      
      if (statusFilter) {
        params.append('status', statusFilter);
      }
      
      const response = await axios.get(`/api/v1/agents?${params.toString()}`);
      setAgents(response.data.agents);
      setTotalAgents(response.data.total);
    } catch (err: any) {
      console.error('Error fetching agents:', err);
      setError(err.response?.data?.message || 'Failed to fetch agents');
      showError('Failed to load agents');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSearch = () => {
    setCurrentPage(1);
    fetchAgents();
  };

  const handleDeleteAgent = async (agentId: string) => {
    if (!confirm('Are you sure you want to delete this agent?')) {
      return;
    }
    
    try {
      await axios.delete(`/api/v1/agents/${agentId}`);
      showSuccess('Agent deleted successfully');
      fetchAgents();
    } catch (err: any) {
      console.error('Error deleting agent:', err);
      showError(err.response?.data?.message || 'Failed to delete agent');
    }
  };

  const handleDuplicateAgent = async (agentId: string) => {
    try {
      const agent = agents.find(a => a.id === agentId);
      if (!agent) return;
      
      const response = await axios.post(`/api/v1/agents/${agentId}/clone`, {
        name: `${agent.name} (Copy)`
      });
      
      showSuccess('Agent duplicated successfully');
      router.push(`/dashboard/agents/${response.data.id}`);
    } catch (err: any) {
      console.error('Error duplicating agent:', err);
      showError(err.response?.data?.message || 'Failed to duplicate agent');
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return (
          <span className="px-2 py-1 bg-green-500/20 text-green-400 rounded-full text-xs font-medium flex items-center gap-1">
            <CheckCircle2 className="w-3 h-3" />
            Active
          </span>
        );
      case 'INACTIVE':
        return (
          <span className="px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded-full text-xs font-medium flex items-center gap-1">
            <XCircle className="w-3 h-3" />
            Inactive
          </span>
        );
      case 'DRAFT':
        return (
          <span className="px-2 py-1 bg-blue-500/20 text-blue-400 rounded-full text-xs font-medium flex items-center gap-1">
            <Edit className="w-3 h-3" />
            Draft
          </span>
        );
      default:
        return (
          <span className="px-2 py-1 bg-gray-500/20 text-gray-400 rounded-full text-xs font-medium">
            {status}
          </span>
        );
    }
  };

  const totalPages = Math.ceil(totalAgents / agentsPerPage);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-7xl mx-auto"
      >
        <div className="flex justify-between items-center mb-8">
          <h1 className="text-4xl font-bold text-white">
            Agents
          </h1>
          <button 
            onClick={() => router.push('/dashboard/agents/new')}
            className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white flex items-center gap-2 hover:opacity-90 transition-all"
          >
            <Plus className="w-5 h-5" />
            New Agent
          </button>
        </div>

        {/* Search and Filter */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search agents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
              className="w-full pl-10 pr-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40"
            />
          </div>
          <div className="flex gap-2">
            <select
              value={statusFilter || ''}
              onChange={(e) => setStatusFilter(e.target.value || null)}
              className="px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
            >
              <option value="">All Status</option>
              <option value="ACTIVE">Active</option>
              <option value="INACTIVE">Inactive</option>
              <option value="DRAFT">Draft</option>
            </select>
            <button
              onClick={handleSearch}
              className="px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all"
            >
              <Filter className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <div className="mb-6 p-4 bg-red-500/20 border border-red-500/40 rounded-lg flex items-center gap-2 text-white">
            <AlertCircle className="w-5 h-5" />
            <span>{error}</span>
          </div>
        )}

        {/* Loading State */}
        {isLoading ? (
          <div className="flex justify-center items-center py-20">
            <Loader2 className="w-10 h-10 text-white animate-spin" />
          </div>
        ) : agents.length === 0 ? (
          <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-12 text-center">
            <Brain className="w-16 h-16 text-white/40 mx-auto mb-4" />
            <h2 className="text-2xl font-semibold text-white mb-2">No agents found</h2>
            <p className="text-gray-400 mb-6">
              {searchQuery || statusFilter
                ? "No agents match your search criteria. Try adjusting your filters."
                : "You haven't created any agents yet. Get started by creating your first agent."}
            </p>
            <button
              onClick={() => router.push('/dashboard/agents/new')}
              className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white flex items-center gap-2 mx-auto hover:opacity-90 transition-all"
            >
              <Plus className="w-5 h-5" />
              Create Your First Agent
            </button>
          </div>
        ) : (
          <>
            {/* Agents Table */}
            <div className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-white/20">
                      <th className="text-left p-4 text-gray-400 font-medium">Name</th>
                      <th className="text-left p-4 text-gray-400 font-medium">Status</th>
                      <th className="text-left p-4 text-gray-400 font-medium">Model</th>
                      <th className="text-left p-4 text-gray-400 font-medium">Created</th>
                      <th className="text-left p-4 text-gray-400 font-medium">Stats</th>
                      <th className="text-right p-4 text-gray-400 font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {agents.map((agent) => (
                      <tr
                        key={agent.id}
                        className="border-b border-white/10 hover:bg-white/5 transition-colors"
                      >
                        <td className="p-4">
                          <div className="flex items-center gap-3">
                            <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg">
                              <Brain className="w-5 h-5 text-white" />
                            </div>
                            <div>
                              <h3 className="text-white font-medium">{agent.name}</h3>
                              <p className="text-gray-400 text-sm truncate max-w-xs">
                                {agent.description || 'No description'}
                              </p>
                            </div>
                          </div>
                        </td>
                        <td className="p-4">
                          {getStatusBadge(agent.status)}
                        </td>
                        <td className="p-4 text-white">
                          {agent.model}
                        </td>
                        <td className="p-4 text-white">
                          {new Date(agent.createdAt).toLocaleDateString()}
                        </td>
                        <td className="p-4">
                          <div className="text-sm">
                            <div className="text-gray-400">
                              {agent.executionCount !== undefined ? (
                                <span>{agent.executionCount} executions</span>
                              ) : (
                                <span>No executions</span>
                              )}
                            </div>
                            {agent.successRate !== undefined && (
                              <div className="text-gray-400">
                                {agent.successRate.toFixed(1)}% success rate
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="p-4 text-right">
                          <div className="relative inline-block">
                            <button
                              onClick={() => setActionMenuOpen(actionMenuOpen === agent.id ? null : agent.id)}
                              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
                            >
                              <MoreVertical className="w-5 h-5 text-white" />
                            </button>
                            {actionMenuOpen === agent.id && (
                              <div className="absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg border border-white/20 z-10">
                                <button
                                  onClick={() => {
                                    setActionMenuOpen(null);
                                    router.push(`/dashboard/agents/${agent.id}`);
                                  }}
                                  className="w-full text-left px-4 py-2 text-white hover:bg-white/10 flex items-center gap-2"
                                >
                                  <Edit className="w-4 h-4" />
                                  Edit
                                </button>
                                <button
                                  onClick={() => {
                                    setActionMenuOpen(null);
                                    router.push(`/dashboard/agents/${agent.id}/execute`);
                                  }}
                                  className="w-full text-left px-4 py-2 text-white hover:bg-white/10 flex items-center gap-2"
                                >
                                  <Play className="w-4 h-4" />
                                  Execute
                                </button>
                                <button
                                  onClick={() => {
                                    setActionMenuOpen(null);
                                    handleDuplicateAgent(agent.id);
                                  }}
                                  className="w-full text-left px-4 py-2 text-white hover:bg-white/10 flex items-center gap-2"
                                >
                                  <Copy className="w-4 h-4" />
                                  Duplicate
                                </button>
                                <button
                                  onClick={() => {
                                    setActionMenuOpen(null);
                                    handleDeleteAgent(agent.id);
                                  }}
                                  className="w-full text-left px-4 py-2 text-red-400 hover:bg-white/10 flex items-center gap-2"
                                >
                                  <Trash2 className="w-4 h-4" />
                                  Delete
                                </button>
                              </div>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Pagination */}
            {totalPages > 1 && (
              <div className="flex justify-between items-center mt-6">
                <div className="text-gray-400">
                  Showing {((currentPage - 1) * agentsPerPage) + 1} to {Math.min(currentPage * agentsPerPage, totalAgents)} of {totalAgents} agents
                </div>
                <div className="flex gap-2">
                  <button
                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                    disabled={currentPage === 1}
                    className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all disabled:opacity-50"
                  >
                    Previous
                  </button>
                  <button
                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                    disabled={currentPage === totalPages}
                    className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all disabled:opacity-50"
                  >
                    Next
                  </button>
                </div>
              </div>
            )}
          </>
        )}
      </motion.div>
    </div>
  );
} 