'use client';

import { useEffect } from 'react';
import { usePathname, useSearchParams } from 'next/navigation';

// Define window.gtag for TypeScript
declare global {
  interface Window {
    gtag?: (command: string, id: string, params?: any) => void;
  }
}

/**
 * Simple analytics component
 */
export function Analytics() {
  const pathname = usePathname();
  const searchParams = useSearchParams();

  // Track page views
  useEffect(() => {
    if (process.env.NODE_ENV !== 'production') {
      return;
    }

    const url = pathname + (searchParams?.toString() ? `?${searchParams.toString()}` : '');
    console.log(`[Analytics] Page view: ${url}`);
    
    // Add your analytics implementation here
  }, [pathname, searchParams]);

  return null;
} 