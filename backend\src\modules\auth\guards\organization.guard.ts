import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

/**
 * Organization Guard
 * 
 * Production-ready multi-tenant guard that:
 * - Ensures users can only access their organization's data
 * - Validates organization ID from request params/body/query
 * - Prevents cross-organization data access
 * - Must be used after AuthGuard
 */
@Injectable()
export class OrganizationGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.organizationId) {
      throw new ForbiddenException('User organization not found');
    }

    // Get organization ID from various sources
    const params = request.params;
    const body = request.body;
    const query = request.query;

    // Check if any organization ID is provided in the request
    const requestOrgId = 
      params?.organizationId || 
      body?.organizationId || 
      query?.organizationId ||
      // Also check for nested organizationId in resources
      body?.data?.organizationId ||
      body?.agent?.organizationId ||
      body?.tool?.organizationId;

    // If no organization ID in request, it's likely a user-scoped request
    if (!requestOrgId) {
      // For routes that don't explicitly require organization ID,
      // we'll inject the user's organization ID
      if (body) {
        body.organizationId = user.organizationId;
      }
      return true;
    }

    // Validate that the requested organization matches user's organization
    if (requestOrgId !== user.organizationId) {
      // Super admins can access any organization
      if (user.role === 'SUPER_ADMIN') {
        return true;
      }

      throw new ForbiddenException(
        'Access denied. You cannot access resources from another organization.'
      );
    }

    return true;
  }
} 