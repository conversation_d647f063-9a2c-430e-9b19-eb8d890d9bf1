import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { RedisService } from '../../redis/redis.service';

// Types needed by session-memory.service
export interface SessionMemory {
  sessionId: string;
  messages: Message[];
  totalTokens: number;
  lastActivity: Date;
  maxTokens?: number; // Add missing property
}

export interface Message {
  id: string;
  role: 'system' | 'user' | 'assistant' | 'tool';
  content: string;
  tokens?: number;
  metadata?: any;
  createdAt: Date;
  timestamp?: Date; // Add missing property
}

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly redisService: RedisService,
  ) {}

  async createSession(
    userId: string,
    organizationId: string,
    agentId?: string,
    metadata: Record<string, any> = {},
  ): Promise<any> {
    try {
      const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      const session = await this.prismaService.session.create({
        data: {
          sessionId,
          userId,
          organizationId,
          metadata: {
            ...metadata,
            agentId, // Store agentId in metadata since it's not in schema
          },
          status: 'ACTIVE',
          expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
        },
      });

      return session;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to create session: ${err.message}`, err.stack);
      throw error;
    }
  }

  async getSession(sessionId: string, userId?: string, organizationId?: string): Promise<any> {
    try {
      const whereClause: any = { sessionId };
      if (userId) whereClause.userId = userId;
      if (organizationId) whereClause.organizationId = organizationId;

      const session = await this.prismaService.session.findFirst({
        where: whereClause,
        // Remove messages include since it doesn't exist in schema
      });

      if (!session) {
        throw new NotFoundException('Session not found');
      }

      return session;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to get session: ${err.message}`, err.stack);
      throw error;
    }
  }

  async updateSession(
    sessionId: string,
    userId: string,
    organizationId: string,
    data: any,
  ): Promise<any> {
    try {
      const session = await this.prismaService.session.update({
        where: { sessionId },
        data,
      });

      return session;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to update session: ${err.message}`, err.stack);
      throw error;
    }
  }

  async deleteSession(sessionId: string, userId?: string, organizationId?: string): Promise<void> {
    try {
      await this.prismaService.session.delete({
        where: { sessionId },
      });

      this.logger.log(`Session deleted: ${sessionId}`);
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to delete session: ${err.message}`, err.stack);
      throw error;
    }
  }

  async addMessage(sessionId: string, message: any): Promise<any> {
    try {
      const updatedSession = await this.prismaService.session.update({
        where: { sessionId },
        data: {
          metadata: message,
        },
      });
      return updatedSession;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to add message: ${err.message}`, err.stack);
      throw error;
    }
  }

  async getUserSessions(userId: string): Promise<any[]> {
    try {
      const sessions = await this.prismaService.session.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
      });
      return sessions;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to get user sessions: ${err.message}`, err.stack);
      throw error;
    }
  }

  async extendSession(sessionId: string, seconds: number): Promise<any> {
    try {
      const session = await this.prismaService.session.update({
        where: { sessionId },
        data: {
          expiresAt: new Date(Date.now() + seconds * 1000),
        },
      });
      return session;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to extend session: ${err.message}`, err.stack);
      throw error;
    }
  }
} 