import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth';
import { getBackendUrl } from '@/lib/config';

// PUT /api/v1/tools/[id]/deactivate
export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getSession();
    
    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const backendUrl = getBackendUrl();
    const url = `${backendUrl}/api/v1/tools/${params.id}/deactivate`;
    
    // Forward the request to the backend
    const response = await fetch(url, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.accessToken}`,
      },
      cache: 'no-store',
    });
    
    // Get the response data
    const data = await response.json().catch(() => ({}));
    
    // Return the response
    return NextResponse.json(
      data,
      { status: response.status }
    );
  } catch (error) {
    console.error('Error deactivating tool:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
} 