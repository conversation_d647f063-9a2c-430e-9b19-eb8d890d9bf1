#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/schematics-cli/bin/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/schematics-cli/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/schematics-cli/bin/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/schematics-cli/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules/@angular-devkit/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../bin/schematics.js" "$@"
else
  exec node  "$basedir/../../bin/schematics.js" "$@"
fi
