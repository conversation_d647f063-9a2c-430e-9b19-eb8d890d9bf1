@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\laragon\www\max\cursorpro\node_modules\.pnpm\@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0\node_modules\@angular-devkit\schematics-cli\bin\node_modules;C:\laragon\www\max\cursorpro\node_modules\.pnpm\@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0\node_modules\@angular-devkit\schematics-cli\node_modules;C:\laragon\www\max\cursorpro\node_modules\.pnpm\@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0\node_modules\@angular-devkit\node_modules;C:\laragon\www\max\cursorpro\node_modules\.pnpm\@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0\node_modules;C:\laragon\www\max\cursorpro\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\laragon\www\max\cursorpro\node_modules\.pnpm\@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0\node_modules\@angular-devkit\schematics-cli\bin\node_modules;C:\laragon\www\max\cursorpro\node_modules\.pnpm\@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0\node_modules\@angular-devkit\schematics-cli\node_modules;C:\laragon\www\max\cursorpro\node_modules\.pnpm\@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0\node_modules\@angular-devkit\node_modules;C:\laragon\www\max\cursorpro\node_modules\.pnpm\@angular-devkit+schematics-cli@17.3.11_chokidar@3.6.0\node_modules;C:\laragon\www\max\cursorpro\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\bin\schematics.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\bin\schematics.js" %*
)
