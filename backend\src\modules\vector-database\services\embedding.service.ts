import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import OpenAI from 'openai';
import { EmbeddingError, EmbeddingConfig } from '@shared/types/vector-database.types';
import { IEmbeddingService } from '../interfaces/vector-database.interface';

@Injectable()
export class EmbeddingService implements IEmbeddingService {
  private readonly logger = new Logger(EmbeddingService.name);
  private openaiClient: OpenAI | null = null;
  private config: EmbeddingConfig | null = null;
  private isInitialized = false;
  
  // Rate limiting
  private requestQueue: Array<() => Promise<void>> = [];
  private isProcessingQueue = false;
  private lastRequestTime = 0;
  private requestCount = 0;
  private windowStart = Date.now();

  constructor(private readonly configService: ConfigService) {}

  /**
   * Initialize the embedding service with configuration
   */
  async initialize(config: EmbeddingConfig): Promise<void> {
    try {
      this.config = config;
      this.logger.log(`Initializing embedding service with provider: ${config.provider}`);

      switch (config.provider) {
        case 'openai':
          await this.initializeOpenAI(config);
          break;
        case 'azure':
          await this.initializeAzureOpenAI(config);
          break;
        case 'huggingface':
          await this.initializeHuggingFace(config);
          break;
        case 'cohere':
          await this.initializeCohere(config);
          break;
        default:
          throw new Error(`Unsupported embedding provider: ${config.provider}`);
      }

      this.isInitialized = true;
      this.logger.log(`Embedding service initialized successfully with ${config.provider}`);
    } catch (error) {
      this.logger.error('Failed to initialize embedding service', error);
      throw new EmbeddingError(
        'Failed to initialize embedding service',
        config.provider,
        config.model,
        false
      );
    }
  }

  /**
   * Generate embedding for a single text
   */
  async generateEmbedding(text: string): Promise<number[]> {
    try {
      this.ensureInitialized();
      
      if (!text || text.trim().length === 0) {
        throw new EmbeddingError(
          'Text cannot be empty',
          this.config!.provider,
          this.config!.model,
          false
        );
      }

      // Check token limit
      if (this.exceedsTokenLimit(text)) {
        throw new EmbeddingError(
          `Text exceeds maximum token limit of ${this.config!.maxTokens}`,
          this.config!.provider,
          this.config!.model,
          false
        );
      }

      // Apply rate limiting
      await this.applyRateLimit();

      let embedding: number[];

      switch (this.config!.provider) {
        case 'openai':
        case 'azure':
          embedding = await this.generateOpenAIEmbedding(text);
          break;
        case 'huggingface':
          embedding = await this.generateHuggingFaceEmbedding(text);
          break;
        case 'cohere':
          embedding = await this.generateCohereEmbedding(text);
          break;
        default:
          throw new EmbeddingError(
            `Unsupported provider: ${this.config!.provider}`,
            this.config!.provider,
            this.config!.model,
            false
          );
      }

      this.logger.debug(`Generated embedding for text of length ${text.length}`);
      return embedding;
    } catch (error) {
      if (error instanceof EmbeddingError) {
        throw error;
      }
      
      this.logger.error('Failed to generate embedding', error);
      throw new EmbeddingError(
        'Failed to generate embedding',
        this.config?.provider || 'unknown',
        this.config?.model || 'unknown',
        this.isRetryableError(error)
      );
    }
  }

  /**
   * Generate embeddings for multiple texts in batch
   */
  async generateEmbeddings(texts: string[]): Promise<number[][]> {
    try {
      this.ensureInitialized();

      if (!texts || texts.length === 0) {
        throw new EmbeddingError(
          'Texts array cannot be empty',
          this.config!.provider,
          this.config!.model,
          false
        );
      }

      // Validate all texts
      for (const text of texts) {
        if (!text || text.trim().length === 0) {
          throw new EmbeddingError(
            'All texts must be non-empty',
            this.config!.provider,
            this.config!.model,
            false
          );
        }

        if (this.exceedsTokenLimit(text)) {
          throw new EmbeddingError(
            `Text exceeds maximum token limit of ${this.config!.maxTokens}`,
            this.config!.provider,
            this.config!.model,
            false
          );
        }
      }

      // Process in batches to respect provider limits
      const batchSize = this.config!.batchSize || 100;
      const embeddings: number[][] = [];

      for (let i = 0; i < texts.length; i += batchSize) {
        const batch = texts.slice(i, i + batchSize);
        
        // Apply rate limiting for batch
        await this.applyRateLimit();

        let batchEmbeddings: number[][];

        switch (this.config!.provider) {
          case 'openai':
          case 'azure':
            batchEmbeddings = await this.generateOpenAIEmbeddings(batch);
            break;
          case 'huggingface':
            batchEmbeddings = await this.generateHuggingFaceEmbeddings(batch);
            break;
          case 'cohere':
            batchEmbeddings = await this.generateCohereEmbeddings(batch);
            break;
          default:
            throw new EmbeddingError(
              `Unsupported provider: ${this.config!.provider}`,
              this.config!.provider,
              this.config!.model,
              false
            );
        }

        embeddings.push(...batchEmbeddings);
        
        this.logger.debug(`Generated ${batchEmbeddings.length} embeddings in batch`);

        // Add small delay between batches to avoid rate limiting
        if (i + batchSize < texts.length) {
          await this.delay(100);
        }
      }

      this.logger.log(`Generated ${embeddings.length} embeddings for ${texts.length} texts`);
      return embeddings;
    } catch (error) {
      if (error instanceof EmbeddingError) {
        throw error;
      }
      
      this.logger.error('Failed to generate batch embeddings', error);
      throw new EmbeddingError(
        'Failed to generate batch embeddings',
        this.config?.provider || 'unknown',
        this.config?.model || 'unknown',
        this.isRetryableError(error)
      );
    }
  }

  /**
   * Get the dimension of embeddings produced by this service
   */
  getDimension(): number {
    this.ensureInitialized();
    return this.config!.dimension;
  }

  /**
   * Get the maximum token limit for input text
   */
  getMaxTokens(): number {
    this.ensureInitialized();
    return this.config!.maxTokens || 8192;
  }

  /**
   * Check if the service is healthy and available
   */
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isInitialized || !this.config) {
        return false;
      }

      // Test with a simple embedding
      const testText = 'Health check test';
      await this.generateEmbedding(testText);
      
      return true;
    } catch (error) {
      this.logger.error('Embedding service health check failed', error);
      return false;
    }
  }

  // Private methods for provider initialization

  private async initializeOpenAI(config: EmbeddingConfig): Promise<void> {
    this.openaiClient = new OpenAI({
      apiKey: config.apiKey || this.configService.get<string>('OPENAI_API_KEY'),
    });

    // Test the connection
    try {
      await this.openaiClient.embeddings.create({
        model: config.model,
        input: 'test',
      });
    } catch (error) {
      throw new Error(`Failed to connect to OpenAI: ${error}`);
    }
  }

  private async initializeAzureOpenAI(config: EmbeddingConfig): Promise<void> {
    if (!config.endpoint) {
      throw new Error('Azure OpenAI endpoint is required');
    }

    this.openaiClient = new OpenAI({
      apiKey: config.apiKey || this.configService.get<string>('AZURE_OPENAI_API_KEY'),
      baseURL: config.endpoint,
      defaultQuery: { 'api-version': '2023-05-15' },
      defaultHeaders: {
        'api-key': config.apiKey || this.configService.get<string>('AZURE_OPENAI_API_KEY'),
      },
    });
  }

  private async initializeHuggingFace(config: EmbeddingConfig): Promise<void> {
    // HuggingFace initialization would go here
    // For now, we'll use a placeholder
    this.logger.warn('HuggingFace embedding provider not fully implemented');
  }

  private async initializeCohere(config: EmbeddingConfig): Promise<void> {
    // Cohere initialization would go here
    // For now, we'll use a placeholder
    this.logger.warn('Cohere embedding provider not fully implemented');
  }

  // Private methods for embedding generation

  private async generateOpenAIEmbedding(text: string): Promise<number[]> {
    try {
      const response = await this.openaiClient!.embeddings.create({
        model: this.config!.model,
        input: text,
      });

      return response.data[0]?.embedding || [];
    } catch (error) {
      throw new EmbeddingError(
        `OpenAI embedding failed: ${error}`,
        'openai',
        this.config!.model,
        this.isRetryableError(error)
      );
    }
  }

  private async generateOpenAIEmbeddings(texts: string[]): Promise<number[][]> {
    try {
      const response = await this.openaiClient!.embeddings.create({
        model: this.config!.model,
        input: texts,
      });

      return response.data.map((item) => item.embedding);
    } catch (error) {
      throw new EmbeddingError(
        `OpenAI batch embedding failed: ${error}`,
        'openai',
        this.config!.model,
        this.isRetryableError(error)
      );
    }
  }

  private async generateHuggingFaceEmbedding(text: string): Promise<number[]> {
    // HuggingFace implementation would go here
    throw new EmbeddingError(
      'HuggingFace embedding not implemented',
      'huggingface',
      this.config!.model,
      false
    );
  }

  private async generateHuggingFaceEmbeddings(texts: string[]): Promise<number[][]> {
    // HuggingFace batch implementation would go here
    throw new EmbeddingError(
      'HuggingFace batch embedding not implemented',
      'huggingface',
      this.config!.model,
      false
    );
  }

  private async generateCohereEmbedding(text: string): Promise<number[]> {
    // Cohere implementation would go here
    throw new EmbeddingError(
      'Cohere embedding not implemented',
      'cohere',
      this.config!.model,
      false
    );
  }

  private async generateCohereEmbeddings(texts: string[]): Promise<number[][]> {
    // Cohere batch implementation would go here
    throw new EmbeddingError(
      'Cohere batch embedding not implemented',
      'cohere',
      this.config!.model,
      false
    );
  }

  // Utility methods

  private ensureInitialized(): void {
    if (!this.isInitialized || !this.config) {
      throw new EmbeddingError(
        'Embedding service is not initialized',
        'unknown',
        'unknown',
        false
      );
    }
  }

  private exceedsTokenLimit(text: string): boolean {
    // Simple token estimation (4 characters ≈ 1 token)
    const estimatedTokens = Math.ceil(text.length / 4);
    return estimatedTokens > this.getMaxTokens();
  }

  private async applyRateLimit(): Promise<void> {
    if (!this.config?.rateLimitRpm) {
      return;
    }

    const now = Date.now();
    const windowDuration = 60000; // 1 minute

    // Reset window if needed
    if (now - this.windowStart >= windowDuration) {
      this.windowStart = now;
      this.requestCount = 0;
    }

    // Check if we've exceeded the rate limit
    if (this.requestCount >= this.config.rateLimitRpm) {
      const waitTime = windowDuration - (now - this.windowStart);
      this.logger.warn(`Rate limit exceeded, waiting ${waitTime}ms`);
      await this.delay(waitTime);
      
      // Reset counters
      this.windowStart = Date.now();
      this.requestCount = 0;
    }

    this.requestCount++;
  }

  private isRetryableError(error: any): boolean {
    // Check if error is retryable based on status code or error type
    if (error?.response?.status) {
      const status = error.response.status;
      return status === 429 || status === 500 || status === 502 || status === 503 || status === 504;
    }
    
    if (error?.code) {
      return ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND'].includes(error.code);
    }
    
    return false;
  }

  private async delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
} 