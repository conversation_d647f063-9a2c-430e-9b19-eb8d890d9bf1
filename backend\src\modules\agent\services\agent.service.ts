import { Injectable, Logger, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { RedisService } from '../../redis/redis.service';
import { SessionService } from '../../session/services/session.service';
import { Agent, AgentStatus, Prisma } from '.prisma/client';
import { v4 as uuidv4 } from 'uuid';

export interface CreateAgentDto {
  name: string;
  description?: string;
  templateId?: string;
  configuration: AgentConfiguration;
  metadata?: Record<string, any>;
}

export interface UpdateAgentDto {
  name?: string;
  description?: string;
  configuration?: AgentConfiguration;
  metadata?: Record<string, any>;
  status?: AgentStatus;
}

export interface AgentConfiguration {
  systemPrompt: string;
  model: string;
  temperature: number;
  maxTokens: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stopSequences?: string[];
  responseFormat?: 'text' | 'json' | 'markdown';
  tools?: string[]; // Tool IDs this agent can use
  knowledge?: string[]; // Knowledge base IDs this agent can access
  behaviors?: AgentBehavior[];
}

export interface AgentBehavior {
  trigger: string;
  action: string;
  parameters?: Record<string, any>;
}

export interface AgentWithStats extends Agent {
  executionCount?: number;
  successRate?: number;
  avgResponseTime?: number;
  lastExecutedAt?: Date;
  configuration?: AgentConfiguration;
  metadata?: Record<string, any>;
}

/**
 * Agent Service
 * 
 * Manages AI agent lifecycle:
 * - Agent creation and configuration
 * - Agent management and updates
 * - Agent discovery and search
 * - Integration with templates
 * - Multi-tenant isolation
 */
@Injectable()
export class AgentService {
  private readonly logger = new Logger(AgentService.name);
  private readonly CACHE_PREFIX = 'agent:';
  private readonly CACHE_TTL = 3600; // 1 hour

  constructor(
    private readonly prismaService: PrismaService,
    private readonly redisService: RedisService,
    private readonly sessionService: SessionService,
  ) {}

  /**
   * Create a new agent
   */
  async createAgent(
    userId: string,
    organizationId: string,
    data: CreateAgentDto,
  ): Promise<Agent> {
    try {
      // Validate configuration
      this.validateAgentConfiguration(data.configuration);

      // Generate agent ID
      const agentId = `agt_${uuidv4()}`;

      // Create agent
      const agent = await this.prismaService.agent.create({
        data: {
          id: agentId,
          name: data.name,
          description: data.description,
          organizationId,
          createdById: userId,
          templateId: data.templateId,
          // Store as JSON in the database
          config: data.configuration as unknown as Prisma.InputJsonValue,
          model: data.configuration.model,
          temperature: data.configuration.temperature,
          maxTokens: data.configuration.maxTokens,
          tools: data.configuration.tools || [],
          prompt: data.configuration.systemPrompt,
          capabilities: data.metadata as unknown as Prisma.InputJsonValue || {},
          status: 'DRAFT',
          version: '1.0.0',
        },
      });

      // Cache agent
      await this.cacheAgent({
        ...agent,
        configuration: data.configuration,
        metadata: data.metadata || {},
      });

      // Log creation
      this.logger.log(`Agent created: ${agentId} by user: ${userId}`);

      // Emit event
      await this.emitAgentEvent('agent.created', agent);

      return agent;
    } catch (error) {
      this.logger.error('Failed to create agent', error);
      throw error;
    }
  }

  /**
   * Get agent by ID
   */
  async getAgent(
    agentId: string,
    userId: string,
    organizationId: string,
  ): Promise<AgentWithStats | null> {
    try {
      // Check cache first
      const cached = await this.getCachedAgent(agentId);
      if (cached) {
        // Verify access
        if (cached.organizationId !== organizationId) {
          throw new ForbiddenException('Access denied');
        }
        return cached;
      }

      // Get from database
      const agent = await this.prismaService.agent.findFirst({
        where: {
          id: agentId,
          organizationId,
          deletedAt: null,
        },
      });

      if (!agent) {
        return null;
      }

      // Get stats
      const stats = await this.getAgentStats(agentId);

      const agentWithStats: AgentWithStats = {
        ...agent,
        ...stats,
        configuration: {
          systemPrompt: agent.prompt || '',
          model: agent.model,
          temperature: agent.temperature,
          maxTokens: agent.maxTokens,
          tools: agent.tools,
          ...(agent.config as unknown as Record<string, any>),
        },
        metadata: agent.capabilities as unknown as Record<string, any>,
      };

      // Cache agent
      await this.cacheAgent(agentWithStats);

      return agentWithStats;
    } catch (error) {
      this.logger.error(`Failed to get agent: ${agentId}`, error);
      throw error;
    }
  }

  /**
   * Update agent
   */
  async updateAgent(
    agentId: string,
    userId: string,
    organizationId: string,
    data: UpdateAgentDto,
  ): Promise<Agent> {
    try {
      // Get existing agent
      const existing = await this.getAgent(agentId, userId, organizationId);
      if (!existing) {
        throw new NotFoundException('Agent not found');
      }

      // Validate configuration if provided
      if (data.configuration) {
        this.validateAgentConfiguration(data.configuration);
      }

      // Update agent
      const updated = await this.prismaService.agent.update({
        where: { id: agentId },
        data: {
          ...(data.name !== undefined ? { name: data.name } : {}),
          ...(data.description !== undefined ? { description: data.description } : {}),
          ...(data.status !== undefined ? { status: data.status } : {}),
          // Update configuration fields
          ...(data.configuration ? {
            config: data.configuration as unknown as Prisma.InputJsonValue,
            model: data.configuration.model,
            temperature: data.configuration.temperature,
            maxTokens: data.configuration.maxTokens,
            tools: data.configuration.tools || [],
            prompt: data.configuration.systemPrompt,
          } : {}),
          // Update metadata
          ...(data.metadata ? {
            capabilities: data.metadata as unknown as Prisma.InputJsonValue,
          } : {}),
          updatedAt: new Date(),
        },
      });

      // Invalidate cache
      await this.invalidateAgentCache(agentId);

      // Emit event
      await this.emitAgentEvent('agent.updated', updated);

      this.logger.log(`Agent updated: ${agentId}`);

      return updated;
    } catch (error) {
      this.logger.error(`Failed to update agent: ${agentId}`, error);
      throw error;
    }
  }

  /**
   * Delete agent (soft delete)
   */
  async deleteAgent(
    agentId: string,
    userId: string,
    organizationId: string,
  ): Promise<void> {
    try {
      // Verify access
      const agent = await this.getAgent(agentId, userId, organizationId);
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Soft delete
      await this.prismaService.agent.update({
        where: { id: agentId },
        data: {
          deletedAt: new Date(),
          status: 'ARCHIVED',
        },
      });

      // Invalidate cache
      await this.invalidateAgentCache(agentId);

      // Emit event
      await this.emitAgentEvent('agent.deleted', { agentId });

      this.logger.log(`Agent deleted: ${agentId}`);
    } catch (error) {
      this.logger.error(`Failed to delete agent: ${agentId}`, error);
      throw error;
    }
  }

  /**
   * List agents for organization
   */
  async listAgents(
    organizationId: string,
    options: {
      status?: AgentStatus;
      templateId?: string;
      search?: string;
      limit?: number;
      offset?: number;
      orderBy?: 'createdAt' | 'updatedAt' | 'name';
      order?: 'asc' | 'desc';
    } = {},
  ): Promise<{ agents: AgentWithStats[]; total: number }> {
    try {
      const where: Prisma.AgentWhereInput = {
        organizationId,
        deletedAt: null,
        ...(options.status && { status: options.status }),
        ...(options.templateId && { templateId: options.templateId }),
        ...(options.search && {
          OR: [
            { name: { contains: options.search, mode: 'insensitive' } },
            { description: { contains: options.search, mode: 'insensitive' } },
          ],
        }),
      };

      // Get total count
      const total = await this.prismaService.agent.count({ where });

      // Get agents
      const agents = await this.prismaService.agent.findMany({
        where,
        take: options.limit || 20,
        skip: options.offset || 0,
        orderBy: {
          [options.orderBy || 'createdAt']: options.order || 'desc',
        },
      });

      // Get stats for each agent
      const agentsWithStats = await Promise.all(
        agents.map(async (agent) => {
          const stats = await this.getAgentStats(agent.id);
          return { 
            ...agent, 
            ...stats,
            configuration: {
              systemPrompt: agent.prompt || '',
              model: agent.model,
              temperature: agent.temperature,
              maxTokens: agent.maxTokens,
              tools: agent.tools,
              ...(agent.config as unknown as Record<string, any>),
            },
            metadata: agent.capabilities as unknown as Record<string, any>,
          };
        }),
      );

      return {
        agents: agentsWithStats,
        total,
      };
    } catch (error) {
      this.logger.error('Failed to list agents', error);
      throw error;
    }
  }

  /**
   * Activate agent
   */
  async activateAgent(
    agentId: string,
    userId: string,
    organizationId: string,
  ): Promise<Agent> {
    return this.updateAgent(agentId, userId, organizationId, {
      status: 'ACTIVE',
    });
  }

  /**
   * Deactivate agent
   */
  async deactivateAgent(
    agentId: string,
    userId: string,
    organizationId: string,
  ): Promise<Agent> {
    return this.updateAgent(agentId, userId, organizationId, {
      status: 'INACTIVE',
    });
  }

  /**
   * Clone agent
   */
  async cloneAgent(
    agentId: string,
    userId: string,
    organizationId: string,
    newName: string,
  ): Promise<Agent> {
    try {
      // Get original agent
      const original = await this.getAgent(agentId, userId, organizationId);
      if (!original) {
        throw new NotFoundException('Agent not found');
      }

      // Create clone
      return this.createAgent(userId, organizationId, {
        name: newName,
        description: `Clone of ${original.name}`,
        templateId: original.templateId || undefined,
        configuration: original.configuration as AgentConfiguration,
        metadata: {
          ...(original.metadata as Record<string, any> || {}),
          clonedFrom: agentId,
          clonedAt: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Failed to clone agent: ${agentId}`, error);
      throw error;
    }
  }

  /**
   * Get agent statistics
   */
  private async getAgentStats(agentId: string): Promise<{
    executionCount: number;
    successRate: number;
    avgResponseTime: number;
    lastExecutedAt?: Date;
  }> {
    try {
      // Use raw SQL queries instead of Prisma's aggregate and findFirst
      const executionStats = await this.prismaService.$queryRaw<{ count: number, avg_duration: number }[]>`
        SELECT 
          COUNT(*) as count,
          AVG(duration) as avg_duration
        FROM "agent_executions"
        WHERE "agentId" = ${agentId}
      `;
      
      const successCount = await this.prismaService.$queryRaw<{ count: number }[]>`
        SELECT COUNT(*) as count
        FROM "agent_executions"
        WHERE "agentId" = ${agentId} AND "status" = 'COMPLETED'
      `;
      
      const lastExecution = await this.prismaService.$queryRaw<{ started_at: Date }[]>`
        SELECT "startedAt" as started_at
        FROM "agent_executions"
        WHERE "agentId" = ${agentId}
        ORDER BY "startedAt" DESC
        LIMIT 1
      `;

      const count = executionStats[0]?.count || 0;
      
      return {
        executionCount: count,
        successRate: count > 0 ? ((successCount[0]?.count || 0) / count) * 100 : 0,
        avgResponseTime: executionStats[0]?.avg_duration || 0,
        lastExecutedAt: lastExecution[0]?.started_at,
      };
    } catch (error) {
      this.logger.error(`Failed to get agent stats: ${agentId}`, error);
      return {
        executionCount: 0,
        successRate: 0,
        avgResponseTime: 0,
      };
    }
  }

  /**
   * Validate agent configuration
   */
  private validateAgentConfiguration(config: AgentConfiguration): void {
    if (!config.systemPrompt || config.systemPrompt.trim().length === 0) {
      throw new BadRequestException('System prompt is required');
    }

    if (!config.model || config.model.trim().length === 0) {
      throw new BadRequestException('Model is required');
    }

    if (config.temperature < 0 || config.temperature > 2) {
      throw new BadRequestException('Temperature must be between 0 and 2');
    }

    if (config.maxTokens < 1 || config.maxTokens > 32000) {
      throw new BadRequestException('Max tokens must be between 1 and 32000');
    }

    if (config.topP !== undefined && (config.topP < 0 || config.topP > 1)) {
      throw new BadRequestException('Top P must be between 0 and 1');
    }
  }

  /**
   * Cache agent
   */
  private async cacheAgent(agent: AgentWithStats): Promise<void> {
    try {
      await this.redisService.set(
        `${this.CACHE_PREFIX}${agent.id}`,
        JSON.stringify(agent),
        this.CACHE_TTL,
      );
    } catch (error) {
      this.logger.error('Failed to cache agent', error);
    }
  }

  /**
   * Get cached agent
   */
  private async getCachedAgent(agentId: string): Promise<AgentWithStats | null> {
    try {
      const cached = await this.redisService.get(`${this.CACHE_PREFIX}${agentId}`);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      this.logger.error('Failed to get cached agent', error);
      return null;
    }
  }

  /**
   * Invalidate agent cache
   */
  private async invalidateAgentCache(agentId: string): Promise<void> {
    try {
      await this.redisService.del(`${this.CACHE_PREFIX}${agentId}`);
    } catch (error) {
      this.logger.error('Failed to invalidate agent cache', error);
    }
  }

  /**
   * Emit agent event
   */
  private async emitAgentEvent(event: string, data: any): Promise<void> {
    try {
      const pubClient = await this.redisService.getClient();
      await pubClient.publish('apix:events', JSON.stringify({
        event,
        data,
        timestamp: new Date().toISOString(),
      }));
    } catch (error) {
      this.logger.error(`Failed to emit agent event: ${event}`, error);
    }
  }
} 