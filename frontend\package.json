{"name": "@synapseai/frontend", "version": "1.0.0", "description": "Next.js 14 frontend for SynapseAI platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "clean": "rm -rf .next out dist", "analyze": "cross-env ANALYZE=true next build", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@monaco-editor/react": "^4.6.0", "@next-auth/prisma-adapter": "^1.0.7", "@synapseai/shared": "workspace:*", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.83.0", "axios": "^1.6.0", "chart.js": "^4.4.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "d3": "^7.8.5", "date-fns": "^2.30.0", "framer-motion": "^10.18.0", "jwt-decode": "^4.0.0", "lodash": "^4.17.21", "lucide-react": "^0.292.0", "nanoid": "^5.0.3", "next": "^14.0.3", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.2.0", "react-datepicker": "^4.23.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-error-boundary": "^4.0.11", "react-flow-renderer": "^10.3.17", "react-helmet-async": "^1.3.0", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.5.2", "react-intersection-observer": "^9.5.3", "react-lazyload": "^3.2.0", "react-markdown": "^9.0.1", "react-select": "^5.8.0", "react-syntax-highlighter": "^15.5.0", "react-table": "^7.8.0", "react-use": "^17.4.0", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "recharts": "^2.8.0", "sharp": "^0.32.6", "socket.io-client": "^4.7.2", "tailwind-merge": "^2.0.0", "use-debounce": "^10.0.0", "uuid": "^9.0.1", "validator": "^13.11.0", "zod": "^3.22.4"}, "devDependencies": {"@next/bundle-analyzer": "^14.0.3", "@playwright/test": "^1.39.0", "@storybook/addon-essentials": "^7.5.3", "@storybook/nextjs": "^7.5.3", "@storybook/react": "^7.5.3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.1", "@types/d3": "^7.4.3", "@types/lodash": "^4.14.200", "@types/node": "^20.8.0", "@types/react": "^18.2.33", "@types/react-beautiful-dnd": "^13.1.6", "@types/react-datepicker": "^4.19.4", "@types/react-dom": "^18.2.14", "@types/react-syntax-highlighter": "^15.5.9", "@types/react-virtualized": "^9.21.25", "@types/react-window": "^1.8.7", "@types/uuid": "^9.0.6", "@types/validator": "^13.11.5", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "autoprefixer": "^10.4.16", "cross-env": "^7.0.3", "eslint": "^8.50.0", "eslint-config-next": "^14.0.3", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "msw": "^2.0.8", "playwright": "^1.39.0", "postcss": "^8.4.31", "prettier": "^3.0.3", "storybook": "^7.5.3", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}