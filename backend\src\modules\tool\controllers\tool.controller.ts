import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Request } from 'express';
import { ToolService, CreateToolDto, UpdateToolDto, ToolConfiguration } from '../services/tool.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { OrganizationGuard } from '../../auth/guards/organization.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { ToolExecutionService } from '../services/tool-execution.service';
import { ToolAnalyticsService } from '../services/tool-analytics.service';
import { ToolMarketplaceService } from '../services/tool-marketplace.service';
import { ToolValidationService } from '../services/tool-validation.service';

interface JwtPayload {
  sub: string;
  email: string;
  organizationId: string;
  role: string;
}

/**
 * Tool Controller
 * 
 * REST API for tool management:
 * - Create, read, update, delete tools
 * - List and search tools
 * - Execute tools with parameters
 * - Validate tool configurations
 * - Tool analytics and marketplace
 */
@ApiTags('tools')
@Controller('api/v1/tools')
@UseGuards(JwtAuthGuard, RolesGuard, OrganizationGuard)
@ApiBearerAuth()
export class ToolController {
  private readonly logger = new Logger(ToolController.name);

  constructor(
    private readonly toolService: ToolService,
    private readonly toolExecutionService: ToolExecutionService,
    private readonly toolAnalyticsService: ToolAnalyticsService,
    private readonly toolMarketplaceService: ToolMarketplaceService,
    private readonly toolValidationService: ToolValidationService,
  ) {}

  /**
   * Create a new tool
   */
  @Post()
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Create a new tool' })
  @ApiBody({ schema: { 
    properties: {
      name: { type: 'string' },
      description: { type: 'string' },
      type: { type: 'string', enum: ['API', 'FUNCTION', 'DATABASE', 'STORAGE', 'MESSAGING'] },
      configuration: { 
        type: 'object',
        properties: {
          endpoint: { type: 'string' },
          method: { type: 'string', enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'] },
          headers: { type: 'object' },
          parameters: { type: 'array' },
          authentication: { type: 'object' },
          schema: { type: 'object' }
        }
      },
      metadata: { type: 'object' }
    }
  }})
  @ApiResponse({ status: 201, description: 'Tool created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async createTool(@Body() data: CreateToolDto, @Req() req: Request) {
    try {
      const user = req['user'] as JwtPayload;
      
      const tool = await this.toolService.createTool(
        user.sub,
        user.organizationId,
        data,
      );

      return tool;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to create tool: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to create tool',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get tool by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get tool by ID' })
  @ApiParam({ name: 'id', description: 'Tool ID' })
  @ApiResponse({ status: 200, description: 'Tool retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async getTool(@Param('id') id: string, @Req() req: Request) {
    try {
      const user = req['user'] as JwtPayload;
      
      const tool = await this.toolService.getTool(
        id,
        user.sub,
        user.organizationId,
      );

      if (!tool) {
        throw new HttpException('Tool not found', HttpStatus.NOT_FOUND);
      }

      return tool;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to get tool: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to get tool',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update tool
   */
  @Put(':id')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Update tool' })
  @ApiParam({ name: 'id', description: 'Tool ID' })
  @ApiBody({ schema: { 
    properties: {
      name: { type: 'string' },
      description: { type: 'string' },
      type: { type: 'string', enum: ['API', 'FUNCTION', 'DATABASE', 'STORAGE', 'MESSAGING'] },
      configuration: { type: 'object' },
      status: { type: 'string', enum: ['DRAFT', 'ACTIVE', 'INACTIVE', 'DEPRECATED'] }
    }
  }})
  @ApiResponse({ status: 200, description: 'Tool updated successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async updateTool(
    @Param('id') id: string,
    @Body() data: UpdateToolDto,
    @Req() req: Request,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      const tool = await this.toolService.updateTool(
        id,
        user.sub,
        user.organizationId,
        data,
      );

      return tool;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to update tool: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to update tool',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Delete tool
   */
  @Delete(':id')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Delete tool' })
  @ApiParam({ name: 'id', description: 'Tool ID' })
  @ApiResponse({ status: 200, description: 'Tool deleted successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async deleteTool(@Param('id') id: string, @Req() req: Request) {
    try {
      const user = req['user'] as JwtPayload;
      
      await this.toolService.deleteTool(
        id,
        user.sub,
        user.organizationId,
      );

      return { message: 'Tool deleted successfully' };
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to delete tool: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to delete tool',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * List tools
   */
  @Get()
  @ApiOperation({ summary: 'List tools' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  @ApiQuery({ name: 'type', required: false, description: 'Filter by tool type' })
  @ApiQuery({ name: 'search', required: false, description: 'Search by name or description' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limit number of results' })
  @ApiQuery({ name: 'offset', required: false, description: 'Offset for pagination' })
  @ApiQuery({ name: 'orderBy', required: false, description: 'Order by field' })
  @ApiQuery({ name: 'order', required: false, description: 'Order direction' })
  @ApiResponse({ status: 200, description: 'Tools retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async listTools(
    @Req() req: Request,
    @Query('status') status?: string,
    @Query('type') type?: string,
    @Query('search') search?: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
    @Query('orderBy') orderBy?: 'createdAt' | 'updatedAt' | 'name',
    @Query('order') order?: 'asc' | 'desc',
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      const result = await this.toolService.listTools(
        user.organizationId,
        {
          status: status as any,
          type: type as any,
          search,
          limit: limit ? parseInt(limit.toString(), 10) : undefined,
          offset: offset ? parseInt(offset.toString(), 10) : undefined,
          orderBy,
          order,
        },
      );

      return result;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to list tools: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to list tools',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Execute tool
   */
  @Post(':id/execute')
  @ApiOperation({ summary: 'Execute tool' })
  @ApiParam({ name: 'id', description: 'Tool ID' })
  @ApiBody({ schema: { properties: { parameters: { type: 'object' }, sessionId: { type: 'string' } } } })
  @ApiResponse({ status: 200, description: 'Tool executed successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async executeTool(
    @Param('id') id: string,
    @Body() data: { parameters: Record<string, any>; sessionId?: string; metadata?: Record<string, any> },
    @Req() req: Request,
  ): Promise<any> {
    try {
      const user = req['user'] as JwtPayload;
      
      const result = await this.toolExecutionService.executeTool(
        id,
        data.parameters,    
        data.metadata,
        data.sessionId,
        user.sub,
        user.organizationId,
        data as any,
      );

      return result;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to execute tool: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to execute tool',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Clone tool
   */
  @Post(':id/clone')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Clone tool' })
  @ApiParam({ name: 'id', description: 'Tool ID' })
  @ApiBody({ schema: { properties: { name: { type: 'string' } } } })
  @ApiResponse({ status: 201, description: 'Tool cloned successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async cloneTool(
    @Param('id') id: string,
    @Body() data: { name: string },
    @Req() req: Request,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      const tool = await this.toolService.cloneTool(
        id,
        user.sub,
        user.organizationId,
        data.name,
      );

      return tool;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to clone tool: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to clone tool',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Activate tool
   */
  @Put(':id/activate')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Activate tool' })
  @ApiParam({ name: 'id', description: 'Tool ID' })
  @ApiResponse({ status: 200, description: 'Tool activated successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async activateTool(@Param('id') id: string, @Req() req: Request) {
    try {
      const user = req['user'] as JwtPayload;
      
      const tool = await this.toolService.activateTool(
        id,
        user.sub,
        user.organizationId,
      );

      return tool;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to activate tool: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to activate tool',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Deactivate tool
   */
  @Put(':id/deactivate')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Deactivate tool' })
  @ApiParam({ name: 'id', description: 'Tool ID' })
  @ApiResponse({ status: 200, description: 'Tool deactivated successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async deactivateTool(@Param('id') id: string, @Req() req: Request) {
    try {
      const user = req['user'] as JwtPayload;
      
      const tool = await this.toolService.deactivateTool(
        id,
        user.sub,
        user.organizationId,
      );

      return tool;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to deactivate tool: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to deactivate tool',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Test tool
   */
  @Post(':id/test')
  @ApiOperation({ summary: 'Test tool' })
  @ApiParam({ name: 'id', description: 'Tool ID' })
  @ApiBody({ schema: { properties: { parameters: { type: 'object' } } } })
  @ApiResponse({ status: 200, description: 'Tool test completed' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async testTool(
    @Param('id') id: string,
    @Body() data: { parameters: Record<string, any> },
    @Req() req: Request,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      const result = await this.toolExecutionService.testTool(
        id,
        data.parameters,
        user.sub,
        user.organizationId,
      );

      return result;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to test tool: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to test tool',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get tool analytics
   */
  @Get(':id/analytics')
  @ApiOperation({ summary: 'Get tool performance analytics' })
  @ApiParam({ name: 'id', description: 'Tool ID' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Start date for analytics period' })
  @ApiQuery({ name: 'endDate', required: false, description: 'End date for analytics period' })
  @ApiResponse({ status: 200, description: 'Analytics retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async getToolAnalytics(
    @Req() req: Request,
    @Param('id') id: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      // Default to last 30 days if no dates provided
      const endDateObj = endDate ? new Date(endDate) : new Date();
      const startDateObj = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      const result = await this.toolAnalyticsService.getToolAnalytics(
        id,
        user.organizationId,
        startDateObj,
        endDateObj,
      );

      return result;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to get tool analytics: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to get tool analytics',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Validate tool configuration
   */
  @Post('validate')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Validate tool configuration' })
  @ApiBody({ schema: { properties: { configuration: { type: 'object' } } } })
  @ApiResponse({ status: 200, description: 'Configuration validated successfully' })
  @ApiResponse({ status: 400, description: 'Invalid configuration' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async validateToolConfiguration(
    @Body() data: { configuration: Record<string, any> },
    @Req() req: Request,
  ) {
    try {
      const result = await this.toolValidationService.validateToolConfiguration(data.configuration as ToolConfiguration);
      return result;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to validate tool configuration: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to validate tool configuration',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Publish tool to marketplace
   */
  @Post(':id/publish')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Publish tool to marketplace' })
  @ApiParam({ name: 'id', description: 'Tool ID' })
  @ApiBody({ 
    schema: { 
      properties: { 
        isPublic: { type: 'boolean' },
        category: { type: 'string' },
        tags: { type: 'array', items: { type: 'string' } },
        pricingModel: { type: 'string', enum: ['FREE', 'PAID', 'SUBSCRIPTION'] },
        price: { type: 'number' }
      } 
    } 
  })
  @ApiResponse({ status: 200, description: 'Tool published successfully' })
  @ApiResponse({ status: 404, description: 'Tool not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async publishTool(
    @Param('id') id: string,
    @Body() data: {
      isPublic: boolean;
      category?: string;
      tags?: string[];
      pricingModel?: 'FREE' | 'PAID' | 'SUBSCRIPTION';
      price?: number;
    },
    @Req() req: Request,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      const result = await this.toolMarketplaceService.publishTool(
        id,
        user.sub,
        user.organizationId,
        data as any,
      );

      return result;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to publish tool: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to publish tool',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}

