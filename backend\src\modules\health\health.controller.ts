import { Controller, Get } from '@nestjs/common';
import {
  HealthCheck,
  HealthCheckService,
  HealthCheckResult,
  PrismaHealthIndicator,
  MemoryHealthIndicator,
  DiskHealthIndicator,
} from '@nestjs/terminus';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';
import { PrismaService } from '../prisma/prisma.service';

/**
 * Health Check Controller
 * 
 * Provides health check endpoints for monitoring
 * system health and dependencies.
 */
@ApiTags('Health')
@Controller('health')
export class HealthController {
  constructor(
    private readonly health: HealthCheckService,
    private readonly healthService: HealthService,
    private readonly prismaHealth: PrismaHealthIndicator,
    private readonly memoryHealth: MemoryHealthIndicator,
    private readonly diskHealth: DiskHealthIndicator,
    private readonly prismaService: PrismaService,
  ) {}

  /**
   * Basic health check
   */
  @Get()
  @HealthCheck()
  @ApiOperation({
    summary: 'Basic health check',
    description: 'Check if the service is running and healthy',
  })
  @ApiResponse({
    status: 200,
    description: 'Service is healthy',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', example: 'ok' },
        info: { type: 'object' },
        error: { type: 'object' },
        details: { type: 'object' },
      },
    },
  })
  @ApiResponse({
    status: 503,
    description: 'Service is unhealthy',
  })
  async check(): Promise<HealthCheckResult> {
    return this.health.check([
      () => this.memoryHealth.checkHeap('memory_heap', 150 * 1024 * 1024), // 150MB
      () => this.memoryHealth.checkRSS('memory_rss', 150 * 1024 * 1024), // 150MB
    ]);
  }

  /**
   * Database health check
   */
  @Get('database')
  @HealthCheck()
  @ApiOperation({
    summary: 'Database health check',
    description: 'Check PostgreSQL database connectivity and performance',
  })
  async checkDatabase(): Promise<HealthCheckResult> {
    return this.health.check([
      () => this.healthService.databaseCheck(),
    ]);
  }

  /**
   * Redis health check
   */
  @Get('redis')
  @HealthCheck()
  @ApiOperation({
    summary: 'Redis health check',
    description: 'Check Redis connectivity and performance',
  })
  async checkRedis(): Promise<HealthCheckResult> {
    return this.health.check([
      () => this.healthService.redisCheck(),
    ]);
  }

  /**
   * Vector database health check
   */
  @Get('vector-db')
  @HealthCheck()
  @ApiOperation({
    summary: 'Vector database health check',
    description: 'Check Pinecone/vector database connectivity',
  })
  async checkVectorDb(): Promise<HealthCheckResult> {
    return this.health.check([
      () => this.healthService.vectorDbCheck(),
    ]);
  }

  /**
   * External dependencies health check
   */
  @Get('external')
  @HealthCheck()
  @ApiOperation({
    summary: 'External dependencies health check',
    description: 'Check external API dependencies (OpenAI, etc.)',
  })
  async checkExternal(): Promise<HealthCheckResult> {
    return this.health.check([
      () => this.healthService.externalServicesCheck(),
    ]);
  }

  /**
   * Comprehensive health check
   */
  @Get('comprehensive')
  @HealthCheck()
  @ApiOperation({
    summary: 'Comprehensive health check',
    description: 'Check all system components and dependencies',
  })
  async checkAll(): Promise<HealthCheckResult> {
    return this.health.check([
      // Memory checks
      () => this.memoryHealth.checkHeap('memory_heap', 200 * 1024 * 1024), // 200MB
      () => this.memoryHealth.checkRSS('memory_rss', 200 * 1024 * 1024), // 200MB
      
      // Storage checks
      () => this.diskHealth.checkStorage('storage', { 
        path: '/', 
        thresholdPercent: 0.9, // 90% disk usage threshold
      }),
      
      // Database checks
      () => this.healthService.databaseCheck(),
      
      // Redis checks
      () => this.healthService.redisCheck(),
      
      // Vector database checks
      () => this.healthService.vectorDbCheck(),
      
      // External dependencies
      () => this.healthService.externalServicesCheck(),
    ]);
  }

  /**
   * System metrics endpoint
   */
  @Get('metrics')
  @ApiOperation({
    summary: 'System metrics',
    description: 'Get detailed system metrics and performance data',
  })
  async getMetrics() {
    return {
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      cpu: process.cpuUsage(),
      platform: process.platform,
      nodeVersion: process.version,
    };
  }
} 