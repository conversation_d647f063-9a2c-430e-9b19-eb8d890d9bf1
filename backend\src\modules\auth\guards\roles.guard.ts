import { Injectable, CanActivate, ExecutionContext, ForbiddenException } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { ROLES_KEY } from '../decorators/roles.decorator';
import { RoleLevel } from '.prisma/client';

/**
 * Roles Guard
 * 
 * Production-ready role-based access control (RBAC) guard that:
 * - Checks user roles against required roles
 * - Supports role hierarchy (SUPER_ADMIN > ORG_ADMIN > DEVELOPER > VIEWER)
 * - Works with @Roles() decorator
 * - Must be used after AuthGuard
 */
@Injectable()
export class RolesGuard implements CanActivate {
  private readonly roleHierarchy: Record<RoleLevel, number> = {
    SUPER_ADMIN: 4,
    ORG_ADMIN: 3,
    DEVELOPER: 2,
    VIEWER: 1,
  };

  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    // Get required roles from decorator
    const requiredRoles = this.reflector.getAllAndOverride<RoleLevel[]>(ROLES_KEY, [
      context.getHandler(),
      context.getClass(),
    ]);

    if (!requiredRoles || requiredRoles.length === 0) {
      return true; // No specific roles required
    }

    // Get user from request (set by AuthGuard)
    const request = context.switchToHttp().getRequest();
    const user = request.user;

    if (!user || !user.role) {
      throw new ForbiddenException('User role not found');
    }

    // Check if user has required role or higher
    const userRoleLevel = this.roleHierarchy[user.role as RoleLevel] || 0;
    const hasRequiredRole = requiredRoles.some(role => {
      const requiredLevel = this.roleHierarchy[role];
      return userRoleLevel >= requiredLevel;
    });

    if (!hasRequiredRole) {
      throw new ForbiddenException(
        `Access denied. Required role: ${requiredRoles.join(' or ')}`
      );
    }

    return true;
  }
} 