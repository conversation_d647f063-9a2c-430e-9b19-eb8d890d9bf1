import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';
import { AuthService } from '../services/auth.service';

/**
 * Local Strategy
 * 
 * Validates user credentials (email/password).
 * Used by LocalAuthGuard for login endpoint.
 */
@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
  constructor(private readonly authService: AuthService) {
    super({
      usernameField: 'email', // Use email instead of username
      passwordField: 'password',
    });
  }

  /**
   * Validate user credentials
   */
  async validate(email: string, password: string): Promise<any> {
    try {
      const result = await this.authService.login({ email, password });
      
      if (!result) {
        throw new UnauthorizedException('Invalid credentials');
      }

      return result;
    } catch (error) {
      throw new UnauthorizedException('Invalid credentials');
    }
  }
} 