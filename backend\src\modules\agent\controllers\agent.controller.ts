import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Request } from 'express';
import { AgentService, CreateAgentDto, UpdateAgentDto } from '../services/agent.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../../auth/guards/roles.guard';
import { OrganizationGuard } from '../../auth/guards/organization.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { AgentExecutionService } from '../services/agent-execution.service';
import { AgentTemplateService } from '../services/agent-template.service';
import { AgentVersioningService } from '../services/agent-versioning.service';
import { AgentAnalyticsService } from '../services/agent-analytics.service';
import { AgentMarketplaceService } from '../services/agent-marketplace.service';

interface JwtPayload {
  sub: string;
  email: string;
  organizationId: string;
  role: string;
}

/**
 * Agent Controller
 * 
 * REST API for agent management:
 * - Create, read, update, delete agents
 * - List and search agents
 * - Execute agent with inputs
 * - Clone and version agents
 */
@ApiTags('agents')
@Controller('api/v1/agents')
@UseGuards(JwtAuthGuard, RolesGuard, OrganizationGuard)
@ApiBearerAuth()
export class AgentController {
  private readonly logger = new Logger(AgentController.name);

  constructor(
    private readonly agentService: AgentService,
    private readonly agentExecutionService: AgentExecutionService,
    private readonly agentTemplateService: AgentTemplateService,
    private readonly agentVersioningService: AgentVersioningService,
    private readonly agentAnalyticsService: AgentAnalyticsService,
    private readonly agentMarketplaceService: AgentMarketplaceService,
  ) {}

  /**
   * Create a new agent
   */
  @Post()
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Create a new agent' })
  @ApiBody({ schema: { 
    properties: {
      name: { type: 'string' },
      description: { type: 'string' },
      configuration: { type: 'object' },
      metadata: { type: 'object' }
    }
  }})
  @ApiResponse({ status: 201, description: 'Agent created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid input' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async createAgent(@Body() data: CreateAgentDto, @Req() req: Request) {
    try {
      const user = req['user'] as JwtPayload;
      
      const agent = await this.agentService.createAgent(
        user.sub,
        user.organizationId,
        data,
      );

      return agent;
    } catch (error) {
      this.logger.error(`Failed to create agent: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to create agent',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get agent by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get agent by ID' })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiResponse({ status: 200, description: 'Agent retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async getAgent(@Param('id') id: string, @Req() req: Request) {
    try {
      const user = req['user'] as JwtPayload;
      
      const agent = await this.agentService.getAgent(
        id,
        user.sub,
        user.organizationId,
      );

      if (!agent) {
        throw new HttpException('Agent not found', HttpStatus.NOT_FOUND);
      }

      return agent;
    } catch (error) {
      this.logger.error(`Failed to get agent: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to get agent',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Update agent
   */
  @Put(':id')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Update agent' })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiBody({ schema: { 
    properties: {
      name: { type: 'string' },
      description: { type: 'string' },
      configuration: { type: 'object' },
      status: { type: 'string' }
    }
  }})
  @ApiResponse({ status: 200, description: 'Agent updated successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async updateAgent(
    @Param('id') id: string,
    @Body() data: UpdateAgentDto,
    @Req() req: Request,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      const agent = await this.agentService.updateAgent(
        id,
        user.sub,
        user.organizationId,
        data,
      );

      return agent;
    } catch (error) {
      this.logger.error(`Failed to update agent: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to update agent',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Delete agent
   */
  @Delete(':id')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Delete agent' })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiResponse({ status: 200, description: 'Agent deleted successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async deleteAgent(@Param('id') id: string, @Req() req: Request) {
    try {
      const user = req['user'] as JwtPayload;
      
      await this.agentService.deleteAgent(
        id,
        user.sub,
        user.organizationId,
      );

      return { message: 'Agent deleted successfully' };
    } catch (error) {
      this.logger.error(`Failed to delete agent: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to delete agent',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * List agents
   */
  @Get()
  @ApiOperation({ summary: 'List agents' })
  @ApiQuery({ name: 'status', required: false, description: 'Filter by status' })
  @ApiQuery({ name: 'templateId', required: false, description: 'Filter by template ID' })
  @ApiQuery({ name: 'search', required: false, description: 'Search by name or description' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limit number of results' })
  @ApiQuery({ name: 'offset', required: false, description: 'Offset for pagination' })
  @ApiQuery({ name: 'orderBy', required: false, description: 'Order by field' })
  @ApiQuery({ name: 'order', required: false, description: 'Order direction' })
  @ApiResponse({ status: 200, description: 'Agents retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async listAgents(
    @Req() req: Request,
    @Query('status') status?: string,
    @Query('templateId') templateId?: string,
    @Query('search') search?: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
    @Query('orderBy') orderBy?: 'createdAt' | 'updatedAt' | 'name',
    @Query('order') order?: 'asc' | 'desc',
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      const result = await this.agentService.listAgents(
        user.organizationId,
        {
          status: status as any,
          templateId,
          search,
          limit: limit ? parseInt(limit.toString(), 10) : undefined,
          offset: offset ? parseInt(offset.toString(), 10) : undefined,
          orderBy,
          order,
        },
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to list agents: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to list agents',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Execute agent
   */
  @Post(':id/execute')
  @ApiOperation({ summary: 'Execute agent' })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiBody({ schema: { properties: { input: { type: 'string' }, sessionId: { type: 'string' } } } })
  @ApiResponse({ status: 200, description: 'Agent executed successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  
  async executeAgent(
    @Param('id') id: string,
    @Body() data: { input: string; sessionId?: string },
    @Req() req: Request,
  ): Promise<any> {
    try {
      const user = req['user'] as JwtPayload;
      
      const result = await this.agentExecutionService.AgentExecuteResult(
        id,
        data.input,
        user.sub,
        user.organizationId,
        data.sessionId,
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to execute agent: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to execute agent',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Clone agent
   */
  @Post(':id/clone')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Clone agent' })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiBody({ schema: { properties: { name: { type: 'string' } } } })
  @ApiResponse({ status: 201, description: 'Agent cloned successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async cloneAgent(
    @Param('id') id: string,
    @Body() data: { name: string },
    @Req() req: Request,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      const agent = await this.agentService.cloneAgent(
        id,
        user.sub,
        user.organizationId,
        data.name,
      );

      return agent;
    } catch (error) {
      this.logger.error(`Failed to clone agent: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to clone agent',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Activate agent
   */
  @Put(':id/activate')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Activate agent' })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiResponse({ status: 200, description: 'Agent activated successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async activateAgent(@Param('id') id: string, @Req() req: Request) {
    try {
      const user = req['user'] as JwtPayload;
      
      const agent = await this.agentService.activateAgent(
        id,
        user.sub,
        user.organizationId,
      );

      return agent;
    } catch (error) {
      this.logger.error(`Failed to activate agent: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to activate agent',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Deactivate agent
   */
  @Put(':id/deactivate')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Deactivate agent' })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiResponse({ status: 200, description: 'Agent deactivated successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async deactivateAgent(@Param('id') id: string, @Req() req: Request) {
    try {
      const user = req['user'] as JwtPayload;
      
      const agent = await this.agentService.deactivateAgent(
        id,
        user.sub,
        user.organizationId,
      );

      return agent;
    } catch (error) {
      this.logger.error(`Failed to deactivate agent: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to deactivate agent',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Test agent
   */
  @Post(':id/test')
  @ApiOperation({ summary: 'Test agent' })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiBody({ schema: { properties: { input: { type: 'string' } } } })
  @ApiResponse({ status: 200, description: 'Agent test completed' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async testAgent(
    @Param('id') id: string,
    @Body() data: { input: string },
    @Req() req: Request,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      const result = await this.agentExecutionService.testAgent(
        id,
        data.input,
        user.sub,
        user.organizationId,
      );

      return result;
    } catch (error) {
      this.logger.error(`Failed to test agent: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to test agent',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get agent performance analytics
   */
  @Get(':id/analytics')
  @ApiOperation({ summary: 'Get agent performance analytics' })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiQuery({ name: 'startDate', required: false, description: 'Start date for analytics period' })
  @ApiQuery({ name: 'endDate', required: false, description: 'End date for analytics period' })
  @ApiResponse({ status: 200, description: 'Analytics retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async getAgentAnalytics(
    @Req() req: Request,
    @Param('id') id: string,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      // Default to last 30 days if no dates provided
      const endDateObj = endDate ? new Date(endDate) : new Date();
      const startDateObj = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

      const result = await this.agentAnalyticsService.getAgentPerformance(
        id,
        user.organizationId,
        {
              startDate: startDateObj,
              endDate: endDateObj,
        },
      );

      return result;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to get agent analytics: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to get agent analytics',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get optimization suggestions for an agent
   */
  @Get(':id/optimization')
  @ApiOperation({ summary: 'Get optimization suggestions for agent' })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiResponse({ status: 200, description: 'Optimization suggestions retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async getOptimizationSuggestions(
    @Param('id') id: string,
    @Req() req: Request,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      const result = await this.agentAnalyticsService.getOptimizationSuggestions(
        id,
        user.organizationId,
      );

      return result;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to get optimization suggestions: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to get optimization suggestions',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Create agent version
   */
  @Post(':id/versions')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Create new version of agent' })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiBody({ schema: { properties: { version: { type: 'string' }, configuration: { type: 'object' } } } })
  @ApiResponse({ status: 201, description: 'Agent version created successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async createAgentVersion(
    @Param('id') id: string,
    @Body() data: { version?: string; configuration?: any },
    @Req() req: Request,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      const result = await this.agentVersioningService.createVersion(
        id,
        user.sub,
        user.organizationId,
        data,
      );

      return result;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to create agent version: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to create agent version',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get agent version history
   */
  @Get(':id/versions')
  @ApiOperation({ summary: 'Get agent version history' })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiResponse({ status: 200, description: 'Version history retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async getAgentVersionHistory(
    @Param('id') id: string,
    @Req() req: Request,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      const result = await this.agentVersioningService.getVersionHistory(
        id,
        user.sub,
        user.organizationId,
      );

      return result;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to get agent version history: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to get agent version history',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Publish agent to marketplace
   */
  @Post(':id/publish')
  @Roles('SUPER_ADMIN', 'ORG_ADMIN', 'DEVELOPER')
  @ApiOperation({ summary: 'Publish agent to marketplace' })
  @ApiParam({ name: 'id', description: 'Agent ID' })
  @ApiBody({ 
    schema: { 
      properties: { 
        isPublic: { type: 'boolean' },
        category: { type: 'string' },
        tags: { type: 'array', items: { type: 'string' } },
        pricingModel: { type: 'string', enum: ['FREE', 'PAID', 'SUBSCRIPTION'] },
        price: { type: 'number' }
      } 
    } 
  })
  @ApiResponse({ status: 200, description: 'Agent published successfully' })
  @ApiResponse({ status: 404, description: 'Agent not found' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async publishAgent(
    @Param('id') id: string,
    @Body() data: {
      isPublic: boolean;
      category?: string;
      tags?: string[];
      pricingModel?: 'FREE' | 'PAID' | 'SUBSCRIPTION';
      price?: number;
    },
    @Req() req: Request,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      const result = await this.agentMarketplaceService.publishAgent(
        id,
        user.sub,
        user.organizationId,
        data,
      );

      return result;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to publish agent: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to publish agent',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get available AI models
   */
  @Get('models/available')
  @ApiOperation({ summary: 'Get available AI models' })
  @ApiResponse({ status: 200, description: 'Available models retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async getAvailableModels() {
    try {
      const result = this.agentExecutionService.getAvailableModels();
      return result;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to get available models: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to get available models',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Test AI provider connectivity
   */
  @Post('models/:model/test')
  @ApiOperation({ summary: 'Test AI provider connectivity' })
  @ApiParam({ name: 'model', description: 'Model name to test' })
  @ApiResponse({ status: 200, description: 'Provider test completed' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  @ApiResponse({ status: 403, description: 'Forbidden' })
  async testProvider(
    @Param('model') model: string,
  ) {
    try {
      const result = await this.agentExecutionService.testProvider(model);
      return result;
    } catch (error: unknown) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to test provider: ${err.message}`, err.stack);
      throw new HttpException(
        err.message || 'Failed to test provider',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
} 