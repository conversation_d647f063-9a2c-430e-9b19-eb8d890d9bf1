import { NextRequest, NextResponse } from 'next/server';
import { getSession } from '@/lib/auth';
import { getBackendUrl } from '@/lib/config';

// GET /api/v1/tools/[id]/analytics
export async function GET(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getSession();
    
    if (!session) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const backendUrl = getBackendUrl();
    const url = `${backendUrl}/api/v1/tools/${params.id}/analytics`;
    
    // Get query parameters
    const { searchParams } = new URL(req.url);
    const queryString = searchParams.toString();
    
    // Forward the request to the backend
    const response = await fetch(
      queryString ? `${url}?${queryString}` : url,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.accessToken}`,
        },
        cache: 'no-store',
      }
    );
    
    // Get the response data
    const data = await response.json().catch(() => ({}));
    
    // Return the response
    return NextResponse.json(
      data,
      { status: response.status }
    );
  } catch (error) {
    console.error('Error fetching tool analytics:', error);
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    );
  }
} 