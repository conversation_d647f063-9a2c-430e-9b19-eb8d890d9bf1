import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ConfigService } from '@nestjs/config';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { AuthService } from '../services/auth.service';

/**
 * JWT Strategy
 * 
 * Validates JWT tokens and extracts user information.
 * Used by JwtAuthGuard for protected routes.
 */
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(
    private readonly authService: AuthService,
    private readonly configService: ConfigService,
  ) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: configService.get<string>('JWT_SECRET'),
    });
  }

  /**
   * Validate JWT payload and return user
   */
  async validate(payload: any) {
    try {
      // Validate the token payload
      const validatedPayload = await this.authService.validateToken(payload);
      
      if (!validatedPayload) {
        throw new UnauthorizedException('Invalid token');
      }

      // Return the payload as the user object
      return validatedPayload;
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }
} 