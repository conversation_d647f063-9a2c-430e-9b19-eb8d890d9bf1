import { Injectable, UnauthorizedException } from '@nestjs/common';
import { AbilityBuilder, PureAbility, AbilityClass, ExtractSubjectType, InferSubjects } from '@casl/ability';
import { PrismaService } from '../../prisma/prisma.service';

// Define actions that can be performed
type Actions = 'manage' | 'create' | 'read' | 'update' | 'delete' | 'execute' | 'publish';

// Define subjects (resources) that actions can be performed on
type Subjects = InferSubjects<typeof Agent | typeof Tool | typeof Hybrid | typeof Organization | typeof User> | 'all';

// Define the Ability type
export type AppAbility = PureAbility<[Actions, Subjects]>;

// Mock entities for CASL (these should match your actual Prisma models)
class Agent {}
class Tool {}
class Hybrid {}
class Organization {}
class User {}

@Injectable()
export class CaslAbilityFactory {
  constructor(private readonly prismaService: PrismaService) {}

  async createForUser(userId: string): Promise<AppAbility> {
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
      include: {
        role: true,
        organization: true,
      },
    });

    if (!user) {
      throw new UnauthorizedException('User not found');
    }

    const { can, cannot, build } = new AbilityBuilder<AppAbility>(PureAbility as AbilityClass<AppAbility>);

    // Define permissions based on role
    switch (user.role.name) {
      case 'SUPER_ADMIN':
        // Super admins can do anything
        can('manage', 'all');
        break;

      case 'ORG_ADMIN':
        // Organization admins can manage everything within their org
        can('manage', Agent, { organizationId: user.organizationId });
        can('manage', Tool, { organizationId: user.organizationId });
        can('manage', Hybrid, { organizationId: user.organizationId });
        can('manage', User, { organizationId: user.organizationId });
        can('read', Organization, { id: user.organizationId });
        can('update', Organization, { id: user.organizationId });
        break;

      case 'DEVELOPER':
        // Developers can create and manage their own resources
        can('create', Agent, { organizationId: user.organizationId });
        can('read', Agent, { organizationId: user.organizationId });
        can('update', Agent, { createdById: user.id });
        can('delete', Agent, { createdById: user.id });
        can('execute', Agent, { organizationId: user.organizationId });
        
        can('create', Tool, { organizationId: user.organizationId });
        can('read', Tool, { organizationId: user.organizationId });
        can('update', Tool, { createdById: user.id });
        can('delete', Tool, { createdById: user.id });
        
        can('create', Hybrid, { organizationId: user.organizationId });
        can('read', Hybrid, { organizationId: user.organizationId });
        can('update', Hybrid, { createdById: user.id });
        can('delete', Hybrid, { createdById: user.id });
        
        can('read', Organization, { id: user.organizationId });
        can('read', User, { organizationId: user.organizationId });
        break;

      case 'VIEWER':
        // Viewers can only read resources in their organization
        can('read', Agent, { organizationId: user.organizationId });
        can('read', Tool, { organizationId: user.organizationId });
        can('read', Hybrid, { organizationId: user.organizationId });
        can('read', Organization, { id: user.organizationId });
        can('read', User, { organizationId: user.organizationId });
        break;

      default:
        // No permissions for unknown roles
        cannot('manage', 'all');
    }

    return build({
      detectSubjectType: (item: any) =>
        item.constructor as ExtractSubjectType<Subjects>,
    });
  }

  // Helper method to check if user can perform action on subject
  async checkAbility(userId: string, action: Actions, subject: any, resource?: any): Promise<boolean> {
    const ability = await this.createForUser(userId);
    return ability.can(action, subject, resource);
  }
} 