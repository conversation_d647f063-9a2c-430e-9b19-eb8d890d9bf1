import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { SessionMemory, Message } from './session.service';

/**
 * Session Memory Service
 * 
 * Manages session memory with intelligent truncation and optimization:
 * - Token counting and estimation
 * - Smart memory truncation preserving context
 * - Memory summarization
 * - Context preservation
 */
@Injectable()
export class SessionMemoryService {
  private readonly logger = new Logger(SessionMemoryService.name);
  private readonly TOKENS_PER_CHAR = 0.25; // Rough estimate for token calculation
  private readonly SYSTEM_MESSAGES_WEIGHT = 2; // System messages are more important
  private readonly RECENT_MESSAGES_COUNT = 4; // Always keep the most recent messages

  constructor(private readonly configService: ConfigService) {}

  /**
   * Estimate tokens in content
   */
  estimateTokens(content: string): number {
    if (!content) return 0;
    return Math.ceil(content.length * this.TOKENS_PER_CHAR);
  }

  /**
   * Truncate memory to fit within token limits
   */
  truncateMemory(memory: SessionMemory): SessionMemory {
    const { messages, maxTokens } = memory;
    
    if (!messages.length) return memory;
    
    // If we're under the limit, no need to truncate
    if (memory.totalTokens <= (maxTokens || 4000)) return memory;

    this.logger.log(`Truncating memory from ${memory.totalTokens} tokens to fit ${maxTokens || 4000} tokens`);
    
    // Always keep system messages and recent messages
    const systemMessages = messages.filter(m => m.role === 'system');
    const nonSystemMessages = messages.filter(m => m.role !== 'system');
    
    // Keep the most recent messages
    const recentMessages = nonSystemMessages.slice(-this.RECENT_MESSAGES_COUNT);
    
    // Calculate tokens for preserved messages
    const systemTokens = systemMessages.reduce((sum, m) => sum + (m.tokens || 0), 0);
    const recentTokens = recentMessages.reduce((sum, m) => sum + (m.tokens || 0), 0);
    const preservedTokens = systemTokens + recentTokens;
    
    // If preserved messages already exceed the limit, we need more aggressive truncation
    if (preservedTokens > (maxTokens || 4000)) {
      // Keep only the most important system messages and the very latest messages
      const sortedSystemMessages = [...systemMessages].sort((a, b) => 
        (b.metadata?.importance || 0) - (a.metadata?.importance || 0)
      );
      
      const truncatedMemory = this.fitMessagesInTokenLimit(
        [...sortedSystemMessages, ...recentMessages.slice(-2)],
        maxTokens || 4000
      );
      
      return {
        ...memory,
        messages: truncatedMemory,
        totalTokens: truncatedMemory.reduce((sum, m) => sum + (m.tokens || 0), 0),
      };
    }
    
    // We have room for some older messages
    const remainingTokens = (maxTokens || 4000) - preservedTokens;
    const oldMessages = nonSystemMessages.slice(0, -this.RECENT_MESSAGES_COUNT);
    
    // Select messages to keep based on importance and recency
    const scoredMessages = oldMessages.map((message, index) => {
      const recencyScore = index / oldMessages.length; // 0 to 1, higher for more recent
      const importanceScore = message.metadata?.importance || 0.5;
      return {
        message,
        score: (recencyScore * 0.7) + (importanceScore * 0.3), // Weight recency higher
      };
    });
    
    // Sort by score (highest first)
    scoredMessages.sort((a, b) => b.score - a.score);
    
    // Add messages until we hit the token limit
    let currentTokens = 0;
    const additionalMessages: Message[] = [];
    
    for (const { message } of scoredMessages) {
      const messageTokens = message.tokens || 0;
      if (currentTokens + messageTokens <= remainingTokens) {
        additionalMessages.push(message);
        currentTokens += messageTokens;
      }
    }
    
    // Combine all messages and sort by timestamp
    const allMessages = [...systemMessages, ...additionalMessages, ...recentMessages]
      .sort((a, b) => (a.timestamp?.getTime() || 0) - (b.timestamp?.getTime() || 0));
    
    return {
      ...memory,
      messages: allMessages,
      totalTokens: preservedTokens + currentTokens,
    };
  }

  /**
   * Fit messages within token limit
   */
  private fitMessagesInTokenLimit(messages: Message[], tokenLimit: number): Message[] {
    let totalTokens = 0;
    const result: Message[] = [];
    if (!messages) return [];
    
    // Process messages in reverse (newest first)
    for (let i = messages.length - 1; i >= 0; i--) {
      const message = messages[i];
      if (!message) continue;

      const messageTokens = message.tokens || 0;

      // Always include system messages if possible
      if (message.role === 'system' && totalTokens + messageTokens <= tokenLimit) {
        result.unshift(message); // Add to beginning
        totalTokens += messageTokens;
      } 
      // Add non-system messages if we have space
      else if (message.role !== 'system' && totalTokens + messageTokens <= tokenLimit) {
        result.unshift(message); // Add to beginning
        totalTokens += messageTokens;
      }

      // Stop if we're out of space
      if (totalTokens >= tokenLimit) break;
    }

    if (!result) return [];

    return result;
  }

  /**
   * Summarize memory context
   */
  summarizeMemory(memory: SessionMemory): string {
    if (!memory) return '';

    const messageCount = memory.messages.length;
    const userMessages = memory.messages.filter(m => m.role === 'user').length;
    const assistantMessages = memory.messages.filter(m => m.role === 'assistant').length;
    const systemMessages = memory.messages.filter(m => m.role === 'system').length;
    const toolMessages = memory.messages.filter(m => m.role === 'tool').length;
    
    return `Conversation with ${userMessages} user messages, ${assistantMessages} assistant responses, ${systemMessages} system messages, and ${toolMessages} tool calls. Total tokens: ${memory.totalTokens}.`;
  }
} 