import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { Prisma } from '.prisma/client';



@Injectable()
export class SessionAnalyticsService {
  private readonly logger = new Logger(SessionAnalyticsService.name);

  constructor(private readonly prismaService: PrismaService) {}

  /**
   * Get active session count for an organization
   */
  async getActiveSessionCount(organizationId: string): Promise<number> {
    return this.prismaService.session.count({
      where: {
        organizationId,
        status: 'ACTIVE',
      },
    });
  }

  /**
   * Get session metrics for an organization
   */
  async getSessionMetrics(organizationId: string, days: number = 7): Promise<any> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Get session count by day
    const sessionsByDay = await this.prismaService.session.groupBy({
      by: ['createdAt'],
      _count: {
        id: true,
      },
      where: {
        organizationId,
        createdAt: {
          gte: startDate,
        },
      },
      orderBy: {
        createdAt: 'asc',
      },
    });

    // Get session count by status
    const sessionsByStatus = await this.prismaService.session.groupBy({
      by: ['status'],
      _count: {
        id: true,
      },
      where: {
        organizationId,
        createdAt: {
          gte: startDate,
        },
      },
    });

    // Get average session duration
    const avgDuration = await this.prismaService.$queryRaw`
      SELECT AVG(EXTRACT(EPOCH FROM ("updatedAt" - "createdAt"))) as avg_duration
      FROM "Session"
      WHERE "organizationId" = ${organizationId}
      AND "createdAt" >= ${startDate}
    `;

    // Get user engagement
    const userEngagement = await this.prismaService.$queryRaw`
      SELECT 
        "userId", 
        COUNT(*) as session_count,
        AVG(EXTRACT(EPOCH FROM ("updatedAt" - "createdAt"))) as avg_duration
      FROM "Session"
      WHERE "organizationId" = ${organizationId}
      AND "createdAt" >= ${startDate}
      GROUP BY "userId"
      ORDER BY COUNT(*) DESC
      LIMIT 10
    `;

    return {
      sessionsByDay,
      sessionsByStatus,
      avgDuration,
      userEngagement,
      totalSessions: await this.prismaService.session.count({
        where: {
          organizationId,   
          createdAt: {
            gte: startDate,
          },
        },
      }),
      activeSessions: await this.getActiveSessionCount(organizationId),
    };
  }

  /**
   * Track session event
   */
  async trackSessionEvent(
    sessionId: string,
    eventType: string,
    metadata: Record<string, any> = {},
  ): Promise<void> {
    try {
      // Get session details
      const session = await this.prismaService.session.findUnique({
        where: { id: sessionId },
        select: {
          id: true,
          userId: true,
          organizationId: true,
        },
      });

      if (!session) {
        this.logger.warn(`Attempted to track event for non-existent session: ${sessionId}`);
        return;
      }

      // In a real implementation, we would store this in an analytics table
      // For now, we'll just log it
      this.logger.log(`Session event: ${eventType}`, {
        sessionId,
        userId: session.userId,
        organizationId: session.organizationId,
        eventType,
        metadata,
        timestamp: new Date(),
      });

      // In production, we would also emit this event to the analytics pipeline
      // await this.analyticsService.trackEvent('session', eventType, {
      //   sessionId,
      //   userId: session.userId,
      //   organizationId: session.organizationId,
      //   ...metadata,
      // });
    } catch (error) {
      this.logger.error(`Error tracking session event: ${eventType}`, error);
    }
  }

  /**
   * Get session usage report for billing
   */
  async getSessionUsageReport(organizationId: string, startDate: Date, endDate: Date): Promise<any> {
    // Get total sessions
    const totalSessions = await this.prismaService.session.count({
      where: {
        organizationId,
        createdAt: {
          gte: startDate,
          lte: endDate,
        },
      },
    });

    // Get total duration
    const totalDurationResult = await this.prismaService.$queryRaw`
      SELECT SUM(EXTRACT(EPOCH FROM ("updatedAt" - "createdAt"))) as total_duration
      FROM "Session"
      WHERE "organizationId" = ${organizationId}
      AND "createdAt" >= ${startDate}
      AND "createdAt" <= ${endDate}
      AND "status" != 'ACTIVE'
    `;

    // Get session count by agent
    const sessionsByAgent = await this.prismaService.$queryRaw`
      SELECT 
        "agentId", 
        COUNT(*) as count
      FROM "Session"
      WHERE "organizationId" = ${organizationId}
      AND "createdAt" >= ${startDate}
      AND "createdAt" <= ${endDate}
      AND "agentId" IS NOT NULL
      GROUP BY "agentId"
    `;

    // Get session count by day
    const sessionsByDay = await this.prismaService.$queryRaw`
      SELECT 
        DATE_TRUNC('day', "createdAt") as day,
        COUNT(*) as count
      FROM "Session"
      WHERE "organizationId" = ${organizationId}
      AND "createdAt" >= ${startDate}
      AND "createdAt" <= ${endDate}
      GROUP BY DATE_TRUNC('day', "createdAt")
      ORDER BY day ASC
    `;

    return {
      organizationId,
      period: {
        start: startDate,
        end: endDate,
      },
      totalSessions,
      totalDuration: totalDurationResult,
      sessionsByAgent,
      sessionsByDay,
    };
  }
} 