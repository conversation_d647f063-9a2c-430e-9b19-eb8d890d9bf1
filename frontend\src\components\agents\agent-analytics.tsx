'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3,
  TrendingUp,
  TrendingDown,
  Activity,
  Clock,
  Target,
  Users,
  Zap,
  Brain,
  Calendar,
  Download,
  RefreshCw,
  ArrowUpRight,
  ArrowDownRight,
  AlertCircle,
  CheckCircle2,
  Loader2
} from 'lucide-react';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
} from 'chart.js';
import axios from 'axios';
import { useToast } from '@/lib/usetoast';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ArcElement
);

interface AnalyticsData {
  overview: {
    totalExecutions: number;
    successRate: number;
    avgResponseTime: number;
    totalUsers: number;
    executionsChange: number;
    successRateChange: number;
    responseTimeChange: number;
    usersChange: number;
  };
  timeSeries: {
    labels: string[];
    executions: number[];
    successRate: number[];
    responseTime: number[];
  };
  statusDistribution: {
    completed: number;
    failed: number;
    timeout: number;
    cancelled: number;
  };
  modelPerformance: {
    model: string;
    executions: number;
    successRate: number;
    avgResponseTime: number;
  }[];
  topErrors: {
    error: string;
    count: number;
    percentage: number;
  }[];
  userEngagement: {
    labels: string[];
    activeUsers: number[];
    newUsers: number[];
  };
}

interface AgentAnalyticsProps {
  agentId: string;
}

export default function AgentAnalytics({ agentId }: AgentAnalyticsProps) {
  const { showError } = useToast();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  const [agent, setAgent] = useState<any>(null);

  useEffect(() => {
    fetchAnalytics();
    fetchAgent();
  }, [agentId, timeRange]);

  const fetchAgent = async () => {
    try {
      const response = await axios.get(`/api/v1/agents/${agentId}`);
      setAgent(response.data);
    } catch (error: any) {
      showError('Failed to load agent details');
    }
  };

  const fetchAnalytics = async () => {
    setIsLoading(true);
    try {
      const endDate = new Date();
      const startDate = new Date();
      
      switch (timeRange) {
        case '24h':
          startDate.setHours(startDate.getHours() - 24);
          break;
        case '7d':
          startDate.setDate(startDate.getDate() - 7);
          break;
        case '30d':
          startDate.setDate(startDate.getDate() - 30);
          break;
        case '90d':
          startDate.setDate(startDate.getDate() - 90);
          break;
      }

      const response = await axios.get(`/api/v1/agents/${agentId}/analytics`, {
        params: {
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
        },
      });

      setAnalyticsData(response.data);
    } catch (error: any) {
      console.error('Error fetching analytics:', error);
      showError('Failed to load analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  const getChangeIndicator = (change: number) => {
    if (change > 0) {
      return (
        <div className="flex items-center gap-1 text-green-400">
          <ArrowUpRight className="w-4 h-4" />
          <span className="text-sm">+{change.toFixed(1)}%</span>
        </div>
      );
    } else if (change < 0) {
      return (
        <div className="flex items-center gap-1 text-red-400">
          <ArrowDownRight className="w-4 h-4" />
          <span className="text-sm">{change.toFixed(1)}%</span>
        </div>
      );
    } else {
      return (
        <div className="flex items-center gap-1 text-gray-400">
          <span className="text-sm">0%</span>
        </div>
      );
    }
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        labels: {
          color: '#fff',
        },
      },
    },
    scales: {
      x: {
        ticks: {
          color: '#9CA3AF',
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
      y: {
        ticks: {
          color: '#9CA3AF',
        },
        grid: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <Loader2 className="w-10 h-10 text-white animate-spin" />
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-red-400 mx-auto mb-4" />
          <h2 className="text-2xl font-semibold text-white mb-2">Failed to Load Analytics</h2>
          <p className="text-gray-400">Unable to fetch analytics data for this agent.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 mb-8"
        >
          <div>
            <h1 className="text-4xl font-bold text-white flex items-center gap-3">
              <BarChart3 className="w-10 h-10 text-blue-400" />
              Agent Analytics
            </h1>
            {agent && (
              <p className="text-gray-400 mt-2">
                Performance insights for <span className="text-white font-medium">{agent.name}</span>
              </p>
            )}
          </div>
          
          <div className="flex flex-wrap gap-3">
            {/* Time Range Selector */}
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
            >
              <option value="24h">Last 24 Hours</option>
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
            
            <button
              onClick={fetchAnalytics}
              className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-all flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Refresh
            </button>
            
            <button
              onClick={() => {
                // Export analytics data
                const dataStr = JSON.stringify(analyticsData, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(dataBlob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `agent-${agentId}-analytics-${timeRange}.json`;
                link.click();
              }}
              className="px-4 py-2 bg-blue-500/20 hover:bg-blue-500/30 rounded-lg text-blue-400 transition-all flex items-center gap-2"
            >
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </motion.div>

        {/* Overview Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8"
        >
          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-blue-500/20 rounded-lg">
                <Activity className="w-6 h-6 text-blue-400" />
              </div>
              {getChangeIndicator(analyticsData.overview.executionsChange)}
            </div>
            <h3 className="text-gray-400 text-sm font-medium">Total Executions</h3>
            <p className="text-3xl font-bold text-white">
              {analyticsData.overview.totalExecutions.toLocaleString()}
            </p>
          </div>

          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-500/20 rounded-lg">
                <Target className="w-6 h-6 text-green-400" />
              </div>
              {getChangeIndicator(analyticsData.overview.successRateChange)}
            </div>
            <h3 className="text-gray-400 text-sm font-medium">Success Rate</h3>
            <p className="text-3xl font-bold text-white">
              {analyticsData.overview.successRate.toFixed(1)}%
            </p>
          </div>

          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-yellow-500/20 rounded-lg">
                <Clock className="w-6 h-6 text-yellow-400" />
              </div>
              {getChangeIndicator(-analyticsData.overview.responseTimeChange)}
            </div>
            <h3 className="text-gray-400 text-sm font-medium">Avg Response Time</h3>
            <p className="text-3xl font-bold text-white">
              {analyticsData.overview.avgResponseTime}ms
            </p>
          </div>

          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-purple-500/20 rounded-lg">
                <Users className="w-6 h-6 text-purple-400" />
              </div>
              {getChangeIndicator(analyticsData.overview.usersChange)}
            </div>
            <h3 className="text-gray-400 text-sm font-medium">Active Users</h3>
            <p className="text-3xl font-bold text-white">
              {analyticsData.overview.totalUsers.toLocaleString()}
            </p>
          </div>
        </motion.div>

        {/* Charts Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Executions Over Time */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6"
          >
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-blue-400" />
              Execution Trends
            </h3>
            <div className="h-64">
              <Line
                data={{
                  labels: analyticsData.timeSeries.labels,
                  datasets: [
                    {
                      label: 'Executions',
                      data: analyticsData.timeSeries.executions,
                      borderColor: 'rgb(59, 130, 246)',
                      backgroundColor: 'rgba(59, 130, 246, 0.1)',
                      tension: 0.4,
                    },
                  ],
                }}
                options={chartOptions}
              />
            </div>
          </motion.div>

          {/* Success Rate Over Time */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6"
          >
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
              <Target className="w-5 h-5 text-green-400" />
              Success Rate Trends
            </h3>
            <div className="h-64">
              <Line
                data={{
                  labels: analyticsData.timeSeries.labels,
                  datasets: [
                    {
                      label: 'Success Rate (%)',
                      data: analyticsData.timeSeries.successRate,
                      borderColor: 'rgb(34, 197, 94)',
                      backgroundColor: 'rgba(34, 197, 94, 0.1)',
                      tension: 0.4,
                    },
                  ],
                }}
                options={{
                  ...chartOptions,
                  scales: {
                    ...chartOptions.scales,
                    y: {
                      ...chartOptions.scales.y,
                      min: 0,
                      max: 100,
                    },
                  },
                }}
              />
            </div>
          </motion.div>

          {/* Status Distribution */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6"
          >
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
              <CheckCircle2 className="w-5 h-5 text-emerald-400" />
              Execution Status
            </h3>
            <div className="h-64">
              <Doughnut
                data={{
                  labels: ['Completed', 'Failed', 'Timeout', 'Cancelled'],
                  datasets: [
                    {
                      data: [
                        analyticsData.statusDistribution.completed,
                        analyticsData.statusDistribution.failed,
                        analyticsData.statusDistribution.timeout,
                        analyticsData.statusDistribution.cancelled,
                      ],
                      backgroundColor: [
                        'rgba(34, 197, 94, 0.8)',
                        'rgba(239, 68, 68, 0.8)',
                        'rgba(245, 158, 11, 0.8)',
                        'rgba(156, 163, 175, 0.8)',
                      ],
                      borderColor: [
                        'rgb(34, 197, 94)',
                        'rgb(239, 68, 68)',
                        'rgb(245, 158, 11)',
                        'rgb(156, 163, 175)',
                      ],
                      borderWidth: 2,
                    },
                  ],
                }}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: '#fff',
                        padding: 20,
                      },
                    },
                  },
                }}
              />
            </div>
          </motion.div>

          {/* Response Time Trends */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6"
          >
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
              <Clock className="w-5 h-5 text-yellow-400" />
              Response Time Trends
            </h3>
            <div className="h-64">
              <Bar
                data={{
                  labels: analyticsData.timeSeries.labels,
                  datasets: [
                    {
                      label: 'Response Time (ms)',
                      data: analyticsData.timeSeries.responseTime,
                      backgroundColor: 'rgba(245, 158, 11, 0.8)',
                      borderColor: 'rgb(245, 158, 11)',
                      borderWidth: 1,
                    },
                  ],
                }}
                options={chartOptions}
              />
            </div>
          </motion.div>
        </div>

        {/* Additional Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Model Performance */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6"
          >
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
              <Brain className="w-5 h-5 text-indigo-400" />
              Model Performance
            </h3>
            <div className="space-y-4">
              {analyticsData.modelPerformance.map((model, index) => (
                <div key={index} className="bg-white/5 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-white font-medium">{model.model}</h4>
                    <span className="text-gray-400 text-sm">
                      {model.executions} executions
                    </span>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-gray-400 text-xs">Success Rate</p>
                      <p className="text-green-400 font-semibold">
                        {model.successRate.toFixed(1)}%
                      </p>
                    </div>
                    <div>
                      <p className="text-gray-400 text-xs">Avg Response</p>
                      <p className="text-yellow-400 font-semibold">
                        {model.avgResponseTime}ms
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </motion.div>

          {/* Top Errors */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6"
          >
            <h3 className="text-xl font-semibold text-white mb-6 flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-red-400" />
              Top Error Types
            </h3>
            <div className="space-y-4">
              {analyticsData.topErrors.length > 0 ? (
                analyticsData.topErrors.map((error, index) => (
                  <div key={index} className="bg-white/5 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-white font-medium text-sm truncate">
                        {error.error}
                      </h4>
                      <span className="text-red-400 text-sm font-semibold">
                        {error.percentage.toFixed(1)}%
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex-1 bg-gray-700 rounded-full h-2 mr-3">
                        <div
                          className="bg-red-400 h-2 rounded-full"
                          style={{ width: `${error.percentage}%` }}
                        />
                      </div>
                      <span className="text-gray-400 text-xs">
                        {error.count} occurrences
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <CheckCircle2 className="w-12 h-12 text-green-400 mx-auto mb-3" />
                  <p className="text-gray-400">No errors in this time period</p>
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
}