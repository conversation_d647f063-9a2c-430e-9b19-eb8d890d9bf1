'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON>, 
  <PERSON>ting<PERSON>, 
  Play, 
  Save,
  Sparkles,
  MessageSquare,
  Zap,
  Code,
  Eye,
  Layers,
  Loader2,
  AlertCircle,
  ChevronDown,
  ChevronUp,
  Plus,
  Minus,
  HelpCircle,
  Upload,
  Download,
  TestTube,
  Rocket,
  Target,
  Clock,
  BarChart3
} from 'lucide-react';
import { useToast } from '@/lib/usetoast';
import { useRouter } from 'next/navigation';
import axios from 'axios';

interface AgentBuilderProps {
  agentId?: string;
  mode?: 'create' | 'edit';
}

interface ModelConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
  stopSequences: string[];
}

interface AgentConfig {
  name: string;
  description: string;
  systemPrompt: string;
  modelConfig: ModelConfig;
  capabilities: string[];
  tools: string[];
  category: string;
  tags: string[];
  isPublic: boolean;
}

interface TestResult {
  success: boolean;
  output?: string;
  duration?: number;
  error?: string;
  metadata?: Record<string, any>;
}

const availableModels = [
  { value: 'gpt-4', label: 'GPT-4', provider: 'OpenAI', description: 'Most capable model' },
  { value: 'gpt-4-turbo-preview', label: 'GPT-4 Turbo', provider: 'OpenAI', description: 'Latest GPT-4 with improved speed' },
  { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo', provider: 'OpenAI', description: 'Fast and efficient' },
  { value: 'claude-3-opus', label: 'Claude 3 Opus', provider: 'Anthropic', description: 'Most powerful Claude model' },
  { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet', provider: 'Anthropic', description: 'Balanced performance' },
  { value: 'claude-3-haiku', label: 'Claude 3 Haiku', provider: 'Anthropic', description: 'Fast and affordable' },
  { value: 'gemini-pro', label: 'Gemini Pro', provider: 'Google', description: 'Google\'s latest model' },
];

const agentCapabilities = [
  'Text Generation',
  'Code Generation', 
  'Data Analysis',
  'Question Answering',
  'Creative Writing',
  'Translation',
  'Summarization',
  'Conversation',
  'Problem Solving',
  'Research',
];

const agentCategories = [
  'Customer Support',
  'Content Creation',
  'Development',
  'Education',
  'Business Intelligence',
  'Entertainment',
  'Healthcare',
  'Finance',
  'Marketing',
  'General Purpose',
];

export default function AgentBuilder({ agentId, mode = 'create' }: AgentBuilderProps) {
  const router = useRouter();
  const { showSuccess, showError } = useToast();
  
  // Main state
  const [config, setConfig] = useState<AgentConfig>({
    name: '',
    description: '',
    systemPrompt: '',
    modelConfig: {
      model: 'gpt-4',
      temperature: 0.7,
      maxTokens: 2048,
      topP: 1.0,
      frequencyPenalty: 0,
      presencePenalty: 0,
      stopSequences: [],
    },
    capabilities: [],
    tools: [],
    category: '',
    tags: [],
    isPublic: false,
  });

  // UI state
  const [activeTab, setActiveTab] = useState('basic');
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    basic: true,
    prompt: true,
    model: false,
    capabilities: false,
    tools: false,
    advanced: false,
  });

  // Action states
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [testInput, setTestInput] = useState('');
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  
  // Validation
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (mode === 'edit' && agentId) {
      loadAgent();
    }
  }, [agentId, mode]);

  const loadAgent = async () => {
    if (!agentId) return;
    
    setIsLoading(true);
    try {
      const response = await axios.get(`/api/v1/agents/${agentId}`);
      const agent = response.data;
      
      setConfig({
        name: agent.name,
        description: agent.description || '',
        systemPrompt: agent.configuration?.systemPrompt || '',
        modelConfig: {
          model: agent.configuration?.model || 'gpt-4',
          temperature: agent.configuration?.temperature || 0.7,
          maxTokens: agent.configuration?.maxTokens || 2048,
          topP: agent.configuration?.topP || 1.0,
          frequencyPenalty: agent.configuration?.frequencyPenalty || 0,
          presencePenalty: agent.configuration?.presencePenalty || 0,
          stopSequences: agent.configuration?.stopSequences || [],
        },
        capabilities: agent.capabilities || [],
        tools: agent.tools || [],
        category: agent.category || '',
        tags: agent.tags || [],
        isPublic: agent.isPublic || false,
      });
    } catch (error: any) {
      showError('Failed to load agent');
      console.error('Error loading agent:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const validateConfig = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!config.name.trim()) {
      newErrors.name = 'Agent name is required';
    }

    if (!config.systemPrompt.trim()) {
      newErrors.systemPrompt = 'System prompt is required';
    }

    if (config.modelConfig.temperature < 0 || config.modelConfig.temperature > 2) {
      newErrors.temperature = 'Temperature must be between 0 and 2';
    }

    if (config.modelConfig.maxTokens < 1 || config.modelConfig.maxTokens > 32000) {
      newErrors.maxTokens = 'Max tokens must be between 1 and 32000';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateConfig()) {
      showError('Please fix validation errors');
      return;
    }

    setIsSaving(true);
    try {
      const payload = {
        name: config.name,
        description: config.description,
        configuration: {
          systemPrompt: config.systemPrompt,
          ...config.modelConfig,
        },
        capabilities: config.capabilities,
        tools: config.tools,
        category: config.category,
        tags: config.tags,
        isPublic: config.isPublic,
      };

      let response;
      if (mode === 'create') {
        response = await axios.post('/api/v1/agents', payload);
        showSuccess('Agent created successfully!');
      } else {
        response = await axios.put(`/api/v1/agents/${agentId}`, payload);
        showSuccess('Agent updated successfully!');
      }

      if (mode === 'create') {
        router.push(`/dashboard/agents/${response.data.id}`);
      }
    } catch (error: any) {
      showError(error.response?.data?.message || 'Failed to save agent');
      console.error('Error saving agent:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleTest = async () => {
    if (!testInput.trim()) {
      showError('Please enter test input');
      return;
    }

    if (!validateConfig()) {
      showError('Please fix configuration errors first');
      return;
    }

    setIsTesting(true);
    setTestResult(null);

    try {
      const response = await axios.post(`/api/v1/agents/${agentId}/test`, {
        input: testInput,
      });

      setTestResult(response.data);
      if (response.data.success) {
        showSuccess('Test completed successfully!');
      } else {
        showError('Test failed');
      }
    } catch (error: any) {
      const errorResult: TestResult = {
        success: false,
        error: error.response?.data?.message || 'Test failed',
      };
      setTestResult(errorResult);
      showError('Test failed');
    } finally {
      setIsTesting(false);
    }
  };

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const addTag = (tag: string) => {
    if (tag && !config.tags.includes(tag)) {
      setConfig(prev => ({
        ...prev,
        tags: [...prev.tags, tag],
      }));
    }
  };

  const removeTag = (tagToRemove: string) => {
    setConfig(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove),
    }));
  };

  const toggleCapability = (capability: string) => {
    setConfig(prev => ({
      ...prev,
      capabilities: prev.capabilities.includes(capability)
        ? prev.capabilities.filter(c => c !== capability)
        : [...prev.capabilities, capability],
    }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <Loader2 className="w-10 h-10 text-white animate-spin" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex justify-between items-center mb-8"
        >
          <div>
            <h1 className="text-4xl font-bold text-white flex items-center gap-3">
              <Brain className="w-10 h-10 text-blue-400" />
              {mode === 'create' ? 'Create Agent' : 'Edit Agent'}
            </h1>
            <p className="text-gray-400 mt-2">
              {mode === 'create' 
                ? 'Build your AI agent with advanced customization options'
                : `Editing: ${config.name}`
              }
            </p>
          </div>
          <div className="flex gap-3">
            <button
              onClick={() => router.push('/dashboard/agents')}
              className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-all"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white hover:opacity-90 transition-all disabled:opacity-50 flex items-center gap-2"
            >
              {isSaving ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  {mode === 'create' ? 'Create Agent' : 'Save Changes'}
                </>
              )}
            </button>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Configuration Panel */}
          <div className="lg:col-span-2 space-y-6">
            {/* Basic Information */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden"
            >
              <button
                onClick={() => toggleSection('basic')}
                className="w-full p-6 flex items-center justify-between hover:bg-white/5 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-500/20 rounded-lg">
                    <Settings className="w-5 h-5 text-blue-400" />
                  </div>
                  <h2 className="text-xl font-semibold text-white">Basic Information</h2>
                </div>
                {expandedSections.basic ? (
                  <ChevronUp className="w-5 h-5 text-gray-400" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-gray-400" />
                )}
              </button>
              
              <AnimatePresence>
                {expandedSections.basic && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="border-t border-white/10"
                  >
                    <div className="p-6 space-y-6">
                      <div>
                        <label className="text-white text-sm font-medium mb-2 block">
                          Agent Name *
                        </label>
                        <input
                          type="text"
                          value={config.name}
                          onChange={(e) => setConfig(prev => ({ ...prev, name: e.target.value }))}
                          placeholder="Enter agent name..."
                          className={`w-full px-4 py-3 bg-white/10 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40 ${
                            errors.name ? 'border-red-500' : 'border-white/20'
                          }`}
                        />
                        {errors.name && (
                          <p className="text-red-400 text-sm mt-1">{errors.name}</p>
                        )}
                      </div>

                      <div>
                        <label className="text-white text-sm font-medium mb-2 block">
                          Description
                        </label>
                        <textarea
                          value={config.description}
                          onChange={(e) => setConfig(prev => ({ ...prev, description: e.target.value }))}
                          placeholder="Describe what your agent does..."
                          rows={3}
                          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="text-white text-sm font-medium mb-2 block">
                            Category
                          </label>
                          <select
                            value={config.category}
                            onChange={(e) => setConfig(prev => ({ ...prev, category: e.target.value }))}
                            className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
                          >
                            <option value="">Select category...</option>
                            {agentCategories.map(category => (
                              <option key={category} value={category}>{category}</option>
                            ))}
                          </select>
                        </div>

                        <div>
                          <label className="text-white text-sm font-medium mb-2 block">
                            Visibility
                          </label>
                          <label className="flex items-center gap-3 p-3 bg-white/5 rounded-lg cursor-pointer">
                            <input
                              type="checkbox"
                              checked={config.isPublic}
                              onChange={(e) => setConfig(prev => ({ ...prev, isPublic: e.target.checked }))}
                              className="form-checkbox h-4 w-4 text-blue-500"
                            />
                            <span className="text-white text-sm">Make agent public</span>
                          </label>
                        </div>
                      </div>

                      {/* Tags */}
                      <div>
                        <label className="text-white text-sm font-medium mb-2 block">
                          Tags
                        </label>
                        <div className="flex flex-wrap gap-2 mb-2">
                          {config.tags.map(tag => (
                            <span
                              key={tag}
                              className="px-3 py-1 bg-blue-500/20 text-blue-400 rounded-full text-sm flex items-center gap-1"
                            >
                              {tag}
                              <button
                                onClick={() => removeTag(tag)}
                                className="hover:text-blue-300"
                              >
                                <Minus className="w-3 h-3" />
                              </button>
                            </span>
                          ))}
                        </div>
                        <input
                          type="text"
                          placeholder="Add tags (press Enter)..."
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') {
                              addTag(e.currentTarget.value.trim());
                              e.currentTarget.value = '';
                            }
                          }}
                          className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40"
                        />
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>

            {/* System Prompt */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.1 }}
              className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden"
            >
              <button
                onClick={() => toggleSection('prompt')}
                className="w-full p-6 flex items-center justify-between hover:bg-white/5 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-500/20 rounded-lg">
                    <MessageSquare className="w-5 h-5 text-purple-400" />
                  </div>
                  <h2 className="text-xl font-semibold text-white">System Prompt</h2>
                  <div className="flex items-center gap-1 text-yellow-400">
                    <HelpCircle className="w-4 h-4" />
                    <span className="text-xs">Required</span>
                  </div>
                </div>
                {expandedSections.prompt ? (
                  <ChevronUp className="w-5 h-5 text-gray-400" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-gray-400" />
                )}
              </button>
              
              <AnimatePresence>
                {expandedSections.prompt && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="border-t border-white/10"
                  >
                    <div className="p-6">
                      <textarea
                        value={config.systemPrompt}
                        onChange={(e) => setConfig(prev => ({ ...prev, systemPrompt: e.target.value }))}
                        placeholder="Define your agent's behavior, role, and instructions..."
                        rows={8}
                        className={`w-full px-4 py-3 bg-white/10 border rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40 font-mono text-sm ${
                          errors.systemPrompt ? 'border-red-500' : 'border-white/20'
                        }`}
                      />
                      {errors.systemPrompt && (
                        <p className="text-red-400 text-sm mt-1">{errors.systemPrompt}</p>
                      )}
                      <p className="text-gray-400 text-xs mt-2">
                        This defines how your agent behaves and responds. Be specific about the role, tone, and capabilities.
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Model Configuration */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
              className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden"
            >
              <button
                onClick={() => toggleSection('model')}
                className="w-full p-6 flex items-center justify-between hover:bg-white/5 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-500/20 rounded-lg">
                    <Brain className="w-5 h-5 text-green-400" />
                  </div>
                  <h2 className="text-xl font-semibold text-white">AI Model Configuration</h2>
                </div>
                {expandedSections.model ? (
                  <ChevronUp className="w-5 h-5 text-gray-400" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-gray-400" />
                )}
              </button>
              
              <AnimatePresence>
                {expandedSections.model && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="border-t border-white/10"
                  >
                    <div className="p-6 space-y-6">
                      <div>
                        <label className="text-white text-sm font-medium mb-2 block">
                          AI Model
                        </label>
                        <select
                          value={config.modelConfig.model}
                          onChange={(e) => setConfig(prev => ({
                            ...prev,
                            modelConfig: { ...prev.modelConfig, model: e.target.value }
                          }))}
                          className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
                        >
                          {availableModels.map(model => (
                            <option key={model.value} value={model.value}>
                              {model.label} ({model.provider}) - {model.description}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="text-white text-sm font-medium mb-2 block">
                            Temperature: {config.modelConfig.temperature}
                          </label>
                          <input
                            type="range"
                            min="0"
                            max="2"
                            step="0.1"
                            value={config.modelConfig.temperature}
                            onChange={(e) => setConfig(prev => ({
                              ...prev,
                              modelConfig: { ...prev.modelConfig, temperature: parseFloat(e.target.value) }
                            }))}
                            className="w-full"
                          />
                          <div className="flex justify-between text-xs text-gray-400 mt-1">
                            <span>Conservative (0)</span>
                            <span>Creative (2)</span>
                          </div>
                          {errors.temperature && (
                            <p className="text-red-400 text-sm mt-1">{errors.temperature}</p>
                          )}
                        </div>

                        <div>
                          <label className="text-white text-sm font-medium mb-2 block">
                            Max Tokens
                          </label>
                          <input
                            type="number"
                            min="1"
                            max="32000"
                            value={config.modelConfig.maxTokens}
                            onChange={(e) => setConfig(prev => ({
                              ...prev,
                              modelConfig: { ...prev.modelConfig, maxTokens: parseInt(e.target.value) }
                            }))}
                            className={`w-full px-4 py-3 bg-white/10 border rounded-lg text-white focus:outline-none focus:border-white/40 ${
                              errors.maxTokens ? 'border-red-500' : 'border-white/20'
                            }`}
                          />
                          {errors.maxTokens && (
                            <p className="text-red-400 text-sm mt-1">{errors.maxTokens}</p>
                          )}
                        </div>
                      </div>

                      {/* Advanced Model Settings */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="text-white text-sm font-medium mb-2 block">
                            Top P: {config.modelConfig.topP}
                          </label>
                          <input
                            type="range"
                            min="0"
                            max="1"
                            step="0.1"
                            value={config.modelConfig.topP}
                            onChange={(e) => setConfig(prev => ({
                              ...prev,
                              modelConfig: { ...prev.modelConfig, topP: parseFloat(e.target.value) }
                            }))}
                            className="w-full"
                          />
                        </div>

                        <div>
                          <label className="text-white text-sm font-medium mb-2 block">
                            Frequency Penalty: {config.modelConfig.frequencyPenalty}
                          </label>
                          <input
                            type="range"
                            min="0"
                            max="2"
                            step="0.1"
                            value={config.modelConfig.frequencyPenalty}
                            onChange={(e) => setConfig(prev => ({
                              ...prev,
                              modelConfig: { ...prev.modelConfig, frequencyPenalty: parseFloat(e.target.value) }
                            }))}
                            className="w-full"
                          />
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>

            {/* Capabilities */}
            <motion.div
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.3 }}
              className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 overflow-hidden"
            >
              <button
                onClick={() => toggleSection('capabilities')}
                className="w-full p-6 flex items-center justify-between hover:bg-white/5 transition-colors"
              >
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-yellow-500/20 rounded-lg">
                    <Zap className="w-5 h-5 text-yellow-400" />
                  </div>
                  <h2 className="text-xl font-semibold text-white">Capabilities</h2>
                </div>
                {expandedSections.capabilities ? (
                  <ChevronUp className="w-5 h-5 text-gray-400" />
                ) : (
                  <ChevronDown className="w-5 h-5 text-gray-400" />
                )}
              </button>
              
              <AnimatePresence>
                {expandedSections.capabilities && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    className="border-t border-white/10"
                  >
                    <div className="p-6">
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                        {agentCapabilities.map(capability => (
                          <label
                            key={capability}
                            className="flex items-center gap-2 p-3 bg-white/5 rounded-lg cursor-pointer hover:bg-white/10 transition-colors"
                          >
                            <input
                              type="checkbox"
                              checked={config.capabilities.includes(capability)}
                              onChange={() => toggleCapability(capability)}
                              className="form-checkbox h-4 w-4 text-blue-500"
                            />
                            <span className="text-white text-sm">{capability}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          </div>

          {/* Test Panel */}
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 p-6"
              >
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-emerald-500/20 rounded-lg">
                    <TestTube className="w-5 h-5 text-emerald-400" />
                  </div>
                  <h3 className="text-lg font-semibold text-white">Test Agent</h3>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="text-white text-sm font-medium mb-2 block">
                      Test Input
                    </label>
                    <textarea
                      value={testInput}
                      onChange={(e) => setTestInput(e.target.value)}
                      placeholder="Enter a message to test your agent..."
                      rows={4}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40"
                    />
                  </div>

                  <button
                    onClick={handleTest}
                    disabled={isTesting || !config.name || !config.systemPrompt}
                    className="w-full flex items-center justify-center gap-2 px-4 py-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-lg text-white hover:opacity-90 transition-all disabled:opacity-50"
                  >
                    {isTesting ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin" />
                        Testing...
                      </>
                    ) : (
                      <>
                        <Play className="w-4 h-4" />
                        Run Test
                      </>
                    )}
                  </button>

                  {testResult && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`p-4 rounded-lg border ${
                        testResult.success
                          ? 'bg-emerald-500/20 border-emerald-500/40'
                          : 'bg-red-500/20 border-red-500/40'
                      }`}
                    >
                      {testResult.success ? (
                        <div>
                          <div className="flex items-center gap-2 text-emerald-400 font-medium mb-2">
                            <Target className="w-4 h-4" />
                            Test Successful
                          </div>
                          {testResult.duration && (
                            <div className="flex items-center gap-2 text-gray-400 text-sm mb-2">
                              <Clock className="w-3 h-3" />
                              {testResult.duration}ms
                            </div>
                          )}
                          <div className="text-white text-sm bg-white/10 rounded p-3">
                            {testResult.output}
                          </div>
                        </div>
                      ) : (
                        <div>
                          <div className="flex items-center gap-2 text-red-400 font-medium mb-2">
                            <AlertCircle className="w-4 h-4" />
                            Test Failed
                          </div>
                          <div className="text-red-200 text-sm">
                            {testResult.error}
                          </div>
                        </div>
                      )}
                    </motion.div>
                  )}
                </div>

                {/* Quick Actions */}
                <div className="border-t border-white/10 pt-6 mt-6">
                  <h4 className="text-white font-medium mb-3">Quick Actions</h4>
                  <div className="space-y-2">
                    <button
                      onClick={() => router.push(`/dashboard/agents/${agentId}/analytics`)}
                      disabled={mode === 'create'}
                      className="w-full flex items-center gap-2 px-3 py-2 bg-white/5 rounded-lg text-white hover:bg-white/10 transition-colors disabled:opacity-50"
                    >
                      <BarChart3 className="w-4 h-4" />
                      View Analytics
                    </button>
                    <button
                      onClick={() => router.push(`/dashboard/agents/${agentId}/versions`)}
                      disabled={mode === 'create'}
                      className="w-full flex items-center gap-2 px-3 py-2 bg-white/5 rounded-lg text-white hover:bg-white/10 transition-colors disabled:opacity-50"
                    >
                      <Layers className="w-4 h-4" />
                      Version History
                    </button>
                    <button
                      onClick={() => router.push(`/dashboard/agents/${agentId}/deploy`)}
                      disabled={mode === 'create'}
                      className="w-full flex items-center gap-2 px-3 py-2 bg-white/5 rounded-lg text-white hover:bg-white/10 transition-colors disabled:opacity-50"
                    >
                      <Rocket className="w-4 h-4" />
                      Deploy Agent
                    </button>
                  </div>
                </div>
              </motion.div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}