import {
  Controller,
  Post,
  Get,
  Delete,
  Body,
  Query,
  Param,
  HttpCode,
  HttpStatus,
  UseGuards,
  Logger,
  ValidationPipe,
  UsePipes,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiQuery,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';
import { VectorDatabaseService } from '../services/vector-database.service';
import {
  VectorSearchRequest,
  VectorSearchResponse,
  VectorUpsertRequest,
  VectorUpsertResponse,
  VectorDeleteRequest,
  VectorDeleteResponse,
  VectorIndexStats,
  VectorBatchRequest,
  VectorBatchResponse,
  VectorSimilaritySearchRequest,
  VectorSimilaritySearchResponse,
  KnowledgeDocument,
  SearchFilter,
} from '@shared/types/vector-database.types';

@ApiTags('Vector Database')
@Controller('vector-database')
@ApiBearerAuth()
export class VectorDatabaseController {
  private readonly logger = new Logger(VectorDatabaseController.name);

  constructor(private readonly vectorDatabaseService: VectorDatabaseService) {}

  @Post('search')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Search for similar vectors',
    description: 'Perform vector similarity search using either a query string or vector array',
  })
  @ApiBody({
    type: 'object',
    description: 'Vector search request',
    examples: {
      textQuery: {
        summary: 'Search with text query',
        value: {
          query: 'machine learning algorithms',
          topK: 10,
          includeMetadata: true,
          namespace: 'knowledge-base',
        },
      },
      vectorQuery: {
        summary: 'Search with vector',
        value: {
          vector: [0.1, 0.2, 0.3],
          topK: 5,
          filter: { category: 'technology' },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Search results returned successfully',
    type: 'object',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid search request',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async search(@Body() request: VectorSearchRequest): Promise<VectorSearchResponse> {
    this.logger.log(`Vector search request: ${JSON.stringify(request, null, 2)}`);
    return this.vectorDatabaseService.search(request);
  }

  @Post('upsert')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Upsert vectors',
    description: 'Insert or update vectors in the database',
  })
  @ApiBody({
    type: 'object',
    description: 'Vector upsert request',
    examples: {
      withEmbeddings: {
        summary: 'Upsert with pre-computed embeddings',
        value: {
          vectors: [
            {
              id: 'doc_1',
              values: [0.1, 0.2, 0.3],
              metadata: { title: 'Document 1', category: 'AI' },
            },
          ],
          namespace: 'documents',
        },
      },
      withText: {
        summary: 'Upsert with text (auto-embedding)',
        value: {
          vectors: [
            {
              id: 'doc_2',
              metadata: {
                text: 'This is a document about machine learning',
                title: 'ML Guide',
                category: 'AI',
              },
            },
          ],
          embeddingModel: 'text-embedding-ada-002',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Vectors upserted successfully',
    type: 'object',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid upsert request',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async upsert(@Body() request: VectorUpsertRequest): Promise<VectorUpsertResponse> {
    this.logger.log(`Vector upsert request for ${request.vectors.length} vectors`);
    return this.vectorDatabaseService.upsert(request);
  }

  @Delete('vectors')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete vectors',
    description: 'Delete vectors by IDs or delete all vectors in a namespace',
  })
  @ApiBody({
    type: 'object',
    description: 'Vector delete request',
    examples: {
      deleteByIds: {
        summary: 'Delete specific vectors',
        value: {
          ids: ['doc_1', 'doc_2'],
          namespace: 'documents',
        },
      },
      deleteAll: {
        summary: 'Delete all vectors in namespace',
        value: {
          deleteAll: true,
          namespace: 'temp-documents',
        },
      },
      deleteByFilter: {
        summary: 'Delete vectors matching filter',
        value: {
          filter: { category: 'outdated' },
          namespace: 'documents',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Vectors deleted successfully',
    type: 'object',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Invalid delete request',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async deleteVectors(@Body() request: VectorDeleteRequest): Promise<VectorDeleteResponse> {
    this.logger.log(`Vector delete request: ${JSON.stringify(request, null, 2)}`);
    return this.vectorDatabaseService.delete(request);
  }

  @Get('stats')
  @ApiOperation({
    summary: 'Get index statistics',
    description: 'Retrieve statistics about the vector index',
  })
  @ApiQuery({
    name: 'namespace',
    required: false,
    description: 'Namespace to get stats for',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Index statistics retrieved successfully',
    type: 'object',
  })
  async getIndexStats(@Query('namespace') namespace?: string): Promise<VectorIndexStats> {
    this.logger.log(`Getting index stats for namespace: ${namespace || 'default'}`);
    return this.vectorDatabaseService.getIndexStats(namespace);
  }

  @Post('batch')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Batch operations',
    description: 'Execute multiple vector operations in a single request',
  })
  @ApiBody({
    type: 'object',
    description: 'Batch request with multiple operations',
    examples: {
      mixedOperations: {
        summary: 'Multiple operation types',
        value: {
          operations: [
            {
              id: 'op_1',
              type: 'upsert',
              namespace: 'docs',
              data: {
                vectors: [
                  {
                    id: 'doc_1',
                    metadata: { text: 'Sample document' },
                  },
                ],
              },
            },
            {
              id: 'op_2',
              type: 'search',
              namespace: 'docs',
              data: {
                query: 'sample query',
                topK: 5,
              },
            },
          ],
          embeddingModel: 'text-embedding-ada-002',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Batch operations completed',
    type: 'object',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async batch(@Body() request: VectorBatchRequest): Promise<VectorBatchResponse> {
    this.logger.log(`Batch request with ${request.operations.length} operations`);
    return this.vectorDatabaseService.batch(request);
  }

  @Post('similarity-search')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Advanced similarity search',
    description: 'Perform similarity search with advanced filtering and ranking',
  })
  @ApiBody({
    type: 'object',
    description: 'Similarity search request',
    examples: {
      advancedSearch: {
        summary: 'Advanced similarity search',
        value: {
          query: 'artificial intelligence machine learning',
          topK: 10,
          minScore: 0.7,
          rerank: true,
          filter: { category: 'AI', published: true },
          namespace: 'knowledge-base',
          includeValues: false,
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Similarity search results',
    type: 'object',
  })
  @UsePipes(new ValidationPipe({ transform: true }))
  async similaritySearch(
    @Body() request: VectorSimilaritySearchRequest
  ): Promise<VectorSimilaritySearchResponse> {
    this.logger.log(`Similarity search: ${request.query.substring(0, 100)}...`);
    return this.vectorDatabaseService.similaritySearch(request);
  }

  @Post('knowledge/documents')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Add knowledge document',
    description: 'Add a knowledge document to the vector database with automatic chunking',
  })
  @ApiBody({
    type: 'object',
    description: 'Knowledge document to add',
    examples: {
      document: {
        summary: 'Add knowledge document',
        value: {
          document: {
            id: 'kb_doc_1',
            title: 'Introduction to Machine Learning',
            content: 'Machine learning is a subset of artificial intelligence...',
            source: 'https://example.com/ml-guide',
            type: 'article',
            metadata: {
              author: 'AI Expert',
              category: 'AI',
              tags: ['machine-learning', 'AI', 'tutorial'],
            },
          },
          namespace: 'knowledge-base',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Knowledge document added successfully',
    type: 'object',
  })
  async addKnowledgeDocument(
    @Body() body: { document: KnowledgeDocument; namespace?: string }
  ): Promise<VectorUpsertResponse> {
    this.logger.log(`Adding knowledge document: ${body.document.title}`);
    return this.vectorDatabaseService.addKnowledgeDocument(body.document, body.namespace);
  }

  @Post('knowledge/search')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Search knowledge base',
    description: 'Search through knowledge documents with semantic understanding',
  })
  @ApiBody({
    type: 'object',
    description: 'Knowledge search request',
    examples: {
      knowledgeSearch: {
        summary: 'Search knowledge base',
        value: {
          query: 'How does machine learning work?',
          options: {
            topK: 5,
            minScore: 0.7,
            filter: { category: 'AI' },
            namespace: 'knowledge-base',
            includeContent: true,
          },
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Knowledge search results',
    type: 'object',
  })
  async searchKnowledge(
    @Body()
    body: {
      query: string;
      options?: {
        topK?: number;
        filter?: SearchFilter;
        namespace?: string;
        minScore?: number;
        includeContent?: boolean;
      };
    }
  ): Promise<VectorSimilaritySearchResponse> {
    this.logger.log(`Knowledge search: ${body.query.substring(0, 100)}...`);
    return this.vectorDatabaseService.searchKnowledge(body.query, body.options);
  }

  @Get('health')
  @ApiOperation({
    summary: 'Health check',
    description: 'Check the health status of the vector database',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Health check results',
    type: 'object',
  })
  async healthCheck(): Promise<{ status: 'healthy' | 'unhealthy'; details: any }> {
    this.logger.log('Vector database health check requested');
    return this.vectorDatabaseService.healthCheck();
  }

  @Delete('namespace/:namespace')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Delete namespace',
    description: 'Delete all vectors in a specific namespace',
  })
  @ApiParam({
    name: 'namespace',
    description: 'Namespace to delete',
    type: String,
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Namespace deleted successfully',
    type: 'object',
  })
  async deleteNamespace(@Param('namespace') namespace: string): Promise<VectorDeleteResponse> {
    this.logger.log(`Deleting namespace: ${namespace}`);
    return this.vectorDatabaseService.delete({
      deleteAll: true,
      namespace,
    });
  }

  @Get('namespaces')
  @ApiOperation({
    summary: 'List namespaces',
    description: 'Get statistics for all namespaces',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Namespace statistics',
    type: 'object',
  })
  async listNamespaces(): Promise<VectorIndexStats> {
    this.logger.log('Listing all namespaces');
    return this.vectorDatabaseService.getIndexStats();
  }
} 