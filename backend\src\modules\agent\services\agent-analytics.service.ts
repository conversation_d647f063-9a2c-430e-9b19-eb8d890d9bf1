import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { Prisma } from '.prisma/client';

/**
 * Agent Analytics Service
 * 
 * Tracks and analyzes agent performance:
 * - Usage metrics and patterns
 * - Performance analytics
 * - Cost tracking and optimization
 * - User engagement insights
 */
@Injectable()
export class AgentAnalyticsService {
  private readonly logger = new Logger(AgentAnalyticsService.name);

  constructor(private readonly prismaService: PrismaService) {}

  /**
   * Get agent performance metrics
   */
  async getAgentPerformance(
    agentId: string,
    organizationId: string,
    period: {
      startDate: Date;
      endDate: Date;
    },
  ): Promise<any> {
    try {
      // Verify agent exists and belongs to organization
      const agent = await this.prismaService.agent.findFirst({
        where: {
          id: agentId,
          organizationId,
          deletedAt: null,
        },
      });

      if (!agent) {
        throw new Error('Agent not found');
      }

      // Get execution metrics
      const executionMetrics = await this.prismaService.$queryRaw<any[]>`
        SELECT
          COUNT(*) as total_executions,
          SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as successful_executions,
          SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_executions,
          AVG(CASE WHEN status = 'COMPLETED' THEN duration ELSE NULL END) as avg_duration,
          MIN(CASE WHEN status = 'COMPLETED' THEN duration ELSE NULL END) as min_duration,
          MAX(CASE WHEN status = 'COMPLETED' THEN duration ELSE NULL END) as max_duration,
          SUM(CASE WHEN "tokenUsage"->>'total' IS NOT NULL THEN ("tokenUsage"->>'total')::float ELSE 0 END) as total_tokens,
          SUM(cost) as total_cost
        FROM "agent_executions"
        WHERE "agentId" = ${agentId}
        AND "startedAt" >= ${period.startDate}
        AND "startedAt" <= ${period.endDate}
      `;

      // Get daily execution counts
      const dailyExecutions = await this.prismaService.$queryRaw<any[]>`
        SELECT
          DATE_TRUNC('day', "startedAt") as day,
          COUNT(*) as executions,
          SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as successful_executions,
          SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_executions
        FROM "agent_executions"
        WHERE "agentId" = ${agentId}
        AND "startedAt" >= ${period.startDate}
        AND "startedAt" <= ${period.endDate}
        GROUP BY DATE_TRUNC('day', "startedAt")
        ORDER BY day ASC
      `;

      // Get user engagement metrics
      const userEngagement = await this.prismaService.$queryRaw<any[]>`
        SELECT
          "userId",
          COUNT(*) as executions,
          AVG(duration) as avg_duration
        FROM "agent_executions"
        WHERE "agentId" = ${agentId}
        AND "startedAt" >= ${period.startDate}
        AND "startedAt" <= ${period.endDate}
        AND "userId" IS NOT NULL
        GROUP BY "userId"
        ORDER BY executions DESC
        LIMIT 10
      `;

      // Get session metrics
      const sessionMetrics = await this.prismaService.$queryRaw<any[]>`
        SELECT
          COUNT(DISTINCT "sessionId") as total_sessions,
          AVG(messages_per_session) as avg_messages_per_session
        FROM (
          SELECT
            "sessionId",
            COUNT(*) as messages_per_session
          FROM "messages"
          WHERE "sessionId" IN (
            SELECT DISTINCT "sessionId"
            FROM "agent_executions"
            WHERE "agentId" = ${agentId}
            AND "startedAt" >= ${period.startDate}
            AND "startedAt" <= ${period.endDate}
            AND "sessionId" IS NOT NULL
          )
          GROUP BY "sessionId"
        ) as session_counts
      `;

      // Calculate success rate
      const metrics = executionMetrics[0] || {};
      const successRate = metrics.total_executions > 0
        ? (metrics.successful_executions / metrics.total_executions) * 100
        : 0;

      // Calculate average cost per execution
      const avgCost = metrics.total_executions > 0
        ? metrics.total_cost / metrics.total_executions
        : 0;

      // Calculate average tokens per execution
      const avgTokens = metrics.total_executions > 0
        ? metrics.total_tokens / metrics.total_executions
        : 0;

      return {
        period: {
          startDate: period.startDate,
          endDate: period.endDate,
        },
        metrics: {
          totalExecutions: Number(metrics.total_executions) || 0,
          successfulExecutions: Number(metrics.successful_executions) || 0,
          failedExecutions: Number(metrics.failed_executions) || 0,
          successRate,
          avgDuration: Number(metrics.avg_duration) || 0,
          minDuration: Number(metrics.min_duration) || 0,
          maxDuration: Number(metrics.max_duration) || 0,
          totalTokens: Number(metrics.total_tokens) || 0,
          avgTokens,
          totalCost: Number(metrics.total_cost) || 0,
          avgCost,
        },
        dailyExecutions: dailyExecutions.map(day => ({
          day: day.day,
          executions: Number(day.executions) || 0,
          successfulExecutions: Number(day.successful_executions) || 0,
          failedExecutions: Number(day.failed_executions) || 0,
        })),
        userEngagement: userEngagement.map(user => ({
          userId: user.userId,
          executions: Number(user.executions) || 0,
          avgDuration: Number(user.avg_duration) || 0,
        })),
        sessionMetrics: {
          totalSessions: Number(sessionMetrics[0]?.total_sessions) || 0,
          avgMessagesPerSession: Number(sessionMetrics[0]?.avg_messages_per_session) || 0,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get agent performance: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw error;
    }
  }

  /**
   * Get organization-wide agent analytics
   */
  async getOrganizationAgentAnalytics(
    organizationId: string,
    period: {
      startDate: Date;
      endDate: Date;
    },
  ): Promise<any> {
    try {
      // Get organization-wide metrics
      const orgMetrics = await this.prismaService.$queryRaw<any[]>`
        SELECT
          COUNT(DISTINCT "agentId") as total_agents,
          COUNT(*) as total_executions,
          SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as successful_executions,
          AVG(CASE WHEN status = 'COMPLETED' THEN duration ELSE NULL END) as avg_duration,
          SUM(CASE WHEN "tokenUsage"->>'total' IS NOT NULL THEN ("tokenUsage"->>'total')::float ELSE 0 END) as total_tokens,
          SUM(cost) as total_cost
        FROM "agent_executions" ae
        JOIN "agents" a ON ae."agentId" = a.id
        WHERE a."organizationId" = ${organizationId}
        AND ae."startedAt" >= ${period.startDate}
        AND ae."startedAt" <= ${period.endDate}
      `;

      // Get top agents by execution count
      const topAgentsByExecutions = await this.prismaService.$queryRaw<any[]>`
        SELECT
          a.id as "agentId",
          a.name as "agentName",
          COUNT(*) as executions,
          SUM(CASE WHEN ae.status = 'COMPLETED' THEN 1 ELSE 0 END) as successful_executions,
          AVG(CASE WHEN ae.status = 'COMPLETED' THEN ae.duration ELSE NULL END) as avg_duration,
          SUM(ae.cost) as total_cost
        FROM "agent_executions" ae
        JOIN "agents" a ON ae."agentId" = a.id
        WHERE a."organizationId" = ${organizationId}
        AND ae."startedAt" >= ${period.startDate}
        AND ae."startedAt" <= ${period.endDate}
        GROUP BY a.id, a.name
        ORDER BY executions DESC
        LIMIT 10
      `;

      // Get daily execution counts
      const dailyExecutions = await this.prismaService.$queryRaw<any[]>`
        SELECT
          DATE_TRUNC('day', ae."startedAt") as day,
          COUNT(*) as executions,
          SUM(CASE WHEN ae.status = 'COMPLETED' THEN 1 ELSE 0 END) as successful_executions
        FROM "agent_executions" ae
        JOIN "agents" a ON ae."agentId" = a.id
        WHERE a."organizationId" = ${organizationId}
        AND ae."startedAt" >= ${period.startDate}
        AND ae."startedAt" <= ${period.endDate}
        GROUP BY DATE_TRUNC('day', ae."startedAt")
        ORDER BY day ASC
      `;

      // Get model usage breakdown
      const modelUsage = await this.prismaService.$queryRaw<any[]>`
        SELECT
          a.model,
          COUNT(*) as executions,
          SUM(ae.cost) as total_cost,
          SUM(CASE WHEN ae."tokenUsage"->>'total' IS NOT NULL THEN (ae."tokenUsage"->>'total')::float ELSE 0 END) as total_tokens
        FROM "agent_executions" ae
        JOIN "agents" a ON ae."agentId" = a.id
        WHERE a."organizationId" = ${organizationId}
        AND ae."startedAt" >= ${period.startDate}
        AND ae."startedAt" <= ${period.endDate}
        GROUP BY a.model
        ORDER BY executions DESC
      `;

      // Calculate success rate
      const metrics = orgMetrics[0] || {};
      const successRate = metrics.total_executions > 0
        ? (metrics.successful_executions / metrics.total_executions) * 100
        : 0;

      return {
        period: {
          startDate: period.startDate,
          endDate: period.endDate,
        },
        metrics: {
          totalAgents: Number(metrics.total_agents) || 0,
          totalExecutions: Number(metrics.total_executions) || 0,
          successfulExecutions: Number(metrics.successful_executions) || 0,
          successRate,
          avgDuration: Number(metrics.avg_duration) || 0,
          totalTokens: Number(metrics.total_tokens) || 0,
          totalCost: Number(metrics.total_cost) || 0,
        },
        topAgentsByExecutions: topAgentsByExecutions.map(agent => ({
          agentId: agent.agentId,
          agentName: agent.agentName,
          executions: Number(agent.executions) || 0,
          successfulExecutions: Number(agent.successful_executions) || 0,
          successRate: agent.executions > 0 
            ? (agent.successful_executions / agent.executions) * 100 
            : 0,
          avgDuration: Number(agent.avg_duration) || 0,
          totalCost: Number(agent.total_cost) || 0,
        })),
        dailyExecutions: dailyExecutions.map(day => ({
          day: day.day,
          executions: Number(day.executions) || 0,
          successfulExecutions: Number(day.successful_executions) || 0,
        })),
        modelUsage: modelUsage.map(model => ({
          model: model.model,
          executions: Number(model.executions) || 0,
          totalCost: Number(model.total_cost) || 0,
          totalTokens: Number(model.total_tokens) || 0,
          costPerExecution: model.executions > 0 
            ? model.total_cost / model.executions 
            : 0,
        })),
      };
    } catch (error) {
      this.logger.error(`Failed to get organization agent analytics: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw error;
    }
  }

  /**
   * Get optimization suggestions for an agent
   */
  async getOptimizationSuggestions(
    agentId: string,
    organizationId: string,
  ): Promise<any> {
    try {
      // Verify agent exists and belongs to organization
      const agent = await this.prismaService.agent.findFirst({
        where: {
          id: agentId,
          organizationId,
          deletedAt: null,
        },
      });

      if (!agent) {
        throw new Error('Agent not found');
      }

      // Get agent metrics for the last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const metrics = await this.getAgentPerformance(agentId, organizationId, {
        startDate: thirtyDaysAgo,
        endDate: new Date(),
      });

      // Generate optimization suggestions
      const suggestions = [];

      // Check for high token usage
      if (metrics.metrics.avgTokens > 5000) {
        suggestions.push({
          type: 'TOKEN_OPTIMIZATION',
          title: 'High token usage detected',
          description: 'Your agent is using a high number of tokens per execution. Consider optimizing the system prompt or using a more efficient model.',
          impact: 'HIGH',
          potentialSavings: (metrics.metrics.avgTokens - 3000) * 0.00001 * metrics.metrics.totalExecutions,
        });
      }

      // Check for low success rate
      if (metrics.metrics.successRate < 90 && metrics.metrics.totalExecutions > 10) {
        suggestions.push({
          type: 'SUCCESS_RATE',
          title: 'Low success rate',
          description: `Your agent has a ${metrics.metrics.successRate.toFixed(1)}% success rate. Review error patterns and improve error handling.`,
          impact: 'MEDIUM',
          details: {
            currentSuccessRate: metrics.metrics.successRate,
            targetSuccessRate: 95,
          },
        });
      }

      // Check for high response times
      if (metrics.metrics.avgDuration > 5000 && metrics.metrics.totalExecutions > 10) {
        suggestions.push({
          type: 'PERFORMANCE',
          title: 'Slow response times',
          description: `Your agent takes ${(metrics.metrics.avgDuration / 1000).toFixed(1)} seconds on average to respond. Consider optimizing the prompt or using a faster model.`,
          impact: 'MEDIUM',
          details: {
            currentAvgDuration: metrics.metrics.avgDuration,
            targetAvgDuration: 3000,
          },
        });
      }

      // Check for cost optimization
      if (metrics.metrics.avgCost > 0.05 && metrics.metrics.totalExecutions > 10) {
        suggestions.push({
          type: 'COST_OPTIMIZATION',
          title: 'High execution costs',
          description: `Your agent costs $${metrics.metrics.avgCost.toFixed(3)} per execution on average. Consider using a more cost-effective model or optimizing token usage.`,
          impact: 'HIGH',
          potentialSavings: (metrics.metrics.avgCost - 0.02) * metrics.metrics.totalExecutions,
        });
      }

      // Check for model recommendations
      if (agent.model === 'gpt-4' && metrics.metrics.avgTokens < 2000) {
        suggestions.push({
          type: 'MODEL_RECOMMENDATION',
          title: 'Consider using a more cost-effective model',
          description: 'Your agent uses GPT-4 but has relatively low token usage. Consider trying GPT-3.5-Turbo for non-complex tasks to reduce costs.',
          impact: 'MEDIUM',
          potentialSavings: metrics.metrics.totalExecutions * 0.03,
        });
      }

      return {
        suggestions,
        metrics: metrics.metrics,
      };
    } catch (error) {
      this.logger.error(`Failed to get optimization suggestions: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      throw error;
    }
  }

  /**
   * Track agent execution event
   */
  async trackExecutionEvent(
    executionId: string,
    event: {
      type: string;
      metadata?: Record<string, any>;
    },
  ): Promise<void> {
    try {
      // Get execution details
      const execution = await this.prismaService.agentExecution.findUnique({
        where: { id: executionId },
        include: {
          agent: true,
        },
      });

      if (!execution) {
        throw new Error('Execution not found');
      }

      // Create event record
      await this.prismaService.analyticsEvent.create({
        data: {
          event: event.type,
          category: 'agent_execution',
          action: 'execution_event',
          properties: {
            ...event.metadata,
            executionId,
            agentId: execution.agentId,
            agentName: execution.agent.name,
            model: execution.agent.model,
          } as unknown as Prisma.InputJsonValue,
          organizationId: execution.agent.organizationId,
          userId: execution.agent.createdById,
        },
      });

      this.logger.log(`Tracked agent execution event: ${event.type} for execution ${executionId}`);
    } catch (error) {
      this.logger.error(`Failed to track execution event: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      // Don't throw error for analytics tracking
    }
  }

  /**
   * Update agent performance metrics
   */
  async updateAgentPerformanceMetrics(agentId: string): Promise<void> {
    try {
      // Get agent
      const agent = await this.prismaService.agent.findUnique({
        where: { id: agentId },
      });

      if (!agent) {
        throw new Error('Agent not found');
      }

      // Get execution metrics
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const metrics = await this.prismaService.$queryRaw<any[]>`
        SELECT
          COUNT(*) as total_executions,
          SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as successful_executions,
          AVG(CASE WHEN status = 'COMPLETED' THEN duration ELSE NULL END) as avg_duration,
          SUM(CASE WHEN "tokenUsage"->>'total' IS NOT NULL THEN ("tokenUsage"->>'total')::float ELSE 0 END) as total_tokens,
          SUM(cost) as total_cost
        FROM "agent_executions"
        WHERE "agentId" = ${agentId}
        AND "startedAt" >= ${thirtyDaysAgo}
      `;

      // Calculate success rate
      const successRate = metrics[0].total_executions > 0
        ? (metrics[0].successful_executions / metrics[0].total_executions) * 100
        : 0;

      // Update agent performance metrics
      await this.prismaService.agent.update({
        where: { id: agentId },
        data: {
          performance: {
            lastUpdated: new Date(),
            period: '30d',
            totalExecutions: Number(metrics[0].total_executions) || 0,
            successRate,
            avgDuration: Number(metrics[0].avg_duration) || 0,
            totalTokens: Number(metrics[0].total_tokens) || 0,
            totalCost: Number(metrics[0].total_cost) || 0,
          } as unknown as Prisma.InputJsonValue,
        },
      });

      this.logger.log(`Updated performance metrics for agent: ${agentId}`);
    } catch (error) {
      this.logger.error(`Failed to update agent performance metrics: ${error instanceof Error ? error.message : 'Unknown error'}`, (error as Error).stack);
      // Don't throw error for analytics updates
    }
  }
} 