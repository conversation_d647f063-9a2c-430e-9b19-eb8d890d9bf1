import { useState, useEffect } from 'react';
import axios from 'axios';
import { Brain, MessageSquare, Code, Zap } from 'lucide-react';

interface Template {
  id: string;
  name: string;
  description: string;
  icon: any;
  color: string;
  configuration: {
    systemPrompt: string;
    model: string;
    temperature: number;
    maxTokens: number;
  };
}

// Icon mapping for dynamic rendering
const iconMap: Record<string, any> = {
  'brain': Brain,
  'message-square': MessageSquare,
  'code': Code,
  'zap': Zap,
};

export function useTemplate() {
  const [templates, setTemplates] = useState<Template[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchTemplates() {
      try {
        setIsLoading(true);
        
        // Use Next.js API route instead of direct backend call
        const response = await axios.get('/api/v1/templates');
        
        // Process templates to add icon components
        const processedTemplates = response.data.map((template: any) => ({
          ...template,
          icon: iconMap[template.iconKey] || Brain, // Default to Brain if icon not found
        }));
        
        setTemplates(processedTemplates);
      } catch (err: any) {
        console.error('Failed to fetch templates:', err);
        setError(err.message || 'Failed to fetch templates');
        
        // Fallback to default templates if API fails
        setTemplates([
          {
            id: 'customer-support',
            name: 'Customer Support',
            description: 'Handle customer inquiries and support requests',
            icon: MessageSquare,
            color: 'from-blue-500 to-blue-600',
            configuration: {
              systemPrompt: 'You are a helpful customer support agent.',
              model: 'gpt-4',
              temperature: 0.7,
              maxTokens: 2048
            }
          },
          {
            id: 'code-assistant',
            name: 'Code Assistant',
            description: 'Help with coding tasks and technical questions',
            icon: Code,
            color: 'from-emerald-500 to-emerald-600',
            configuration: {
              systemPrompt: 'You are a helpful coding assistant.',
              model: 'gpt-4',
              temperature: 0.3,
              maxTokens: 4096
            }
          }
        ]);
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchTemplates();
  }, []);

  return { templates, isLoading, error };
} 