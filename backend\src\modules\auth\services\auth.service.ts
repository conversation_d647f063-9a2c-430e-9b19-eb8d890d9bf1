import { Injectable, UnauthorizedException, BadRequestException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { RedisService } from '../../redis/redis.service';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';
import { User, Role, UserStatus } from '.prisma/client';

export interface RegisterDto {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  organizationName?: string;
  organizationSlug?: string;
}

export interface LoginDto {
  email: string;
  password: string;
  organizationId?: string;
}

export interface AuthTokens {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface JwtPayload {
  sub: string; // userId
  email: string;
  organizationId: string;
  role: string;
  permissions: string[];
  iat?: number;
  exp?: number;
}

/**
 * Authentication Service
 * 
 * Production-ready authentication with:
 * - JWT token management
 * - Organization-scoped authentication
 * - Session management via Redis
 * - Secure password hashing
 * - Token refresh mechanism
 */
@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private readonly jwtAccessExpiration: number;
  private readonly jwtRefreshExpiration: number;
  private readonly saltRounds = 12;

  constructor(
    private readonly prismaService: PrismaService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {
    this.jwtAccessExpiration = this.configService.get<number>('JWT_ACCESS_EXPIRATION_SECONDS', 900); // 15 minutes
    this.jwtRefreshExpiration = this.configService.get<number>('JWT_REFRESH_EXPIRATION_SECONDS', 604800); // 7 days
  }

  /**
   * Register a new user with organization
   */
  async register(dto: RegisterDto): Promise<AuthTokens> {
    const { email, password, firstName, lastName, organizationName, organizationSlug } = dto;

    // Check if user already exists
    const existingUser = await this.prismaService.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      throw new BadRequestException('User with this email already exists');
    }

    // Hash password
    const passwordHash = await bcrypt.hash(password, this.saltRounds);

    // Create organization and user in transaction
    const result = await this.prismaService.$transaction(async (prisma) => {
      // Create organization if provided
      let organizationId: string;
      
      if (organizationName && organizationSlug) {
        const organization = await prisma.organization.create({
          data: {
            name: organizationName,
            slug: organizationSlug,
            settings: {
              features: ['agents', 'tools', 'workflows'],
              limits: {
                agents: 5,
                tools: 10,
                workflows: 5,
                users: 3,
              },
            },
          },
        });
        organizationId = organization.id;
      } else {
        // Find or create default organization
        const defaultOrg = await prisma.organization.findFirst({
          where: { slug: 'default' },
        });
        
        if (defaultOrg) {
          organizationId = defaultOrg.id;
        } else {
          const newDefaultOrg = await prisma.organization.create({
            data: {
              name: 'Default Organization',
              slug: 'default',
            },
          });
          organizationId = newDefaultOrg.id;
        }
      }

      // Get default role
      const defaultRole = await prisma.role.findFirst({
        where: { level: 'DEVELOPER' },
      });

      if (!defaultRole) {
        throw new Error('Default role not found. Please run database seed.');
      }

      // Create user
      const user = await prisma.user.create({
        data: {
          email,
          passwordHash,
          firstName,
          lastName,
          organizationId,
          roleId: defaultRole.id,
          emailVerifyToken: uuidv4(),
        },
        include: {
          role: true,
          organization: true,
        },
      });

      return user;
    });

    // Generate tokens
    const tokens = await this.generateTokens(result);

    // Store refresh token in Redis
    await this.storeRefreshToken(result.id, tokens.refreshToken);

    this.logger.log(`User registered: ${email} in organization: ${result.organization.name}`);

    return tokens;
  }

  /**
   * Login user with organization context
   */
  async login(dto: LoginDto): Promise<AuthTokens> {
    const { email, password, organizationId } = dto;

    // Find user with organization
    const user = await this.prismaService.user.findFirst({
      where: {
        email,
        ...(organizationId && { organizationId }),
        status: UserStatus.ACTIVE,
      },
      include: {
        role: true,
        organization: true,
      },
    });

    if (!user) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.passwordHash || '');
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Check organization status
    if (user.organization.status !== 'ACTIVE') {
      throw new UnauthorizedException('Organization is not active');
    }

    // Generate tokens
    const tokens = await this.generateTokens(user);

    // Store refresh token in Redis
    await this.storeRefreshToken(user.id, tokens.refreshToken);

    // Update last seen
    await this.prismaService.user.update({
      where: { id: user.id },
      data: { lastSeen: new Date() },
    });

    this.logger.log(`User logged in: ${email} from organization: ${user.organization.name}`);

    return tokens;
  }

  /**
   * Refresh access token
   */
  async refreshTokens(refreshToken: string): Promise<AuthTokens> {
    try {
      // Verify refresh token
      const payload = this.jwtService.verify<JwtPayload>(refreshToken, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
      });

      // Check if refresh token exists in Redis
      const storedToken = await this.redisService.get(`refresh_token:${payload.sub}`);
      if (!storedToken || storedToken !== refreshToken) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      // Get user with fresh data
      const user = await this.prismaService.user.findUnique({
        where: { id: payload.sub },
        include: {
          role: true,
          organization: true,
        },
      });

      if (!user || user.status !== UserStatus.ACTIVE) {
        throw new UnauthorizedException('User not found or inactive');
      }

      // Generate new tokens
      const tokens = await this.generateTokens(user);

      // Store new refresh token
      await this.storeRefreshToken(user.id, tokens.refreshToken);

      return tokens;
    } catch (error) {
      this.logger.error('Refresh token error:', error);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  /**
   * Logout user
   */
  async logout(userId: string): Promise<void> {
    // Remove refresh token from Redis
    await this.redisService.del(`refresh_token:${userId}`);
    
    // Clear all user sessions
    const sessionKeys = await this.redisService.keys(`session:${userId}:*`);
    if (sessionKeys && sessionKeys.length > 0) {
      await Promise.all(sessionKeys.map((key: string) => this.redisService.del(key as string)));
    }

    this.logger.log(`User logged out: ${userId}`);
  }

  /**
   * Validate JWT token
   */
  async validateToken(token: string): Promise<JwtPayload> {
    try {
      const payload = this.jwtService.verify<JwtPayload>(token, {
        secret: this.configService.get<string>('JWT_SECRET'),
      });

      // Check if user is still active
      const user = await this.prismaService.user.findUnique({
        where: { id: payload.sub },
        select: { status: true },
      });

      if (!user || user.status !== UserStatus.ACTIVE) {
        throw new UnauthorizedException('User not found or inactive');
      }

      return payload;
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }

  /**
   * Change user password
   */
  async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<void> {
    const user = await this.prismaService.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      throw new BadRequestException('User not found');
    }

    // Verify current password
    const isPasswordValid = await bcrypt.compare(currentPassword, user.passwordHash || '');
    if (!isPasswordValid) {
      throw new BadRequestException('Current password is incorrect');
    }

    // Hash new password
    const newPasswordHash = await bcrypt.hash(newPassword, this.saltRounds);

    // Update password
    await this.prismaService.user.update({
      where: { id: userId },
      data: { 
        passwordHash: newPasswordHash,
        resetToken: null,
        resetTokenExpiry: null,
      },
    });

    // Invalidate all sessions
    await this.logout(userId);

    this.logger.log(`Password changed for user: ${userId}`);
  }

  /**
   * Generate JWT tokens
   */
  private async generateTokens(user: User & { role: Role; organization: any }): Promise<AuthTokens> {
    const permissions = user.role.permissions as string[];

    const payload: JwtPayload = {
      sub: user.id,
      email: user.email,
      organizationId: user.organizationId,
      role: user.role.name,
      permissions,
    };

    const [accessToken, refreshToken] = await Promise.all([
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('JWT_SECRET'),
        expiresIn: this.jwtAccessExpiration,
      }),
      this.jwtService.signAsync(payload, {
        secret: this.configService.get<string>('JWT_REFRESH_SECRET'),
        expiresIn: this.jwtRefreshExpiration,
      }),
    ]);

    return {
      accessToken,
      refreshToken,
      expiresIn: this.jwtAccessExpiration,
    };
  }

  /**
   * Store refresh token in Redis
   */
  private async storeRefreshToken(userId: string, refreshToken: string): Promise<void> {
    await this.redisService.set(
      `refresh_token:${userId}`,
      refreshToken,
      this.jwtRefreshExpiration,
    );
  }

  /**
   * Validate user by ID (for Passport strategies)
   */
  async validateUserById(userId: string): Promise<User | null> {
    return this.prismaService.user.findUnique({
      where: { id: userId },
      include: {
        role: true,
        organization: true,
      },
    });
  }

  /**
   * Create API key for organization
   */
  async createApiKey(organizationId: string, name: string): Promise<string> {
    const apiKey = `synapseai_${uuidv4().replace(/-/g, '')}`;
    const hashedKey = await bcrypt.hash(apiKey, this.saltRounds);

    // Store API key in database (you might want to create an ApiKey model)
    await this.redisService.set(
      `api_key:${hashedKey}`,
      JSON.stringify({ organizationId, name }),
      0, // No expiration
    );

    return apiKey;
  }

  /**
   * Validate API key
   */
  async validateApiKey(apiKey: string): Promise<{ organizationId: string; name: string } | null> {
    // Hash the API key to find it
    const keyData = await this.redisService.get(`api_key:${apiKey}`);
    
    if (!keyData) {
      return null;
    }

    return JSON.parse(keyData);
  }
} 