import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { AgentService } from './agent.service';
import { SessionService } from '../../session/services/session.service';
import { AIProviderService, ChatMessage } from './ai-provider.service';
import { ExecutionStatus, Prisma } from '.prisma/client';
import { v4 as uuidv4 } from 'uuid';

interface ExecutionResult {
  id: string;
  output: string;
  sessionId?: string;
  duration: number;
  tokenUsage: {
    prompt: number;
    completion: number;
    total: number;
  };
  status: ExecutionStatus;
  metadata: Record<string, any>;
}

interface SessionMessage {
  role: string;
  content: string;
  id?: string;
  timestamp?: Date;
  tokens?: number;
  metadata?: Record<string, any>;
}

/**
 * Agent Execution Service
 * 
 * Handles the execution of agents:
 * - Processes agent inputs
 * - Manages AI provider integration
 * - Tracks execution metrics
 * - Handles session context
 * - Implements retry and fallback logic
 */
@Injectable()
export class AgentExecutionService {
  private readonly logger = new Logger(AgentExecutionService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly configService: ConfigService,
    private readonly agentService: AgentService,
    private readonly sessionService: SessionService,
    private readonly aiProviderService: AIProviderService,
  ) {}

  /**
   * Execute an agent with the given input
   */
  async AgentExecuteResult(
    agentId: string,
    input: string,
    userId: string,
    organizationId: string,
    sessionId?: string,
  ): Promise<ExecutionResult> {
    try {
      // Get agent
      const agent = await this.agentService.getAgent(agentId, userId, organizationId);
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Validate agent is active
      if (agent.status !== 'ACTIVE') {
        throw new BadRequestException(`Agent is not active (status: ${agent.status})`);
      }

      // Generate execution ID
      const executionId = `exec_${uuidv4()}`;
      
      // Start tracking execution time
      const startTime = Date.now();

      // Create or get session
      let session;
      if (sessionId) {
        session = await this.sessionService.getSession(sessionId);
        if (!session || session.organizationId !== organizationId) {
          throw new BadRequestException('Invalid session ID');
        }
      } else {
        session = await this.sessionService.createSession(
          userId,
          organizationId,
          agentId,
          {
            agentName: agent.name,
            agentVersion: agent.version,
          }
        );
        sessionId = session.id;
      }

      // Add user message to session
      if (sessionId) {
        await this.sessionService.addMessage(sessionId, {
          role: 'user',
          content: input,
        });
      }

      // Create execution record
      await this.prismaService.agentExecution.create({
        data: {
          id: executionId,
          agentId,
          sessionId,
          input,
          status: 'PROCESSING' as ExecutionStatus,
          startedAt: new Date(),
          metadata: {
            agentName: agent.name,
            agentVersion: agent.version,
            userId,
            organizationId,
          } as unknown as Prisma.InputJsonValue,
        },
      });

      // Get agent configuration
      const config = agent.configuration;
      if (!config) {
        throw new BadRequestException('Agent configuration is missing');
      }

      // Get conversation history from session memory
      const messages: SessionMessage[] = session.memory?.messages || [];

      // Prepare messages for AI provider
      const aiMessages: ChatMessage[] = [
        { role: 'system', content: config.systemPrompt },
        ...messages.map((msg: SessionMessage) => ({
          role: msg.role as 'user' | 'assistant',
          content: msg.content,
        })),
        { role: 'user', content: input },
      ];

      // Execute the agent using the AI provider
      let result: {
        output: string;
        tokenUsage: {
          prompt: number;
          completion: number;
          total: number;
        };
        metadata: Record<string, any>;
      } | undefined;
      
      let executionError: Error | undefined;
      try {
        // Validate model configuration
        const configValidation = this.aiProviderService.validateModelConfig({
          model: config.model,
          temperature: config.temperature,
          maxTokens: config.maxTokens,
        });

        if (!configValidation.valid) {
          throw new BadRequestException(`Invalid agent configuration: ${configValidation.errors.join(', ')}`);
        }

        // Execute with real AI provider
        const aiResponse = await this.aiProviderService.executeCompletion(aiMessages, {
          model: config.model,
          temperature: config.temperature,
          maxTokens: config.maxTokens,
          topP: config.topP,
          frequencyPenalty: config.frequencyPenalty,
          presencePenalty: config.presencePenalty,
          stopSequences: config.stopSequences,
        });

        // Add assistant message to session
        if (sessionId) {
          await this.sessionService.addMessage(sessionId, {
            role: 'assistant',
            content: aiResponse.output,
          });
        }

        result = {
          output: aiResponse.output,
          tokenUsage: aiResponse.tokenUsage,
          metadata: {
            ...aiResponse.metadata,
            agentId,
            agentName: agent.name,
            agentVersion: agent.version,
          },
        };

        this.logger.log(`Agent execution completed successfully: ${executionId} with ${aiResponse.metadata.provider} provider`);
      } catch (err) {
        executionError = err instanceof Error ? err : new Error(String(err));
        this.logger.error(`Error executing agent: ${executionError.message}`, executionError.stack);
      }

      // Calculate execution time
      const endTime = Date.now();
      const duration = endTime - startTime;

      // Update execution record
      if (executionError) {
        await this.prismaService.agentExecution.update({
          where: { id: executionId },
          data: {
            status: 'FAILED' as ExecutionStatus,
            error: executionError.message,
            completedAt: new Date(),
            duration,
          },
        });

        throw new BadRequestException(`Failed to execute agent: ${executionError.message}`);
      } else if (result) {
        await this.prismaService.agentExecution.update({
          where: { id: executionId },
          data: {
            output: result.output,
            status: 'COMPLETED' as ExecutionStatus,
            completedAt: new Date(),
            duration,
            tokenUsage: result.tokenUsage as unknown as Prisma.InputJsonValue,
            cost: this.calculateCost(result.tokenUsage, config.model),
          },
        });
      }

      // Track execution in analytics
      // In a real implementation, this would use the analytics module
      this.logger.log(`Agent execution completed: ${executionId}`);

      // Return execution result
      if (!result) {
        throw new Error('Execution failed with no result');
      }
      
      return {
        id: executionId,
        output: result.output,
        sessionId,
        duration,
        tokenUsage: result.tokenUsage,
        status: 'COMPLETED' as ExecutionStatus,
        metadata: {
          ...result.metadata,
          agentId,
          agentName: agent.name,
        },
      };
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to execute agent: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * Calculate cost based on token usage and model
   */
  private calculateCost(tokenUsage: { prompt: number; completion: number; total: number }, model: string): number {
    // These are example rates, real implementation would use provider-specific pricing
    const rates: Record<string, { prompt: number; completion: number }> = {
      'gpt-4': { prompt: 0.03, completion: 0.06 },
      'gpt-3.5-turbo': { prompt: 0.0015, completion: 0.002 },
      'claude-2': { prompt: 0.01, completion: 0.03 },
      'default': { prompt: 0.001, completion: 0.002 },
    };

    // Always provide a default rate
    const defaultRate = { prompt: 0.001, completion: 0.002 };
    const rate = rates[model] || rates['default'] || defaultRate;
    
    // Calculate cost per 1000 tokens
    const promptCost = (tokenUsage.prompt / 1000) * rate.prompt;
    const completionCost = (tokenUsage.completion / 1000) * rate.completion;
    
    return promptCost + completionCost;
  }

  /**
   * Get execution by ID
   */
  async getExecution(
    executionId: string,
    userId: string,
    organizationId: string,
  ): Promise<any> {
    try {
      const execution = await this.prismaService.agentExecution.findFirst({
        where: {
          id: executionId,
          agent: {
            organizationId,
          },
        },
      });

      if (!execution) {
        throw new NotFoundException('Execution not found');
      }

      return execution;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to get execution: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * List executions for an agent
   */
  async listExecutions(
    agentId: string,
    userId: string,
    organizationId: string,
    options: {
      status?: ExecutionStatus;
      limit?: number;
      offset?: number;
      orderBy?: 'startedAt' | 'completedAt';
      order?: 'asc' | 'desc';
    } = {},
  ): Promise<{ executions: any[]; total: number }> {
    try {
      // Verify agent exists and user has access
      const agent = await this.agentService.getAgent(agentId, userId, organizationId);
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      const where: Prisma.AgentExecutionWhereInput = {
        agentId,
        ...(options.status && { status: options.status }),
      };

      // Get total count
      const total = await this.prismaService.agentExecution.count({ where });

      // Get executions
      const executions = await this.prismaService.agentExecution.findMany({
        where,
        take: options.limit || 20,
        skip: options.offset || 0,
        orderBy: {
          [options.orderBy || 'startedAt']: options.order || 'desc',
        },
      });

      return {
        executions,
        total,
      };
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to list executions: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * Test agent execution without creating a permanent execution record
   */
  async testAgent(
    agentId: string,
    testInput: string,
    userId: string,
    organizationId: string,
  ): Promise<{
    success: boolean;
    output?: string;
    duration?: number;
    error?: string;
    metadata?: Record<string, any>;
  }> {
    try {
      // Get agent
      const agent = await this.agentService.getAgent(agentId, userId, organizationId);
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Validate agent is active or draft
      if (!['ACTIVE', 'DRAFT'].includes(agent.status)) {
        throw new BadRequestException(`Agent cannot be tested (status: ${agent.status})`);
      }

      const startTime = Date.now();

      // Get agent configuration
      const config = agent.configuration;
      if (!config) {
        throw new BadRequestException('Agent configuration is missing');
      }

      // Prepare test messages
      const testMessages: ChatMessage[] = [
        { role: 'system', content: config.systemPrompt },
        { role: 'user', content: testInput },
      ];

      // Validate model configuration
      const configValidation = this.aiProviderService.validateModelConfig({
        model: config.model,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
      });

      if (!configValidation.valid) {
        throw new BadRequestException(`Invalid agent configuration: ${configValidation.errors.join(', ')}`);
      }

      // Execute test with AI provider
      const aiResponse = await this.aiProviderService.executeCompletion(testMessages, {
        model: config.model,
        temperature: config.temperature,
        maxTokens: config.maxTokens,
        topP: config.topP,
        frequencyPenalty: config.frequencyPenalty,
        presencePenalty: config.presencePenalty,
        stopSequences: config.stopSequences,
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      this.logger.log(`Agent test completed: ${agentId} in ${duration}ms`);

      return {
        success: true,
        output: aiResponse.output,
        duration,
        metadata: {
          ...aiResponse.metadata,
          agentId,
          agentName: agent.name,
          testMode: true,
        },
      };
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Agent test failed: ${err.message}`, err.stack);
      
      return {
        success: false,
        error: err.message,
      };
    }
  }

  /**
   * Get available AI models
   */
  getAvailableModels(): Record<string, string[]> {
    return this.aiProviderService.getAvailableModels();
  }

  /**
   * Test AI provider connectivity
   */
  async testProvider(model: string): Promise<{ success: boolean; error?: string; latency?: number }> {
    return this.aiProviderService.testProvider(model);
  }
} 