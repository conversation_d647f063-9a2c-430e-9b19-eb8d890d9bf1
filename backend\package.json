{"name": "@synapseai/backend", "version": "1.0.0", "description": "NestJS backend for SynapseAI platform with vector database integration", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "dev": "nest start --watch", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "type-check": "tsc --noEmit --skipL<PERSON><PERSON><PERSON><PERSON>", "db:migrate": "prisma migrate dev", "db:migrate:reset": "prisma migrate reset", "db:migrate:deploy": "prisma migrate deploy", "db:generate": "prisma generate", "db:studio": "prisma studio", "db:seed": "ts-node prisma/seed.ts", "db:reset": "prisma migrate reset --force"}, "dependencies": {"@aws-sdk/client-s3": "^3.441.0", "@aws-sdk/s3-request-presigner": "^3.441.0", "@azure/openai": "^1.0.0-beta.6", "@casl/ability": "^6.7.3", "@fastify/compress": "^6.4.0", "@fastify/cors": "^8.4.0", "@fastify/helmet": "^11.1.1", "@fastify/multipart": "^8.0.0", "@fastify/rate-limit": "^9.0.1", "@huggingface/inference": "^2.6.4", "@nestjs/axios": "^4.0.1", "@nestjs/bullmq": "^10.0.1", "@nestjs/common": "^10.2.7", "@nestjs/config": "^3.1.1", "@nestjs/core": "^10.2.7", "@nestjs/event-emitter": "^2.0.2", "@nestjs/jwt": "^10.1.1", "@nestjs/passport": "^10.0.2", "@nestjs/platform-express": "^11.1.5", "@nestjs/platform-fastify": "^10.2.7", "@nestjs/platform-socket.io": "^10.2.7", "@nestjs/platform-ws": "^11.1.5", "@nestjs/schedule": "^4.0.0", "@nestjs/serve-static": "^4.0.0", "@nestjs/swagger": "^7.1.13", "@nestjs/terminus": "^11.0.0", "@nestjs/throttler": "^5.0.0", "@nestjs/websockets": "^10.2.7", "@pinecone-database/pinecone": "^1.1.2", "@prisma/client": "^5.22.0", "@sendgrid/mail": "^7.7.0", "@sentry/node": "^7.77.0", "@sentry/profiling-node": "^1.2.6", "@synapseai/shared": "workspace:*", "@types/compression": "^1.8.1", "archiver": "^6.0.1", "axios": "^1.10.0", "bcrypt": "^6.0.0", "bullmq": "^4.12.7", "cache-manager": "^5.2.4", "cache-manager-redis-store": "^3.0.1", "cheerio": "^1.0.0-rc.12", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "cohere-ai": "^7.3.2", "compression": "^1.7.4", "crypto-js": "^4.2.0", "date-fns": "^2.30.0", "express": "^5.1.0", "express-prometheus-middleware": "^1.2.0", "express-rate-limit": "^8.0.1", "helmet": "^7.0.0", "ioredis": "^5.3.2", "jsonwebtoken": "^9.0.2", "langchain": "^0.0.175", "lodash": "^4.17.21", "mammoth": "^1.6.0", "mime-types": "^2.1.35", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.2", "node-html-parser": "^6.1.10", "openai": "^4.14.2", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pdf-parse": "^1.1.1", "prisma": "^5.5.2", "prom-client": "^13.2.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1", "sanitize-html": "^2.11.0", "sharp": "^0.32.6", "socket.io": "^4.7.2", "stripe": "^13.8.0", "tiktoken": "^1.0.10", "twilio": "^4.19.0", "unzipper": "^0.10.14", "uuid": "^9.0.1", "validator": "^13.11.0", "weaviate-ts-client": "^1.4.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.2.7", "@types/bcrypt": "^5.0.1", "@types/crypto-js": "^4.2.1", "@types/express": "^4.17.23", "@types/jest": "^29.5.6", "@types/jsonwebtoken": "^9.0.4", "@types/lodash": "^4.14.200", "@types/mime-types": "^2.1.3", "@types/multer": "^1.4.9", "@types/node": "^20.8.0", "@types/node-cron": "^3.0.9", "@types/passport-jwt": "^3.0.11", "@types/passport-local": "^1.0.37", "@types/sanitize-html": "^2.9.4", "@types/supertest": "^2.0.15", "@types/uuid": "^9.0.6", "@types/validator": "^13.11.5", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "prettier": "^3.0.3", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.0", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.2.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "moduleNameMapping": {"^@/(.*)$": "<rootDir>/$1", "^@shared/(.*)$": "<rootDir>/../shared/src/$1"}}}