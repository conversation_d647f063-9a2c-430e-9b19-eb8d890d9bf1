import { Injectable, Logger, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { ToolService, ToolConfiguration } from './tool.service';
import { ExecutionStatus, Prisma } from '.prisma/client';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { v4 as uuidv4 } from 'uuid';

export interface ToolExecutionRequest {
  toolId: string;
  input: Record<string, any>;
  sessionId?: string;
  userId: string;
  organizationId: string;
  metadata?: Record<string, any>;
}

export interface ToolExecutionResult {
  id: string;
  output?: Record<string, any>;
  error?: string;
  status: ExecutionStatus;
  duration?: number;
  cost?: number;
  metadata?: Record<string, any>;
}

export interface HttpExecutionContext {
  method: string;
  url: string;
  headers: Record<string, string>;
  body?: any;
  timeout: number;
  retryConfig?: {
    maxRetries: number;
    backoffStrategy: 'linear' | 'exponential';
    retryDelay: number;
  };
}

/**
 * Tool Execution Service
 * 
 * Handles tool execution with real integrations:
 * - HTTP API calls
 * - Database queries
 * - File operations
 * - Webhook handling
 * - Custom tool execution
 * - Error handling and retries
 * - Performance monitoring
 * - Security validation
 */
@Injectable()
export class ToolExecutionService {
  private readonly logger = new Logger(ToolExecutionService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly toolService: ToolService,
  ) {}

  /**
   * Execute a tool
   */
  async executeTool(id: string, parameters: Record<string, any>, metadata: Record<string, any> | undefined, sessionId: string | undefined, sub: string, organizationId: string, request: ToolExecutionRequest): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    let executionId: string;

    try {
      // Get tool configuration
      const tool = await this.toolService.getTool(
        request.toolId,
        request.userId,
        request.organizationId,
      );

      if (!tool) {
        throw new BadRequestException('Tool not found');
      }

      if (tool.status !== 'ACTIVE') {
        throw new BadRequestException('Tool is not active');
      }

      // Create execution record
      executionId = `exec_${uuidv4()}`;
      await this.createExecutionRecord({
        id: executionId,
        toolId: request.toolId,
        sessionId: request.sessionId,
        input: request.input,
        status: 'PENDING',
        metadata: request.metadata,
      });

      // Update execution status to running
      await this.updateExecutionStatus(executionId, 'RUNNING');

      // Execute based on tool type
      let result: any;
      switch (tool.type) {
        case 'API':
          result = await this.executeApiTool(tool, request.input);
          break;
        case 'DATABASE':
          result = await this.executeDatabaseTool(tool, request.input);
          break;
        case 'FILE':
          result = await this.executeFileTool(tool, request.input);
          break;
        case 'WEBHOOK':
          result = await this.executeWebhookTool(tool, request.input);
          break;
        case 'CUSTOM':
          result = await this.executeCustomTool(tool, request.input);
          break;
        default:
          throw new BadRequestException(`Unsupported tool type: ${tool.type}`);
      }

      const duration = Date.now() - startTime;

      // Update execution record with success
      await this.updateExecutionRecord({
        id: executionId,
        output: result,
        status: 'COMPLETED',
        duration,
        cost: this.calculateExecutionCost(tool, duration),
      });

      this.logger.log(`Tool execution completed: ${executionId} in ${duration}ms`);

      return {
        id: executionId,
        output: result,
        status: 'COMPLETED',
        duration,
        cost: this.calculateExecutionCost(tool, duration),
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      this.logger.error(`Tool execution failed: ${request.toolId}`, error);

      // Update execution record with error if we have an ID
      if (executionId!) {
        await this.updateExecutionRecord({
          id: executionId,
          error: errorMessage,
          status: 'FAILED',
          duration,
        });
      }

      return {
        id: executionId! || `failed_${uuidv4()}`,
        error: errorMessage,
        status: 'FAILED',
        duration,
      };
    }
  }

  /**
   * Test tool execution without persistence
   */
  async testTool(
    toolId: string,
    input: Record<string, any>,
    userId: string,
    organizationId: string,
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();

    try {
      // Get tool configuration
      const tool = await this.toolService.getTool(toolId, userId, organizationId);
      if (!tool) {
        throw new BadRequestException('Tool not found');
      }

      // Execute without creating persistent records
      let result: any;
      switch (tool.type) {
        case 'API':
          result = await this.executeApiTool(tool, input);
          break;
        case 'DATABASE':
          result = await this.executeDatabaseTool(tool, input);
          break;
        case 'FILE':
          result = await this.executeFileTool(tool, input);
          break;
        case 'WEBHOOK':
          result = await this.executeWebhookTool(tool, input);
          break;
        case 'CUSTOM':
          result = await this.executeCustomTool(tool, input);
          break;
        default:
          throw new BadRequestException(`Unsupported tool type: ${tool.type}`);
      }

      const duration = Date.now() - startTime;

      return {
        id: `test_${uuidv4()}`,
        output: result,
        status: 'COMPLETED',
        duration,
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      return {
        id: `test_failed_${uuidv4()}`,
        error: errorMessage,
        status: 'FAILED',
        duration,
      };
    }
  }

  /**
   * Execute API tool
   */
  private async executeApiTool(tool: any, input: Record<string, any>): Promise<any> {
    const config = tool.configuration as ToolConfiguration;
    
    if (!config.endpoint) {
      throw new BadRequestException('API endpoint is required');
    }

    // Build request configuration
    const requestConfig: AxiosRequestConfig = {
      method: config.method.toLowerCase(),
      url: this.interpolateUrl(config.endpoint, input),
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'SynapseAI-Tool-Executor/1.0',
        ...config.headers,
      },
    };

    // Add authentication
    if (config.authentication) {
      this.addAuthentication(requestConfig, config.authentication);
    }

    // Add request body for POST/PUT/PATCH
    if (['post', 'put', 'patch'].includes(config.method.toLowerCase())) {
      requestConfig.data = this.buildRequestBody(config, input);
    } else if (config.method.toLowerCase() === 'get') {
      requestConfig.params = input;
    }

    // Execute with retry logic
    return this.executeHttpRequest(requestConfig, config.retry);
  }

  /**
   * Execute database tool
   */
  private async executeDatabaseTool(tool: any, input: Record<string, any>): Promise<any> {
    // This is a placeholder for database tool execution
    // In a real implementation, you would:
    // 1. Connect to the specified database
    // 2. Execute the query with proper sanitization
    // 3. Return the results
    
    throw new BadRequestException('Database tools are not yet implemented');
  }

  /**
   * Execute file tool
   */
  private async executeFileTool(tool: any, input: Record<string, any>): Promise<any> {
    // This is a placeholder for file tool execution
    // In a real implementation, you would:
    // 1. Handle file uploads/downloads
    // 2. Process file content
    // 3. Apply transformations
    // 4. Return processed results
    
    throw new BadRequestException('File tools are not yet implemented');
  }

  /**
   * Execute webhook tool
   */
  private async executeWebhookTool(tool: any, input: Record<string, any>): Promise<any> {
    // Similar to API tool but with webhook-specific handling
    return this.executeApiTool(tool, input);
  }

  /**
   * Execute custom tool
   */
  private async executeCustomTool(tool: any, input: Record<string, any>): Promise<any> {
    // This is a placeholder for custom tool execution
    // In a real implementation, you would:
    // 1. Execute custom code/scripts
    // 2. Handle sandboxed execution
    // 3. Apply security constraints
    // 4. Return results
    
    throw new BadRequestException('Custom tools are not yet implemented');
  }

  /**
   * Execute HTTP request with retry logic
   */
  private async executeHttpRequest(
    config: AxiosRequestConfig,
    retryConfig?: any,
  ): Promise<any> {
    const maxRetries = retryConfig?.maxRetries || 0;
    let error: Error;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const response: AxiosResponse = await axios(config);
        
        // Validate response
        if (response.status >= 200 && response.status < 300) {
          return {
            status: response.status,
            headers: response.headers,
            data: response.data,
          };
        } else {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

      } catch (error) {
        error = error as Error;
        
        // Don't retry on last attempt
        if (attempt === maxRetries) {
          break;
        }

        // Check if error is retryable
        if (!this.isRetryableError(error)) {
          break;
        }

        // Calculate delay
        const delay = this.calculateRetryDelay(
          attempt,
          retryConfig?.retryDelay || 1000,
          retryConfig?.backoffStrategy || 'exponential',
        );  

        await this.sleep(delay);
        this.logger.warn(`Retrying HTTP request (attempt ${attempt + 2}/${maxRetries + 1}) after ${delay}ms`);
      }
    }

    throw new InternalServerErrorException(`HTTP request failed after ${maxRetries + 1} attempts: Unknown error`);
  }

  /**
   * Interpolate URL with input parameters
   */
  private interpolateUrl(url: string, input: Record<string, any>): string {
    let interpolatedUrl = url;
    
    // Replace path parameters like {id} with input values
    Object.keys(input).forEach(key => {
      const placeholder = `{${key}}`;
      if (interpolatedUrl.includes(placeholder)) {
        interpolatedUrl = interpolatedUrl.replace(placeholder, encodeURIComponent(String(input[key])));
        delete input[key]; // Remove from input to avoid duplication
      }
    });

    return interpolatedUrl;
  }

  /**
   * Add authentication to request
   */
  private addAuthentication(config: AxiosRequestConfig, auth: any): void {
    switch (auth.type) {
      case 'API_KEY':
        if (auth.credentials?.headerName && auth.credentials?.apiKey) {
          config.headers![auth.credentials.headerName] = auth.credentials.apiKey;
        }
        break;
      case 'BEARER_TOKEN':
        if (auth.token) {
          config.headers!['Authorization'] = `Bearer ${auth.token}`;
        }
        break;
      case 'BASIC_AUTH':
        if (auth.username && auth.password) {
          config.auth = {
            username: auth.username,
            password: auth.password,
          };
        }
        break;
      case 'OAUTH2':
        // OAuth2 would require token exchange, for now just pass token if available
        if (auth.token) {
          config.headers!['Authorization'] = `Bearer ${auth.token}`;
        }
        break;
    }
  }

  /**
   * Build request body
   */
  private buildRequestBody(config: ToolConfiguration, input: Record<string, any>): any {
    // Apply parameter validation and transformation
    if (config.parameters) {
      const validatedInput: Record<string, any> = {};
      
      config.parameters.forEach(param => {
        const value = input[param.name];
        
        if (param.required && (value === undefined || value === null)) {
          throw new BadRequestException(`Required parameter '${param.name}' is missing`);
        }
        
        if (value !== undefined) {
          validatedInput[param.name] = this.validateAndTransformParameter(param, value);
        } else if (param.defaultValue !== undefined) {
          validatedInput[param.name] = param.defaultValue;
        }
      });
      
      return validatedInput;
    }
    
    return input;
  }

  /**
   * Validate and transform parameter
   */
  private validateAndTransformParameter(param: any, value: any): any {
    // Type validation
    switch (param.type) {
      case 'string':
        if (typeof value !== 'string') {
          throw new BadRequestException(`Parameter '${param.name}' must be a string`);
        }
        break;
      case 'number':
        if (typeof value !== 'number') {
          throw new BadRequestException(`Parameter '${param.name}' must be a number`);
        }
        break;
      case 'boolean':
        if (typeof value !== 'boolean') {
          throw new BadRequestException(`Parameter '${param.name}' must be a boolean`);
        }
        break;
    }

    // Validation rules
    if (param.validation) {
      if (param.validation.min !== undefined && value < param.validation.min) {
        throw new BadRequestException(`Parameter '${param.name}' must be >= ${param.validation.min}`);
      }
      if (param.validation.max !== undefined && value > param.validation.max) {
        throw new BadRequestException(`Parameter '${param.name}' must be <= ${param.validation.max}`);
      }
      if (param.validation.pattern && !new RegExp(param.validation.pattern).test(value)) {
        throw new BadRequestException(`Parameter '${param.name}' does not match required pattern`);
      }
      if (param.validation.enum && !param.validation.enum.includes(value)) {
        throw new BadRequestException(`Parameter '${param.name}' must be one of: ${param.validation.enum.join(', ')}`);
      }
    }

    return value;
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: any): boolean {
    // Network errors
    if (error.code === 'ECONNRESET' || error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
      return true;
    }

    // HTTP status codes that are retryable
    if (error.response?.status >= 500 || error.response?.status === 429) {
      return true;
    }

    return false;
  }

  /**
   * Calculate retry delay
   */
  private calculateRetryDelay(
    attempt: number,
    baseDelay: number,
    strategy: 'linear' | 'exponential',
  ): number {
    switch (strategy) {
      case 'linear':
        return baseDelay * (attempt + 1);
      case 'exponential':
        return baseDelay * Math.pow(2, attempt);
      default:
        return baseDelay;
    }
  }

  /**
   * Sleep utility
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Calculate execution cost
   */
  private calculateExecutionCost(tool: any, duration: number): number {
    // Simple cost calculation based on execution time
    // In a real implementation, this would consider:
    // - API call costs
    // - Compute time
    // - Data transfer
    // - Storage usage
    const baseCost = 0.001; // $0.001 per execution
    const timeCost = (duration / 1000) * 0.0001; // $0.0001 per second
    return baseCost + timeCost;
  }

  /**
   * Create execution record
   */
  private async createExecutionRecord(data: {
    id: string;
    toolId: string;
    sessionId?: string;
    input: Record<string, any>;
    status: ExecutionStatus;
    metadata?: Record<string, any>;
  }): Promise<void> {
    await this.prismaService.toolExecution.create({
      data: {
        id: data.id,
        toolId: data.toolId,
        sessionId: data.sessionId,
        input: data.input as unknown as Prisma.InputJsonValue,
        status: data.status,
        metadata: (data.metadata || {}) as unknown as Prisma.InputJsonValue,
        startedAt: new Date(),
      },
    });
  }

  /**
   * Update execution record
   */
  private async updateExecutionRecord(data: {
    id: string;
    output?: Record<string, any>;
    error?: string;
    status?: ExecutionStatus;
    duration?: number;
    cost?: number;
  }): Promise<void> {
    const updateData: any = {
      ...(data.output && { output: data.output as unknown as Prisma.InputJsonValue }),
      ...(data.error && { error: data.error }),
      ...(data.status && { status: data.status }),
      ...(data.duration && { duration: data.duration }),
      ...(data.cost && { cost: data.cost }),
    };

    if (data.status === 'COMPLETED' || data.status === 'FAILED') {
      updateData.completedAt = new Date();
    }

    await this.prismaService.toolExecution.update({
      where: { id: data.id },
      data: updateData,
    });
  }

  /**
   * Update execution status
   */
  private async updateExecutionStatus(executionId: string, status: ExecutionStatus): Promise<void> {
    await this.prismaService.toolExecution.update({
      where: { id: executionId },
      data: { status },
    });
  }
}