import { Injectable, Logger, BadRequestException, NotFoundException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { RedisService } from '../../redis/redis.service';
import { SessionService } from '../../session/services/session.service';
import { Tool, ToolStatus, ToolType, AuthType, HttpMethod, Prisma } from '.prisma/client';
import { v4 as uuidv4 } from 'uuid';

export interface CreateToolDto {
  name: string;
  description?: string;
  type: ToolType;
  configuration: ToolConfiguration;
  metadata?: Record<string, any>;
}

export interface UpdateToolDto {
  name?: string;
  description?: string;
  type?: ToolType;
  configuration?: ToolConfiguration;
  metadata?: Record<string, any>;
  status?: ToolStatus;
}

export interface ToolConfiguration {
  endpoint?: string;
  method: HttpMethod;
  headers?: Record<string, string>;
  parameters?: ToolParameter[];
  authentication?: ToolAuthentication;
  validation?: ToolValidation;
  retry?: ToolRetryConfig;
  timeout?: number;
  rateLimit?: ToolRateLimit;
  caching?: ToolCachingConfig;
  schema?: Record<string, any>;
}

export interface ToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description?: string;
  defaultValue?: any;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    enum?: any[];
  };
}

export interface ToolAuthentication {
  type: AuthType;
  credentials?: Record<string, string>;
  token?: string;
  username?: string;
  password?: string;
  apiKey?: string;
  oauth?: {
    clientId: string;
    clientSecret: string;
    scope?: string[];
    tokenUrl?: string;
  };
}

export interface ToolValidation {
  requestValidation?: Record<string, any>;
  responseValidation?: Record<string, any>;
  allowedStatusCodes?: number[];
}

export interface ToolRetryConfig {
  maxRetries: number;
  backoffStrategy: 'linear' | 'exponential';
  retryDelay: number;
  retryConditions?: ('timeout' | 'server_error' | 'rate_limit')[];
}

export interface ToolRateLimit {
  requestsPerMinute: number;
  requestsPerHour: number;
  requestsPerDay: number;
}

export interface ToolCachingConfig {
  enabled: boolean;
  ttl: number;
  strategy: 'none' | 'request_based' | 'time_based' | 'content_based';
  keyPattern?: string;
}

export interface ToolWithStats extends Tool {
  executionCount?: number;
  successRate?: number;
  avgResponseTime?: number;
  lastExecutedAt?: Date;
  configuration?: ToolConfiguration;
  metadata?: Record<string, any>;
}

/**
 * Tool Service
 * 
 * Manages tool lifecycle:
 * - Tool creation and configuration
 * - Tool management and updates
 * - Tool discovery and search
 * - Integration with authentication
 * - Multi-tenant isolation
 * - Performance monitoring
 */
@Injectable()
export class ToolService {
  private readonly logger = new Logger(ToolService.name);
  private readonly CACHE_PREFIX = 'tool:';
  private readonly CACHE_TTL = 3600; // 1 hour

  constructor(
    private readonly prismaService: PrismaService,
    private readonly redisService: RedisService,
    private readonly sessionService: SessionService,
  ) {}

  /**
   * Create a new tool
   */
  async createTool(
    userId: string,
    organizationId: string,
    data: CreateToolDto,
  ): Promise<Tool> {
    try {
      // Validate configuration
      this.validateToolConfiguration(data.configuration);

      // Generate tool ID
      const toolId = `tool_${uuidv4()}`;

      // Create tool
      const tool = await this.prismaService.tool.create({
        data: {
          id: toolId,
          name: data.name,
          description: data.description,
          type: data.type,
          organizationId,
          createdById: userId,
          // Store configuration as JSON
          config: data.configuration as unknown as Prisma.InputJsonValue,
          endpoint: data.configuration.endpoint,
          method: data.configuration.method,
          headers: (data.configuration.headers || {}) as unknown as Prisma.InputJsonValue,
          authType: data.configuration.authentication?.type || 'NONE',
          credentials: (data.configuration.authentication?.credentials || {}) as unknown as Prisma.InputJsonValue,
          schema: (data.configuration.schema || {}) as unknown as Prisma.InputJsonValue,
          status: 'DRAFT',
          version: '1.0.0',
        },
      });

      // Cache tool
      await this.cacheTool({
        ...tool,
        configuration: data.configuration,
        metadata: data.metadata || {},
      });

      // Log creation
      this.logger.log(`Tool created: ${toolId} by user: ${userId}`);

      // Emit event
      await this.emitToolEvent('tool.created', tool);

      return tool;
    } catch (error) {
      this.logger.error('Failed to create tool', error);
      throw error;
    }
  }

  /**
   * Get tool by ID
   */
  async getTool(
    toolId: string,
    userId: string,
    organizationId: string,
  ): Promise<ToolWithStats | null> {
    try {
      // Check cache first
      const cached = await this.getCachedTool(toolId);
      if (cached) {
        // Verify access
        if (cached.organizationId !== organizationId) {
          throw new ForbiddenException('Access denied');
        }
        return cached;
      }

      // Get from database
      const tool = await this.prismaService.tool.findFirst({
        where: {
          id: toolId,
          organizationId,
          deletedAt: null,
        },
      });

      if (!tool) {
        return null;
      }

      // Get stats
      const stats = await this.getToolStats(toolId);

      const toolWithStats: ToolWithStats = {
        ...tool,
        ...stats,
        configuration: {
          endpoint: tool.endpoint || undefined,
          method: tool.method,
          headers: tool.headers as unknown as Record<string, string>,
          authentication: {
            type: tool.authType,
            credentials: tool.credentials as unknown as Record<string, string>,
          },
          schema: tool.schema as unknown as Record<string, any>,
          ...(tool.config as unknown as Record<string, any>),
        },
        metadata: tool.analytics as unknown as Record<string, any>,
      };

      // Cache tool
      await this.cacheTool(toolWithStats);

      return toolWithStats;
    } catch (error) {
      this.logger.error(`Failed to get tool: ${toolId}`, error);
      throw error;
    }
  }

  /**
   * Update tool
   */
  async updateTool(
    toolId: string,
    userId: string,
    organizationId: string,
    data: UpdateToolDto,
  ): Promise<Tool> {
    try {
      // Get existing tool
      const existing = await this.getTool(toolId, userId, organizationId);
      if (!existing) {
        throw new NotFoundException('Tool not found');
      }

      // Validate configuration if provided
      if (data.configuration) {
        this.validateToolConfiguration(data.configuration);
      }

      // Update tool
      const updated = await this.prismaService.tool.update({
        where: { id: toolId },
        data: {
          ...(data.name !== undefined ? { name: data.name } : {}),
          ...(data.description !== undefined ? { description: data.description } : {}),
          ...(data.type !== undefined ? { type: data.type } : {}),
          ...(data.status !== undefined ? { status: data.status } : {}),
          // Update configuration fields
          ...(data.configuration ? {
            config: data.configuration as unknown as Prisma.InputJsonValue,
            endpoint: data.configuration.endpoint,
            method: data.configuration.method,
            headers: (data.configuration.headers || {}) as unknown as Prisma.InputJsonValue,
            authType: data.configuration.authentication?.type || 'NONE',
            credentials: (data.configuration.authentication?.credentials || {}) as unknown as Prisma.InputJsonValue,
            schema: (data.configuration.schema || {}) as unknown as Prisma.InputJsonValue,
          } : {}),
          // Update metadata
          ...(data.metadata ? {
            analytics: data.metadata as unknown as Prisma.InputJsonValue,
          } : {}),
          updatedAt: new Date(),
        },
      });

      // Invalidate cache
      await this.invalidateToolCache(toolId);

      // Emit event
      await this.emitToolEvent('tool.updated', updated);

      this.logger.log(`Tool updated: ${toolId}`);

      return updated;
    } catch (error) {
      this.logger.error(`Failed to update tool: ${toolId}`, error);
      throw error;
    }
  }

  /**
   * Delete tool (soft delete)
   */
  async deleteTool(
    toolId: string,
    userId: string,
    organizationId: string,
  ): Promise<void> {
    try {
      // Verify access
      const tool = await this.getTool(toolId, userId, organizationId);
      if (!tool) {
        throw new NotFoundException('Tool not found');
      }

      // Soft delete
      await this.prismaService.tool.update({
        where: { id: toolId },
        data: {
          deletedAt: new Date(),
          status: 'DEPRECATED',
        },
      });

      // Invalidate cache
      await this.invalidateToolCache(toolId);

      // Emit event
      await this.emitToolEvent('tool.deleted', { toolId });

      this.logger.log(`Tool deleted: ${toolId}`);
    } catch (error) {
      this.logger.error(`Failed to delete tool: ${toolId}`, error);
      throw error;
    }
  }

  /**
   * List tools for organization
   */
  async listTools(
    organizationId: string,
    options: {
      status?: ToolStatus;
      type?: ToolType;
      search?: string;
      limit?: number;
      offset?: number;
      orderBy?: 'createdAt' | 'updatedAt' | 'name';
      order?: 'asc' | 'desc';
    } = {},
  ): Promise<{ tools: ToolWithStats[]; total: number }> {
    try {
      const where: Prisma.ToolWhereInput = {
        organizationId,
        deletedAt: null,
        ...(options.status && { status: options.status }),
        ...(options.type && { type: options.type }),
        ...(options.search && {
          OR: [
            { name: { contains: options.search, mode: 'insensitive' } },
            { description: { contains: options.search, mode: 'insensitive' } },
          ],
        }),
      };

      // Get total count
      const total = await this.prismaService.tool.count({ where });

      // Get tools
      const tools = await this.prismaService.tool.findMany({
        where,
        take: options.limit || 20,
        skip: options.offset || 0,
        orderBy: {
          [options.orderBy || 'createdAt']: options.order || 'desc',
        },
      });

      // Get stats for each tool
      const toolsWithStats = await Promise.all(
        tools.map(async (tool) => {
          const stats = await this.getToolStats(tool.id);
          return { 
            ...tool, 
            ...stats,
            configuration: {
              endpoint: tool.endpoint || undefined,
              method: tool.method,
              headers: tool.headers as unknown as Record<string, string>,
              authentication: {
                type: tool.authType,
                credentials: tool.credentials as unknown as Record<string, string>,
              },
              schema: tool.schema as unknown as Record<string, any>,
              ...(tool.config as unknown as Record<string, any>),
            },
            metadata: tool.analytics as unknown as Record<string, any>,
          };
        }),
      );

      return {
        tools: toolsWithStats,
        total,
      };
    } catch (error) {
      this.logger.error('Failed to list tools', error);
      throw error;
    }
  }

  /**
   * Activate tool
   */
  async activateTool(
    toolId: string,
    userId: string,
    organizationId: string,
  ): Promise<Tool> {
    return this.updateTool(toolId, userId, organizationId, {
      status: 'ACTIVE',
    });
  }

  /**
   * Deactivate tool
   */
  async deactivateTool(
    toolId: string,
    userId: string,
    organizationId: string,
  ): Promise<Tool> {
    return this.updateTool(toolId, userId, organizationId, {
      status: 'INACTIVE',
    });
  }

  /**
   * Clone tool
   */
  async cloneTool(
    toolId: string,
    userId: string,
    organizationId: string,
    newName: string,
  ): Promise<Tool> {
    try {
      // Get original tool
      const original = await this.getTool(toolId, userId, organizationId);
      if (!original) {
        throw new NotFoundException('Tool not found');
      }

      // Create clone
      return this.createTool(userId, organizationId, {
        name: newName,
        description: `Clone of ${original.name}`,
        type: original.type,
        configuration: original.configuration as ToolConfiguration,
        metadata: {
          ...(original.metadata as Record<string, any> || {}),
          clonedFrom: toolId,
          clonedAt: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Failed to clone tool: ${toolId}`, error);
      throw error;
    }
  }

  /**
   * Get tool statistics
   */
  private async getToolStats(toolId: string): Promise<{
    executionCount: number;
    successRate: number;
    avgResponseTime: number;
    lastExecutedAt?: Date;
  }> {
    try {
      // Use raw SQL queries for performance
      const executionStats = await this.prismaService.$queryRaw<{ count: number, avg_duration: number }[]>`
        SELECT 
          COUNT(*) as count,
          AVG(duration) as avg_duration
        FROM "tool_executions"
        WHERE "toolId" = ${toolId}
      `;
      
      const successCount = await this.prismaService.$queryRaw<{ count: number }[]>`
        SELECT COUNT(*) as count
        FROM "tool_executions"
        WHERE "toolId" = ${toolId} AND "status" = 'COMPLETED'
      `;
      
      const lastExecution = await this.prismaService.$queryRaw<{ started_at: Date }[]>`
        SELECT "startedAt" as started_at
        FROM "tool_executions"
        WHERE "toolId" = ${toolId}
        ORDER BY "startedAt" DESC
        LIMIT 1
      `;

      const count = executionStats[0]?.count || 0;
      
      return {
        executionCount: count,
        successRate: count > 0 ? ((successCount[0]?.count || 0) / count) * 100 : 0,
        avgResponseTime: executionStats[0]?.avg_duration || 0,
        lastExecutedAt: lastExecution[0]?.started_at,
      };
    } catch (error) {
      this.logger.error(`Failed to get tool stats: ${toolId}`, error);
      return {
        executionCount: 0,
        successRate: 0,
        avgResponseTime: 0,
      };
    }
  }

  /**
   * Validate tool configuration
   */
  private validateToolConfiguration(config: ToolConfiguration): void {
    if (!config.method) {
      throw new BadRequestException('HTTP method is required');
    }

    if (config.endpoint && !this.isValidUrl(config.endpoint)) {
      throw new BadRequestException('Invalid endpoint URL');
    }

    if (config.timeout && (config.timeout < 1000 || config.timeout > 300000)) {
      throw new BadRequestException('Timeout must be between 1000ms and 300000ms');
    }

    if (config.retry) {
      if (config.retry.maxRetries < 0 || config.retry.maxRetries > 10) {
        throw new BadRequestException('Max retries must be between 0 and 10');
      }
      if (config.retry.retryDelay < 100 || config.retry.retryDelay > 60000) {
        throw new BadRequestException('Retry delay must be between 100ms and 60000ms');
      }
    }

    if (config.rateLimit) {
      if (config.rateLimit.requestsPerMinute < 1 || config.rateLimit.requestsPerMinute > 10000) {
        throw new BadRequestException('Requests per minute must be between 1 and 10000');
      }
    }
  }

  /**
   * Validate URL format
   */
  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Cache tool
   */
  private async cacheTool(tool: ToolWithStats): Promise<void> {
    try {
      await this.redisService.set(
        `${this.CACHE_PREFIX}${tool.id}`,
        JSON.stringify(tool),
        this.CACHE_TTL,
      );
    } catch (error) {
      this.logger.error('Failed to cache tool', error);
    }
  }

  /**
   * Get cached tool
   */
  private async getCachedTool(toolId: string): Promise<ToolWithStats | null> {
    try {
      const cached = await this.redisService.get(`${this.CACHE_PREFIX}${toolId}`);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      this.logger.error('Failed to get cached tool', error);
      return null;
    }
  }

  /**
   * Invalidate tool cache
   */
  private async invalidateToolCache(toolId: string): Promise<void> {
    try {
      await this.redisService.del(`${this.CACHE_PREFIX}${toolId}`);
    } catch (error) {
      this.logger.error('Failed to invalidate tool cache', error);
    }
  }

  /**
   * Emit tool event
   */
  private async emitToolEvent(event: string, data: any): Promise<void> {
    try {
      const pubClient = await this.redisService.getClient();
      await pubClient.publish('apix:events', JSON.stringify({
        event,
        data,
        timestamp: new Date().toISOString(),
      }));
    } catch (error) {
      this.logger.error(`Failed to emit tool event: ${event}`, error);
    }
  }
}