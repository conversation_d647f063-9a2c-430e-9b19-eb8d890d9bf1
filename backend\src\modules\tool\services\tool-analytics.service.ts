import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { RedisService } from '../../redis/redis.service';

export interface ToolAnalytics {
  totalExecutions: number;
  successfulExecutions: number;
  failedExecutions: number;
  successRate: number;
  avgResponseTime: number;
  totalCost: number;
  usageByDay: { date: string; count: number; successRate: number }[];
  errorBreakdown: { error: string; count: number; percentage: number }[];
  performanceMetrics: {
    p50ResponseTime: number;
    p90ResponseTime: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
  };
  costAnalysis: {
    totalCost: number;
    avgCostPerExecution: number;
    costByDay: { date: string; cost: number }[];
  };
}

export interface ToolUsageStats {
  executions: number;
  successRate: number;
  avgResponseTime: number;
  lastUsed: Date | null;
  errorCount: number;
  totalCost: number;
}

@Injectable()
export class ToolAnalyticsService {
  private readonly logger = new Logger(ToolAnalyticsService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly redisService: RedisService,
  ) {}

  /**
   * Get comprehensive analytics for a tool
   */
  async getToolAnalytics(
toolId: string, organizationId: string, startDateObj: Date, endDateObj: Date, timeframe: 'hour' | 'day' | 'week' | 'month' = 'week',
  ): Promise<ToolAnalytics> {
    try {
      const timeframeHours = this.getTimeframeHours(timeframe);
      const startDate = new Date(Date.now() - timeframeHours * 60 * 60 * 1000);

      // Get basic statistics
      const basicStats = await this.getBasicStats(toolId, startDate);
      
      // Get usage by day
      const usageByDay = await this.getUsageByDay(toolId, startDate);
      
      // Get error breakdown
      const errorBreakdown = await this.getErrorBreakdown(toolId, startDate);
      
      // Get performance metrics
      const performanceMetrics = await this.getPerformanceMetrics(toolId, startDate);
      
      // Get cost analysis
      const costAnalysis = await this.getCostAnalysis(toolId, startDate);

      return {
        totalExecutions: basicStats.totalExecutions,
        successfulExecutions: basicStats.successfulExecutions,
        failedExecutions: basicStats.failedExecutions,
        successRate: basicStats.successRate,
        avgResponseTime: basicStats.avgResponseTime,
        totalCost: basicStats.totalCost,
        usageByDay,
        errorBreakdown,
        performanceMetrics,
        costAnalysis,
      };
    } catch (error) {
      this.logger.error(`Failed to get tool analytics: ${toolId}`, error);
      throw error;
    }
  }

  /**
   * Get usage statistics for multiple tools
   */
  async getMultipleToolsStats(
    toolIds: string[],
    organizationId: string,
    timeframe: 'hour' | 'day' | 'week' | 'month' = 'week',
  ): Promise<Record<string, ToolUsageStats>> {
    try {
      const timeframeHours = this.getTimeframeHours(timeframe);
      const startDate = new Date(Date.now() - timeframeHours * 60 * 60 * 1000);

      const stats: Record<string, ToolUsageStats> = {};

      for (const toolId of toolIds) {
        const toolStats = await this.getBasicStats(toolId, startDate);
        const lastExecution = await this.getLastExecution(toolId);

        stats[toolId] = {
          executions: toolStats.totalExecutions,
          successRate: toolStats.successRate,
          avgResponseTime: toolStats.avgResponseTime,
          lastUsed: lastExecution,
          errorCount: toolStats.failedExecutions,
          totalCost: toolStats.totalCost,
        };
      }

      return stats;
    } catch (error) {
      this.logger.error('Failed to get multiple tools stats', error);
      throw error;
    }
  }

  /**
   * Get top performing tools
   */
  async getTopPerformingTools(
    organizationId: string,
    limit = 10,
    metric: 'executions' | 'success_rate' | 'avg_response_time' = 'executions',
  ): Promise<Array<{ toolId: string; toolName: string; value: number }>> {
    try {
      let orderBy: string;
      let orderDirection: string;

      switch (metric) {
        case 'executions':
          orderBy = 'COUNT(*)';
          orderDirection = 'DESC';
          break;
        case 'success_rate':
          orderBy = 'AVG(CASE WHEN status = \'COMPLETED\' THEN 1.0 ELSE 0.0 END)';
          orderDirection = 'DESC';
          break;
        case 'avg_response_time':
          orderBy = 'AVG(duration)';
          orderDirection = 'ASC';
          break;
        default:
          orderBy = 'COUNT(*)';
          orderDirection = 'DESC';
      }

      const results = await this.prismaService.$queryRaw<Array<{
        tool_id: string;
        tool_name: string;
        value: number;
      }>>`
        SELECT 
          te."toolId" as tool_id,
          t.name as tool_name,
          ${orderBy === 'COUNT(*)' ? 'COUNT(*)' : orderBy} as value
        FROM "tool_executions" te
        JOIN "tools" t ON te."toolId" = t.id
        WHERE t."organizationId" = ${organizationId}
          AND t."deletedAt" IS NULL
          AND te."startedAt" >= NOW() - INTERVAL '7 days'
        GROUP BY te."toolId", t.name
        ORDER BY ${orderBy} ${orderDirection}
        LIMIT ${limit}
      `;

      return results.map(row => ({
        toolId: row.tool_id,
        toolName: row.tool_name,
        value: Number(row.value),
      }));
    } catch (error) {
      this.logger.error('Failed to get top performing tools', error);
      throw error;
    }
  }

  /**
   * Get organization-wide tool usage summary
   */
  async getOrganizationSummary(
    organizationId: string,
    timeframe: 'hour' | 'day' | 'week' | 'month' = 'week',
  ): Promise<{
    totalExecutions: number;
    activeTools: number;
    avgSuccessRate: number;
    totalCost: number;
    executionsByDay: Array<{ date: string; count: number }>;
  }> {
    try {
      const timeframeHours = this.getTimeframeHours(timeframe);
      const startDate = new Date(Date.now() - timeframeHours * 60 * 60 * 1000);

      // Get total executions
      const totalExecutions = await this.prismaService.$queryRaw<Array<{ count: number }>>`
        SELECT COUNT(*) as count
        FROM "tool_executions" te
        JOIN "tools" t ON te."toolId" = t.id
        WHERE t."organizationId" = ${organizationId}
          AND te."startedAt" >= ${startDate}
      `;

      // Get active tools count
      const activeTools = await this.prismaService.$queryRaw<Array<{ count: number }>>`
        SELECT COUNT(DISTINCT te."toolId") as count
        FROM "tool_executions" te
        JOIN "tools" t ON te."toolId" = t.id
        WHERE t."organizationId" = ${organizationId}
          AND te."startedAt" >= ${startDate}
      `;

      // Get average success rate
      const successRate = await this.prismaService.$queryRaw<Array<{ rate: number }>>`
        SELECT AVG(CASE WHEN te.status = 'COMPLETED' THEN 1.0 ELSE 0.0 END) as rate
        FROM "tool_executions" te
        JOIN "tools" t ON te."toolId" = t.id
        WHERE t."organizationId" = ${organizationId}
          AND te."startedAt" >= ${startDate}
      `;

      // Get total cost
      const totalCost = await this.prismaService.$queryRaw<Array<{ cost: number }>>`
        SELECT COALESCE(SUM(te.cost), 0) as cost
        FROM "tool_executions" te
        JOIN "tools" t ON te."toolId" = t.id
        WHERE t."organizationId" = ${organizationId}
          AND te."startedAt" >= ${startDate}
      `;

      // Get executions by day
      const executionsByDay = await this.prismaService.$queryRaw<Array<{
        date: string;
        count: number;
      }>>`
        SELECT 
          DATE(te."startedAt") as date,
          COUNT(*) as count
        FROM "tool_executions" te
        JOIN "tools" t ON te."toolId" = t.id
        WHERE t."organizationId" = ${organizationId}
          AND te."startedAt" >= ${startDate}
        GROUP BY DATE(te."startedAt")
        ORDER BY date
      `;

      return {
        totalExecutions: Number(totalExecutions[0]?.count || 0),
        activeTools: Number(activeTools[0]?.count || 0),
        avgSuccessRate: Number(successRate[0]?.rate || 0) * 100,
        totalCost: Number(totalCost[0]?.cost || 0),
        executionsByDay: executionsByDay.map(row => ({
          date: row.date,
          count: Number(row.count),
        })),
      };
    } catch (error) {
      this.logger.error('Failed to get organization summary', error);
      throw error;
    }
  }

  /**
   * Get basic statistics for a tool
   */
  private async getBasicStats(toolId: string, startDate: Date) {
    const stats = await this.prismaService.$queryRaw<Array<{
      total_executions: number;
      successful_executions: number;
      failed_executions: number;
      avg_response_time: number;
      total_cost: number;
    }>>`
      SELECT 
        COUNT(*) as total_executions,
        SUM(CASE WHEN status = 'COMPLETED' THEN 1 ELSE 0 END) as successful_executions,
        SUM(CASE WHEN status = 'FAILED' THEN 1 ELSE 0 END) as failed_executions,
        COALESCE(AVG(duration), 0) as avg_response_time,
        COALESCE(SUM(cost), 0) as total_cost
      FROM "tool_executions"
      WHERE "toolId" = ${toolId}
        AND "startedAt" >= ${startDate}
    `;

    const row = stats[0];
    const totalExecutions = Number(row?.total_executions || 0);
    const successfulExecutions = Number(row?.successful_executions || 0);
    const failedExecutions = Number(row?.failed_executions || 0);

    return {
      totalExecutions,
      successfulExecutions,
      failedExecutions,
      successRate: totalExecutions > 0 ? (successfulExecutions / totalExecutions) * 100 : 0,
      avgResponseTime: Number(row?.avg_response_time || 0),
      totalCost: Number(row?.total_cost || 0),
    };
  }

  /**
   * Get usage by day
   */
  private async getUsageByDay(toolId: string, startDate: Date) {
    const results = await this.prismaService.$queryRaw<Array<{
      date: string;
      count: number;
      success_rate: number;
    }>>`
      SELECT 
        DATE("startedAt") as date,
        COUNT(*) as count,
        AVG(CASE WHEN status = 'COMPLETED' THEN 1.0 ELSE 0.0 END) as success_rate
      FROM "tool_executions"
      WHERE "toolId" = ${toolId}
        AND "startedAt" >= ${startDate}
      GROUP BY DATE("startedAt")
      ORDER BY date
    `;

    return results.map(row => ({
      date: row.date,
      count: Number(row.count),
      successRate: Number(row.success_rate) * 100,
    }));
  }

  /**
   * Get error breakdown
   */
  private async getErrorBreakdown(toolId: string, startDate: Date) {
    const results = await this.prismaService.$queryRaw<Array<{
      error: string;
      count: number;
    }>>`
      SELECT 
        COALESCE(error, 'Unknown') as error,
        COUNT(*) as count
      FROM "tool_executions"
      WHERE "toolId" = ${toolId}
        AND "startedAt" >= ${startDate}
        AND status = 'FAILED'
      GROUP BY error
      ORDER BY count DESC
      LIMIT 10
    `;

    const totalErrors = results.reduce((sum, row) => sum + Number(row.count), 0);

    return results.map(row => ({
      error: row.error,
      count: Number(row.count),
      percentage: totalErrors > 0 ? (Number(row.count) / totalErrors) * 100 : 0,
    }));
  }

  /**
   * Get performance metrics
   */
  private async getPerformanceMetrics(toolId: string, startDate: Date) {
    const results = await this.prismaService.$queryRaw<Array<{
      p50: number;
      p90: number;
      p95: number;
      p99: number;
    }>>`
      SELECT 
        PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY duration) as p50,
        PERCENTILE_CONT(0.9) WITHIN GROUP (ORDER BY duration) as p90,
        PERCENTILE_CONT(0.95) WITHIN GROUP (ORDER BY duration) as p95,
        PERCENTILE_CONT(0.99) WITHIN GROUP (ORDER BY duration) as p99
      FROM "tool_executions"
      WHERE "toolId" = ${toolId}
        AND "startedAt" >= ${startDate}
        AND duration IS NOT NULL
        AND status = 'COMPLETED'
    `;

    const row = results[0];
    return {
      p50ResponseTime: Number(row?.p50 || 0),
      p90ResponseTime: Number(row?.p90 || 0),
      p95ResponseTime: Number(row?.p95 || 0),
      p99ResponseTime: Number(row?.p99 || 0),
    };
  }

  /**
   * Get cost analysis
   */
  private async getCostAnalysis(toolId: string, startDate: Date) {
    const totalCostResult = await this.prismaService.$queryRaw<Array<{
      total_cost: number;
      avg_cost: number;
    }>>`
      SELECT 
        COALESCE(SUM(cost), 0) as total_cost,
        COALESCE(AVG(cost), 0) as avg_cost
      FROM "tool_executions"
      WHERE "toolId" = ${toolId}
        AND "startedAt" >= ${startDate}
        AND cost IS NOT NULL
    `;

    const costByDay = await this.prismaService.$queryRaw<Array<{
      date: string;
      cost: number;
    }>>`
      SELECT 
        DATE("startedAt") as date,
        COALESCE(SUM(cost), 0) as cost
      FROM "tool_executions"
      WHERE "toolId" = ${toolId}
        AND "startedAt" >= ${startDate}
        AND cost IS NOT NULL
      GROUP BY DATE("startedAt")
      ORDER BY date
    `;

    const row = totalCostResult[0];
    return {
      totalCost: Number(row?.total_cost || 0),
      avgCostPerExecution: Number(row?.avg_cost || 0),
      costByDay: costByDay.map(dayRow => ({
        date: dayRow.date,
        cost: Number(dayRow.cost),
      })),
    };
  }

  /**
   * Get last execution date
   */
  private async getLastExecution(toolId: string): Promise<Date | null> {
    const result = await this.prismaService.$queryRaw<Array<{ last_execution: Date }>>`
      SELECT MAX("startedAt") as last_execution
      FROM "tool_executions"
      WHERE "toolId" = ${toolId}
    `;

    return result[0]?.last_execution || null;
  }

  /**
   * Convert timeframe to hours
   */
  private getTimeframeHours(timeframe: 'hour' | 'day' | 'week' | 'month'): number {
    switch (timeframe) {
      case 'hour':
        return 1;
      case 'day':
        return 24;
      case 'week':
        return 24 * 7;
      case 'month':
        return 24 * 30;
      default:
        return 24 * 7; // Default to week
    }
  }
}