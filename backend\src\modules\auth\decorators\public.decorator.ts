import { SetMetadata } from '@nestjs/common';

export const IS_PUBLIC_KEY = 'isPublic';

/**
 * Public Decorator
 * 
 * Mark routes as public to bypass authentication.
 * Use this decorator on controllers or individual routes
 * that should be accessible without authentication.
 * 
 * @example
 * ```typescript
 * @Public()
 * @Get('health')
 * checkHealth() {
 *   return { status: 'ok' };
 * }
 * ```
 */
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true); 