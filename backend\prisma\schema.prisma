// SynapseAI Platform Database Schema
// Multi-tenant SaaS platform for AI agents, tools, workflows, and knowledge management

generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// =============================================================================
// CORE ENTITIES - Foundation Layer
// =============================================================================

model Organization {
  id        String   @id @default(cuid())
  name      String
  slug      String   @unique
  domain    String?  @unique
  logo      String?
  settings  Json     @default("{}")
  metadata  Json     @default("{}")
  
  // Subscription & Billing
  planType        PlanType @default(FREE)
  billingEmail    String?
  stripeCustomerId String? @unique
  
  // Quotas & Limits
  quotas Json @default("{}")
  
  // Status & Lifecycle
  status    OrganizationStatus @default(ACTIVE)
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt
  deletedAt DateTime?

  // Relations
  users              User[]
  agents             Agent[]
  tools              Tool[]
  hybrids            Hybrid[]
  templates          Template[]
  sessions           Session[]
  hitlRequests       HITLRequest[]
  documents          Document[]
  widgets            Widget[]
  sandboxes          Sandbox[]
  notifications      Notification[]
  billingRecords     BillingRecord[]
  quotaUsages        QuotaUsage[]
  analyticsEvents    AnalyticsEvent[]
  providers          Provider[]
  knowledgeBases     KnowledgeBase[]
  marketplaceListings MarketplaceListing[]
  marketplaceDownloads MarketplaceDownload[]
  abTests            AbTest[]
  apiKeys            ApiKey[]

  @@map("organizations")
}

model User {
  id       String  @id @default(cuid())
  email    String  @unique
  username String?
  
  // Profile
  firstName String?
  lastName  String?
  avatar    String?
  timezone  String  @default("UTC")
  locale    String  @default("en")
  
  // Authentication
  passwordHash     String?
  emailVerified    DateTime?
  emailVerifyToken String?
  resetToken       String?
  resetTokenExpiry DateTime?
  
  // Multi-factor Authentication
  mfaEnabled Boolean @default(false)
  mfaSecret  String?
  
  // Preferences & Settings
  preferences Json @default("{}")
  settings    Json @default("{}")
  
  // Status & Lifecycle
  status    UserStatus @default(ACTIVE)
  lastSeen  DateTime?
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  deletedAt DateTime?

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  role           Role         @relation(fields: [roleId], references: [id])
  roleId         String

  // Relations
  sessions           Session[]
  createdAgents      Agent[]        @relation("CreatedAgents")
  createdTools       Tool[]         @relation("CreatedTools")
  createdHybrids     Hybrid[]       @relation("CreatedHybrids")
  createdTemplates   Template[]     @relation("CreatedTemplates")
  hitlAssignments    HITLRequest[]  @relation("HITLAssignee")
  hitlRequests       HITLRequest[]  @relation("HITLRequester")
  uploadedDocuments  Document[]     @relation("DocumentUploader")
  createdWidgets     Widget[]       @relation("CreatedWidgets")
  sandboxes          Sandbox[]
  sentNotifications  Notification[] @relation("NotificationSender")
  notifications      Notification[] @relation("NotificationRecipient")
  analyticsEvents    AnalyticsEvent[]
  agentVersions      AgentVersion[]
  publishedListings  MarketplaceListing[]
  marketplaceDownloads MarketplaceDownload[]
  agentRatings       AgentRating[]
  abTests            AbTest[]
  createdApiKeys     ApiKey[]         @relation("CreatedApiKeys")

  @@unique([email, organizationId])
  @@map("users")
}

model Role {
  id          String       @id @default(cuid())
  name        String
  description String?
  permissions Json         @default("[]")
  level       RoleLevel    @default(VIEWER)
  isSystem    Boolean      @default(false)
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  users User[]

  @@unique([name])
  @@map("roles")
}

// =============================================================================
// SESSION & MEMORY MANAGEMENT
// =============================================================================

model Session {
  id        String      @id @default(cuid())
  sessionId String      @unique
  type      SessionType @default(CHAT)
  
  // Context & Memory
  context    Json @default("{}")
  memory     Json @default("{}")
  metadata   Json @default("{}")
  
  // Lifecycle
  status    SessionStatus @default(ACTIVE)
  expiresAt DateTime?
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
  
  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String?
  user           User?        @relation(fields: [userId], references: [id], onDelete: SetNull)

  // Relations
  agentExecutions  AgentExecution[]
  toolExecutions   ToolExecution[]
  hybridExecutions HybridExecution[]
  messages         Message[]

  @@map("sessions")
}

// =============================================================================
// AGENT BUILDER & TEMPLATES
// =============================================================================

model Template {
  id          String       @id @default(cuid())
  name        String
  description String?
  type        TemplateType @default(AGENT)
  
  // Template Content
  content     Json
  schema      Json         @default("{}")
  variables   Json         @default("[]")
  
  // Marketplace & Sharing
  isPublic    Boolean @default(false)
  category    String?
  tags        String[]
  rating      Float?   @default(0)
  downloads   Int      @default(0)
  
  // Version Control
  version     String  @default("1.0.0")
  parentId    String?
  parent      Template? @relation("TemplateVersions", fields: [parentId], references: [id])
  versions    Template[] @relation("TemplateVersions")
  
  // Performance & Analytics
  performance Json @default("{}")
  
  // Status & Lifecycle
  status    TemplateStatus @default(DRAFT)
  createdAt DateTime       @default(now())
  updatedAt DateTime       @updatedAt
  deletedAt DateTime?

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdById    String
  createdBy      User         @relation("CreatedTemplates", fields: [createdById], references: [id])

  // Relations
  agents Agent[]

  @@map("templates")
}

model Agent {
  id          String    @id @default(cuid())
  name        String
  description String?
  
  // Configuration
  config      Json
  prompt      String?
  model       String    @default("gpt-4")
  temperature Float     @default(0.7)
  maxTokens   Int       @default(2048)
  
  // Capabilities & Tools
  tools       String[]
  capabilities Json     @default("[]")
  
  // Marketplace
  isPublic    Boolean   @default(false)
  category    String?
  tags        String[]
  
  // Performance & Analytics
  performance Json      @default("{}")
  analytics   Json      @default("{}")
  
  // Version Control
  version     String    @default("1.0.0")
  
  // Status & Lifecycle
  status      AgentStatus @default(DRAFT)
  createdAt   DateTime    @default(now())
  updatedAt   DateTime    @updatedAt
  deletedAt   DateTime?

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdById    String
  createdBy      User         @relation("CreatedAgents", fields: [createdById], references: [id])
  templateId     String?
  template       Template?    @relation(fields: [templateId], references: [id])

  // Relations
  executions       AgentExecution[]
  hybrids          Hybrid[]         @relation("HybridAgents")
  widgets          Widget[]
  childVersions    AgentVersion[]   @relation("ChildVersions")
  parentVersions   AgentVersion[]   @relation("ParentVersions")
  marketplaceListing MarketplaceListing[]
  marketplaceDownloads MarketplaceDownload[]
  ratings          AgentRating[]
  abTestVersion1   AbTest[]         @relation("AbTestVersion1")
  abTestVersion2   AbTest[]         @relation("AbTestVersion2")

  @@map("agents")
}

model AgentExecution {
  id        String            @id @default(cuid())
  
  // Execution Context
  input     String
  output    String?
  error     String?
  metadata  Json              @default("{}")
  
  // Performance Metrics
  duration      Int?              // milliseconds
  tokenUsage    Json              @default("{}")
  cost          Float?
  
  // Status & Lifecycle
  status        ExecutionStatus   @default(PENDING)
  startedAt     DateTime          @default(now())
  completedAt   DateTime?
  
  // Relations
  agentId       String
  agent         Agent             @relation(fields: [agentId], references: [id], onDelete: Cascade)
  sessionId     String?
  session       Session?          @relation(fields: [sessionId], references: [id], onDelete: SetNull)

  @@map("agent_executions")
}

// =============================================================================
// TOOL MANAGER
// =============================================================================

model Tool {
  id          String     @id @default(cuid())
  name        String
  description String?
  type        ToolType   @default(API)
  
  // Configuration
  config      Json
  schema      Json       @default("{}")
  endpoint    String?
  method      HttpMethod @default(POST)
  headers     Json       @default("{}")
  
  // Authentication & Security
  authType    AuthType   @default(NONE)
  credentials Json       @default("{}")
  
  // Marketplace & Sharing
  isPublic    Boolean    @default(false)
  category    String?
  tags        String[]
  rating      Float?     @default(0)
  downloads   Int        @default(0)
  
  // Performance & Analytics
  performance Json       @default("{}")
  analytics   Json       @default("{}")
  
  // Version Control
  version     String     @default("1.0.0")
  
  // Status & Lifecycle
  status      ToolStatus @default(DRAFT)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  deletedAt   DateTime?

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdById    String
  createdBy      User         @relation("CreatedTools", fields: [createdById], references: [id])

  // Relations
  executions ToolExecution[]
  hybrids    Hybrid[]        @relation("HybridTools")

  @@map("tools")
}

model ToolExecution {
  id        String          @id @default(cuid())
  
  // Execution Context
  input     Json
  output    Json?
  error     String?
  metadata  Json            @default("{}")
  
  // Performance Metrics
  duration  Int?            // milliseconds
  cost      Float?
  
  // Status & Lifecycle
  status      ExecutionStatus @default(PENDING)
  startedAt   DateTime        @default(now())
  completedAt DateTime?
  
  // Relations
  toolId    String
  tool      Tool            @relation(fields: [toolId], references: [id], onDelete: Cascade)
  sessionId String?
  session   Session?        @relation(fields: [sessionId], references: [id], onDelete: SetNull)

  @@map("tool_executions")
}

// =============================================================================
// HYBRID WORKFLOWS
// =============================================================================

model Hybrid {
  id          String      @id @default(cuid())
  name        String
  description String?
  
  // Workflow Definition
  workflow    Json
  config      Json        @default("{}")
  
  // Performance & Analytics
  performance Json        @default("{}")
  analytics   Json        @default("{}")
  
  // Version Control
  version     String      @default("1.0.0")
  
  // Status & Lifecycle
  status      HybridStatus @default(DRAFT)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  deletedAt   DateTime?

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdById    String
  createdBy      User         @relation("CreatedHybrids", fields: [createdById], references: [id])

  // Relations
  agents     Agent[]           @relation("HybridAgents")
  tools      Tool[]            @relation("HybridTools")
  executions HybridExecution[]

  @@map("hybrids")
}

model HybridExecution {
  id        String          @id @default(cuid())
  
  // Execution Context
  input     Json
  output    Json?
  error     String?
  metadata  Json            @default("{}")
  steps     Json            @default("[]")
  
  // Performance Metrics
  duration  Int?            // milliseconds
  cost      Float?
  
  // Status & Lifecycle
  status      ExecutionStatus @default(PENDING)
  startedAt   DateTime        @default(now())
  completedAt DateTime?
  
  // Relations
  hybridId  String
  hybrid    Hybrid          @relation(fields: [hybridId], references: [id], onDelete: Cascade)
  sessionId String?
  session   Session?        @relation(fields: [sessionId], references: [id], onDelete: SetNull)

  @@map("hybrid_executions")
}

// =============================================================================
// PROVIDER MANAGEMENT
// =============================================================================

model Provider {
  id       String       @id @default(cuid())
  name     String
  type     ProviderType @default(LLM)
  
  // Configuration
  config      Json
  credentials Json        @default("{}")
  endpoints   Json        @default("{}")
  
  // Performance & Monitoring
  performance Json        @default("{}")
  health      Json        @default("{}")
  
  // Billing & Usage
  pricing     Json        @default("{}")
  
  // Status & Lifecycle
  status    ProviderStatus @default(ACTIVE)
  createdAt DateTime       @default(now())
  updatedAt DateTime       @updatedAt

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("providers")
}

// =============================================================================
// HITL (Human-in-the-Loop) WORKFLOWS
// =============================================================================

model HITLRequest {
  id          String         @id @default(cuid())
  type        HITLType       @default(APPROVAL)
  title       String
  description String?
  
  // Request Context
  context     Json
  metadata    Json           @default("{}")
  
  // Workflow Configuration
  priority    Priority       @default(MEDIUM)
  deadline    DateTime?
  
  // Approval Chain
  approvalChain Json         @default("[]")
  currentStep   Int          @default(0)
  
  // Decision & Resolution
  decision      HITLDecision?
  resolution    String?
  feedback      String?
  
  // Status & Lifecycle
  status      HITLStatus @default(PENDING)
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  resolvedAt  DateTime?

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  requesterId    String
  requester      User         @relation("HITLRequester", fields: [requesterId], references: [id])
  assigneeId     String?
  assignee       User?        @relation("HITLAssignee", fields: [assigneeId], references: [id])

  @@map("hitl_requests")
}

// =============================================================================
// KNOWLEDGE BASE & DOCUMENTS
// =============================================================================

model KnowledgeBase {
  id          String    @id @default(cuid())
  name        String
  description String?
  
  // Configuration
  config      Json      @default("{}")
  indexConfig Json      @default("{}")
  
  // Vector Database Settings
  namespace   String?
  
  // Analytics
  analytics   Json      @default("{}")
  
  // Status & Lifecycle
  status    KnowledgeBaseStatus @default(ACTIVE)
  createdAt DateTime            @default(now())
  updatedAt DateTime            @updatedAt

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  // Relations
  documents Document[]

  @@map("knowledge_bases")
}

model Document {
  id       String       @id @default(cuid())
  title    String
  content  String?
  type     DocumentType @default(TEXT)
  
  // File Information
  filename    String?
  mimeType    String?
  size        Int?        // bytes
  url         String?
  
  // Processing Status
  processingStatus ProcessingStatus @default(PENDING)
  extractedText    String?
  metadata         Json             @default("{}")
  
  // Vector Embeddings
  chunked         Boolean @default(false)
  chunkCount      Int     @default(0)
  embeddingModel  String?
  vectorIds       String[]
  
  // Classification & Organization
  category    String?
  tags        String[]
  language    String?  @default("en")
  
  // Version Control
  version     String   @default("1.0.0")
  checksum    String?
  
  // Status & Lifecycle
  status    DocumentStatus @default(DRAFT)
  createdAt DateTime       @default(now())
  updatedAt DateTime       @updatedAt
  deletedAt DateTime?

  // Multi-tenant Relations
  organizationId  String
  organization    Organization  @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  knowledgeBaseId String?
  knowledgeBase   KnowledgeBase? @relation(fields: [knowledgeBaseId], references: [id])
  uploadedById    String
  uploadedBy      User          @relation("DocumentUploader", fields: [uploadedById], references: [id])

  @@map("documents")
}

// =============================================================================
// WIDGETS & EMBEDDING
// =============================================================================

model Widget {
  id          String      @id @default(cuid())
  name        String
  description String?
  type        WidgetType  @default(AGENT_CHAT)
  
  // Configuration
  config      Json
  theme       Json        @default("{}")
  customCSS   String?
  
  // Embedding Information
  embedCode   String
  domains     String[]
  
  // Analytics
  analytics   Json        @default("{}")
  
  // Status & Lifecycle
  status    WidgetStatus @default(DRAFT)
  createdAt DateTime     @default(now())
  updatedAt DateTime     @updatedAt
  deletedAt DateTime?

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdById    String
  createdBy      User         @relation("CreatedWidgets", fields: [createdById], references: [id])
  agentId        String?
  agent          Agent?       @relation(fields: [agentId], references: [id])

  @@map("widgets")
}

// =============================================================================
// SANDBOX & TESTING
// =============================================================================

model Sandbox {
  id          String        @id @default(cuid())
  name        String
  description String?
  type        SandboxType   @default(DEVELOPMENT)
  
  // Configuration
  config      Json
  environment Json          @default("{}")
  
  // Resource Limits
  resources   Json          @default("{}")
  
  // Status & Lifecycle
  status    SandboxStatus @default(ACTIVE)
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt
  deletedAt DateTime?

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String
  user           User         @relation(fields: [userId], references: [id])

  @@map("sandboxes")
}

// =============================================================================
// ANALYTICS & MONITORING
// =============================================================================

model AnalyticsEvent {
  id        String    @id @default(cuid())
  event     String
  category  String
  action    String
  label     String?
  value     Float?
  
  // Context & Metadata
  properties Json     @default("{}")
  userAgent  String?
  ipAddress  String?
  sessionId  String?
  
  // Timestamp
  timestamp DateTime @default(now())

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  userId         String?
  user           User?        @relation(fields: [userId], references: [id])

  @@map("analytics_events")
}

// =============================================================================
// BILLING & USAGE TRACKING
// =============================================================================

model BillingRecord {
  id          String      @id @default(cuid())
  type        BillingType @default(USAGE)
  
  // Financial Information
  amount      Float
  currency    String      @default("USD")
  
  // Usage Details
  resource    String
  quantity    Float
  unitPrice   Float
  
  // Billing Period
  periodStart DateTime
  periodEnd   DateTime
  
  // External References
  invoiceId   String?
  paymentId   String?
  
  // Metadata
  metadata    Json        @default("{}")
  
  // Status & Lifecycle
  status    BillingStatus @default(PENDING)
  createdAt DateTime      @default(now())
  updatedAt DateTime      @updatedAt

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@map("billing_records")
}

model QuotaUsage {
  id       String @id @default(cuid())
  resource String
  
  // Usage Metrics
  used     Float
  limit    Float
  period   String
  
  // Time Period
  periodStart DateTime
  periodEnd   DateTime
  
  // Metadata
  metadata Json     @default("{}")
  
  // Status & Lifecycle
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)

  @@unique([organizationId, resource, periodStart])
  @@map("quota_usages")
}

// =============================================================================
// NOTIFICATIONS
// =============================================================================

model Notification {
  id      String             @id @default(cuid())
  type    NotificationType   @default(INFO)
  channel NotificationChannel @default(EMAIL)
  
  // Content
  title   String
  message String
  data    Json               @default("{}")
  
  // Delivery Information
  recipient   String
  deliveredAt DateTime?
  readAt      DateTime?
  
  // Status & Lifecycle
  status    NotificationStatus @default(PENDING)
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt

  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  senderId       String?
  sender         User?        @relation("NotificationSender", fields: [senderId], references: [id])
  recipientId    String?
  recipientUser  User?        @relation("NotificationRecipient", fields: [recipientId], references: [id])

  @@map("notifications")
}

// =============================================================================
// AGENT MARKETPLACE & VERSIONING
// =============================================================================

model AgentVersion {
  id            String   @id @default(cuid())
  childAgentId  String
  parentAgentId String
  version       String
  createdById   String
  createdAt     DateTime @default(now())

  // Relations
  childAgent  Agent @relation("ChildVersions", fields: [childAgentId], references: [id], onDelete: Cascade)
  parentAgent Agent @relation("ParentVersions", fields: [parentAgentId], references: [id], onDelete: Cascade)
  createdBy   User  @relation(fields: [createdById], references: [id])

  @@unique([childAgentId])
  @@map("agent_versions")
}

model MarketplaceListing {
  id             String            @id @default(cuid())
  agentId        String
  organizationId String
  publisherId    String
  
  // Marketplace Data
  category       String?
  tags           String[]
  pricingModel   MarketplacePricing @default(FREE)
  price          Float             @default(0)
  downloads      Int               @default(0)
  rating         Float             @default(0)
  reviewCount    Int               @default(0)
  isFeatured     Boolean           @default(false)
  
  // Status & Lifecycle
  isActive       Boolean           @default(true)
  publishedAt    DateTime          @default(now())
  unpublishedAt  DateTime?
  createdAt      DateTime          @default(now())
  updatedAt      DateTime          @updatedAt

  // Relations
  agent        Agent        @relation(fields: [agentId], references: [id], onDelete: Cascade)
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  publisher    User         @relation(fields: [publisherId], references: [id])
  downloadRecords MarketplaceDownload[]
  // Relation removed to fix schema error
  // ratings      AgentRating[]

  @@unique([agentId, organizationId])
  @@map("marketplace_listings")
}

model MarketplaceDownload {
  id             String   @id @default(cuid())
  agentId        String
  userId         String
  organizationId String
  listingId      String
  newAgentId     String
  createdAt      DateTime @default(now())

  // Relations
  agent        Agent             @relation(fields: [agentId], references: [id])
  user         User              @relation(fields: [userId], references: [id])
  organization Organization      @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  listing      MarketplaceListing @relation(fields: [listingId], references: [id])

  @@map("marketplace_downloads")
}

model AgentRating {
  id        String    @id @default(cuid())
  agentId   String
  userId    String
  rating    Int       // 1-5
  review    String?
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt

  // Relations
  agent   Agent             @relation(fields: [agentId], references: [id], onDelete: Cascade, map: "agent_ratings_agent_fkey")
  user    User              @relation(fields: [userId], references: [id])
  // Relation removed due to error in schema
  // listing MarketplaceListing? @relation(fields: [agentId], references: [agentId])

  @@unique([agentId, userId])
  @@map("agent_ratings")
}

model AbTest {
  id           String      @id @default(cuid())
  name         String
  description  String?
  version1Id   String
  version2Id   String
  distribution Int         @default(50) // Percentage for version 1
  startDate    DateTime    @default(now())
  endDate      DateTime
  status       AbTestStatus @default(ACTIVE)
  
  organizationId String
  createdById    String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  organization Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdBy    User         @relation(fields: [createdById], references: [id])
  version1     Agent        @relation("AbTestVersion1", fields: [version1Id], references: [id])
  version2     Agent        @relation("AbTestVersion2", fields: [version2Id], references: [id])

  @@map("ab_tests")
}

model Message {
  id        String   @id @default(cuid())
  sessionId String
  role      String   // 'user', 'assistant', 'system'
  content   String
  metadata  Json     @default("{}")
  tokens    Int?
  createdAt DateTime @default(now())

  // Relations
  session Session @relation(fields: [sessionId], references: [id], onDelete: Cascade)

  @@map("messages")
}

// =============================================================================
// ENUMS
// =============================================================================

enum PlanType {
  FREE
  STARTER
  PRO
  ENTERPRISE
  CUSTOM
}

enum OrganizationStatus {
  ACTIVE
  SUSPENDED
  DELETED
  TRIAL
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_VERIFICATION
}

enum RoleLevel {
  SUPER_ADMIN
  ORG_ADMIN
  DEVELOPER
  VIEWER
}

enum SessionType {
  CHAT
  AGENT
  TOOL
  HYBRID
  SANDBOX
}

enum SessionStatus {
  ACTIVE
  PAUSED
  COMPLETED
  EXPIRED
  ERROR
}

enum TemplateType {
  AGENT
  TOOL
  HYBRID
  WORKFLOW
}

enum TemplateStatus {
  DRAFT
  PUBLISHED
  ARCHIVED
  DEPRECATED
}

enum AgentStatus {
  DRAFT
  ACTIVE
  INACTIVE
  ARCHIVED
  ERROR
}

enum ExecutionStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
  TIMEOUT
}

enum ToolType {
  API
  DATABASE
  FILE
  WEBHOOK
  CUSTOM
}

enum HttpMethod {
  GET
  POST
  PUT
  DELETE
  PATCH
  HEAD
  OPTIONS
}

enum AuthType {
  NONE
  API_KEY
  BEARER_TOKEN
  BASIC_AUTH
  OAUTH2
  CUSTOM
}

enum ToolStatus {
  DRAFT
  ACTIVE
  INACTIVE
  DEPRECATED
  ERROR
}

enum HybridStatus {
  DRAFT
  ACTIVE
  INACTIVE
  ARCHIVED
  ERROR
}

enum ProviderType {
  LLM
  EMBEDDING
  VECTOR_DATABASE
  STORAGE
  NOTIFICATION
  ANALYTICS
}

enum ProviderStatus {
  ACTIVE
  INACTIVE
  ERROR
  MAINTENANCE
}

enum HITLType {
  APPROVAL
  REVIEW
  FEEDBACK
  ESCALATION
  CONSULTATION
}

enum Priority {
  LOW
  MEDIUM
  HIGH
  URGENT
  CRITICAL
}

enum HITLStatus {
  PENDING
  IN_PROGRESS
  APPROVED
  REJECTED
  ESCALATED
  EXPIRED
}

enum HITLDecision {
  APPROVE
  REJECT
  REQUEST_CHANGES
  ESCALATE
}

enum KnowledgeBaseStatus {
  ACTIVE
  INACTIVE
  SYNCING
  ERROR
}

enum DocumentType {
  TEXT
  PDF
  DOCX
  HTML
  MARKDOWN
  JSON
  CSV
  XML
  IMAGE
  AUDIO
  VIDEO
  URL
}

enum ProcessingStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  SKIPPED
}

enum DocumentStatus {
  DRAFT
  PROCESSING
  ACTIVE
  ARCHIVED
  ERROR
}

enum WidgetType {
  AGENT_CHAT
  TOOL_EXECUTOR
  HYBRID_WORKFLOW
  KNOWLEDGE_SEARCH
  CUSTOM
}

enum WidgetStatus {
  DRAFT
  ACTIVE
  INACTIVE
  ARCHIVED
}

enum SandboxType {
  DEVELOPMENT
  TESTING
  STAGING
  DEMO
}

enum SandboxStatus {
  ACTIVE
  PAUSED
  STOPPED
  ERROR
}

enum BillingType {
  USAGE
  SUBSCRIPTION
  ONE_TIME
  REFUND
  ADJUSTMENT
}

enum BillingStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
  REFUNDED
}

enum NotificationType {
  INFO
  WARNING
  ERROR
  SUCCESS
  SYSTEM
  BILLING
  SECURITY
}

enum NotificationChannel {
  EMAIL
  SMS
  PUSH
  WEBHOOK
  SLACK
  TEAMS
  IN_APP
}

enum NotificationStatus {
  PENDING
  SENT
  DELIVERED
  FAILED
  READ
}

enum MarketplacePricing {
  FREE
  PAID
  SUBSCRIPTION
}

enum AbTestStatus {
  ACTIVE
  PAUSED
  COMPLETED
  CANCELLED
}

// =============================================================================
// API KEYS & EXTERNAL ACCESS
// =============================================================================

model ApiKey {
  id             String   @id @default(cuid())
  name           String
  key            String   @unique
  hashedKey      String   @unique
  permissions    Json     @default("[]")
  
  // Usage tracking
  lastUsedAt     DateTime?
  usageCount     Int      @default(0)
  
  // Limits
  rateLimit      Int?     // Requests per minute
  quotaLimit     Int?     // Total requests
  
  // Status & Lifecycle
  isActive       Boolean  @default(true)
  expiresAt      DateTime?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  
  // Multi-tenant Relations
  organizationId String
  organization   Organization @relation(fields: [organizationId], references: [id], onDelete: Cascade)
  createdById    String
  createdBy      User         @relation("CreatedApiKeys", fields: [createdById], references: [id])

  @@map("api_keys")
}
