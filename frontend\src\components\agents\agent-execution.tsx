'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Play,
  Send,
  RotateCcw,
  Download,
  Setting<PERSON>,
  Brain,
  User,
  <PERSON><PERSON>,
  <PERSON>,
  Zap,
  Eye,
  Trash2,
  Copy,
  Save,
  Loader2,
  AlertCircle,
  CheckCircle2,
  MessageSquare,
  Activity,
  FileText,
  ExternalLink
} from 'lucide-react';
import axios from 'axios';
import { useToast } from '@/lib/usetoast';

interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    tokens?: number;
    duration?: number;
    model?: string;
  };
}

interface ExecutionResult {
  id: string;
  output: string;
  sessionId?: string;
  duration: number;
  tokenUsage: {
    prompt: number;
    completion: number;
    total: number;
  };
  status: string;
  metadata: Record<string, any>;
}

interface Session {
  id: string;
  name: string;
  createdAt: string;
  messageCount: number;
}

interface AgentExecutionProps {
  agentId: string;
}

export default function AgentExecution({ agentId }: AgentExecutionProps) {
  const { showSuccess, showError } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // State
  const [agent, setAgent] = useState<any>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentInput, setCurrentInput] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [currentSession, setCurrentSession] = useState<Session | null>(null);
  const [sessions, setSessions] = useState<Session[]>([]);
  const [showSettings, setShowSettings] = useState(false);
  const [executionHistory, setExecutionHistory] = useState<ExecutionResult[]>([]);

  // Settings
  const [settings, setSettings] = useState({
    autoScroll: true,
    showMetadata: true,
    streamResponse: false,
    saveHistory: true,
  });

  useEffect(() => {
    fetchAgent();
    fetchSessions();
    scrollToBottom();
  }, [agentId]);

  useEffect(() => {
    if (settings.autoScroll) {
      scrollToBottom();
    }
  }, [messages, settings.autoScroll]);

  const fetchAgent = async () => {
    try {
      const response = await axios.get(`/api/v1/agents/${agentId}`);
      setAgent(response.data);
    } catch (error: any) {
      showError('Failed to load agent');
    }
  };

  const fetchSessions = async () => {
    try {
      const response = await axios.get(`/api/v1/agents/${agentId}/sessions`);
      setSessions(response.data.sessions || []);
    } catch (error: any) {
      console.error('Error fetching sessions:', error);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const createNewSession = async () => {
    try {
      const response = await axios.post(`/api/v1/agents/${agentId}/sessions`, {
        name: `Session ${new Date().toLocaleTimeString()}`,
      });
      
      const newSession = response.data;
      setCurrentSession(newSession);
      setSessions(prev => [newSession, ...prev]);
      setMessages([]);
      showSuccess('New session created');
    } catch (error: any) {
      showError('Failed to create session');
    }
  };

  const loadSession = async (sessionId: string) => {
    try {
      const response = await axios.get(`/api/v1/sessions/${sessionId}/messages`);
      const sessionMessages = response.data.messages.map((msg: any) => ({
        id: msg.id,
        role: msg.role,
        content: msg.content,
        timestamp: new Date(msg.createdAt),
        metadata: msg.metadata,
      }));
      
      setMessages(sessionMessages);
      setCurrentSession(sessions.find(s => s.id === sessionId) || null);
    } catch (error: any) {
      showError('Failed to load session');
    }
  };

  const executeAgent = async () => {
    if (!currentInput.trim()) {
      showError('Please enter a message');
      return;
    }

    if (!agent) {
      showError('Agent not loaded');
      return;
    }

    setIsExecuting(true);

    // Add user message immediately
    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: currentInput.trim(),
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    
    const inputToExecute = currentInput.trim();
    setCurrentInput('');

    try {
      const response = await axios.post(`/api/v1/agents/${agentId}/execute`, {
        input: inputToExecute,
        sessionId: currentSession?.id,
      });

      const result: ExecutionResult = response.data;

      // Add assistant message
      const assistantMessage: Message = {
        id: result.id,
        role: 'assistant',
        content: result.output,
        timestamp: new Date(),
        metadata: {
          tokens: result.tokenUsage?.total,
          duration: result.duration,
          model: result.metadata?.model,
        },
      };

      setMessages(prev => [...prev, assistantMessage]);

      // Update session if exists
      if (currentSession) {
        setCurrentSession(prev => prev ? {
          ...prev,
          messageCount: prev.messageCount + 2,
        } : null);
      }

      // Add to execution history
      if (settings.saveHistory) {
        setExecutionHistory(prev => [result, ...prev.slice(0, 99)]);
      }

      showSuccess('Execution completed successfully');
    } catch (error: any) {
      const errorMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: `Error: ${error.response?.data?.message || 'Execution failed'}`,
        timestamp: new Date(),
        metadata: {
          error: true,
        },
      };

      setMessages(prev => [...prev, errorMessage]);
      showError('Execution failed');
    } finally {
      setIsExecuting(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      executeAgent();
    }
  };

  const exportConversation = () => {
    const conversationData = {
      agent: agent?.name,
      session: currentSession?.name,
      messages: messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp.toISOString(),
        metadata: msg.metadata,
      })),
      exportedAt: new Date().toISOString(),
    };

    const dataStr = JSON.stringify(conversationData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `conversation-${agentId}-${Date.now()}.json`;
    link.click();
  };

  const clearConversation = () => {
    if (confirm('Are you sure you want to clear this conversation?')) {
      setMessages([]);
      setCurrentSession(null);
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-6 mb-6"
        >
          <div>
            <h1 className="text-4xl font-bold text-white flex items-center gap-3">
              <Play className="w-10 h-10 text-blue-400" />
              Agent Execution
            </h1>
            {agent && (
              <p className="text-gray-400 mt-2">
                Interactive chat with <span className="text-white font-medium">{agent.name}</span>
              </p>
            )}
          </div>
          
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className={`px-4 py-2 rounded-lg transition-all flex items-center gap-2 ${
                showSettings ? 'bg-blue-500/20 text-blue-400' : 'bg-white/10 text-white hover:bg-white/20'
              }`}
            >
              <Settings className="w-4 h-4" />
              Settings
            </button>
            <button
              onClick={exportConversation}
              disabled={messages.length === 0}
              className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-all flex items-center gap-2 disabled:opacity-50"
            >
              <Download className="w-4 h-4" />
              Export
            </button>
            <button
              onClick={createNewSession}
              className="px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white hover:opacity-90 transition-all flex items-center gap-2"
            >
              <MessageSquare className="w-4 h-4" />
              New Session
            </button>
          </div>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Agent Info */}
            {agent && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6"
              >
                <div className="flex items-center gap-3 mb-4">
                  <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg">
                    <Brain className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">{agent.name}</h3>
                    <p className="text-gray-400 text-sm">{agent.model}</p>
                  </div>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-400">Status:</span>
                    <span className={`${
                      agent.status === 'ACTIVE' ? 'text-green-400' : 
                      agent.status === 'DRAFT' ? 'text-blue-400' : 'text-gray-400'
                    }`}>
                      {agent.status}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Temperature:</span>
                    <span className="text-white">{agent.configuration?.temperature || 0.7}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-400">Max Tokens:</span>
                    <span className="text-white">{agent.configuration?.maxTokens || 2048}</span>
                  </div>
                </div>
              </motion.div>
            )}

            {/* Sessions */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6"
            >
              <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                <MessageSquare className="w-4 h-4" />
                Sessions
              </h3>
              
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {sessions.map((session) => (
                  <button
                    key={session.id}
                    onClick={() => loadSession(session.id)}
                    className={`w-full text-left p-3 rounded-lg transition-colors ${
                      currentSession?.id === session.id
                        ? 'bg-blue-500/20 border border-blue-500/40'
                        : 'bg-white/5 hover:bg-white/10'
                    }`}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-white text-sm font-medium truncate">
                          {session.name}
                        </p>
                        <p className="text-gray-400 text-xs">
                          {session.messageCount} messages
                        </p>
                      </div>
                      <span className="text-gray-400 text-xs">
                        {new Date(session.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </button>
                ))}
                
                {sessions.length === 0 && (
                  <p className="text-gray-400 text-sm text-center py-4">
                    No sessions yet
                  </p>
                )}
              </div>
            </motion.div>

            {/* Settings Panel */}
            <AnimatePresence>
              {showSettings && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6"
                >
                  <h3 className="text-white font-semibold mb-4 flex items-center gap-2">
                    <Settings className="w-4 h-4" />
                    Settings
                  </h3>
                  
                  <div className="space-y-4">
                    <label className="flex items-center gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.autoScroll}
                        onChange={(e) => setSettings(prev => ({ ...prev, autoScroll: e.target.checked }))}
                        className="form-checkbox h-4 w-4 text-blue-500"
                      />
                      <span className="text-white text-sm">Auto-scroll to bottom</span>
                    </label>
                    
                    <label className="flex items-center gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.showMetadata}
                        onChange={(e) => setSettings(prev => ({ ...prev, showMetadata: e.target.checked }))}
                        className="form-checkbox h-4 w-4 text-blue-500"
                      />
                      <span className="text-white text-sm">Show metadata</span>
                    </label>
                    
                    <label className="flex items-center gap-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={settings.saveHistory}
                        onChange={(e) => setSettings(prev => ({ ...prev, saveHistory: e.target.checked }))}
                        className="form-checkbox h-4 w-4 text-blue-500"
                      />
                      <span className="text-white text-sm">Save execution history</span>
                    </label>
                  </div>
                  
                  <div className="border-t border-white/10 pt-4 mt-4">
                    <button
                      onClick={clearConversation}
                      className="w-full flex items-center justify-center gap-2 px-3 py-2 bg-red-500/20 text-red-400 rounded-lg hover:bg-red-500/30 transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                      Clear Conversation
                    </button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Chat Interface */}
          <div className="lg:col-span-3">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 flex flex-col h-[calc(100vh-12rem)]"
            >
              {/* Chat Header */}
              <div className="flex items-center justify-between p-6 border-b border-white/10">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-500/20 rounded-lg">
                    <Activity className="w-5 h-5 text-green-400" />
                  </div>
                  <div>
                    <h3 className="text-white font-semibold">
                      {currentSession?.name || 'New Conversation'}
                    </h3>
                    <p className="text-gray-400 text-sm">
                      {messages.length} messages
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-2">
                  <button
                    onClick={() => setMessages([])}
                    disabled={messages.length === 0}
                    className="p-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-colors disabled:opacity-50"
                    title="Clear chat"
                  >
                    <RotateCcw className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Messages */}
              <div className="flex-1 overflow-y-auto p-6 space-y-4">
                {messages.length === 0 ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <MessageSquare className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-xl font-semibold text-white mb-2">
                        Start a conversation
                      </h3>
                      <p className="text-gray-400">
                        Send a message to begin chatting with {agent?.name || 'your agent'}
                      </p>
                    </div>
                  </div>
                ) : (
                  messages.map((message) => (
                    <motion.div
                      key={message.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className={`flex gap-3 ${
                        message.role === 'user' ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      {message.role === 'assistant' && (
                        <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg">
                          <Bot className="w-5 h-5 text-white" />
                        </div>
                      )}
                      
                      <div className={`max-w-2xl ${
                        message.role === 'user' ? 'order-first' : ''
                      }`}>
                        <div className={`p-4 rounded-2xl ${
                          message.role === 'user'
                            ? 'bg-blue-500 text-white ml-auto'
                            : message.metadata?.error
                            ? 'bg-red-500/20 border border-red-500/40 text-red-200'
                            : 'bg-white/10 text-white'
                        }`}>
                          <p className="whitespace-pre-wrap">{message.content}</p>
                          
                          {settings.showMetadata && message.metadata && (
                            <div className="mt-3 pt-3 border-t border-white/20 flex flex-wrap gap-4 text-xs opacity-70">
                              {message.metadata.duration && (
                                <div className="flex items-center gap-1">
                                  <Clock className="w-3 h-3" />
                                  {message.metadata.duration}ms
                                </div>
                              )}
                              {message.metadata.tokens && (
                                <div className="flex items-center gap-1">
                                  <Zap className="w-3 h-3" />
                                  {message.metadata.tokens} tokens
                                </div>
                              )}
                              {message.metadata.model && (
                                <div className="flex items-center gap-1">
                                  <Brain className="w-3 h-3" />
                                  {message.metadata.model}
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                        
                        <div className={`flex items-center gap-2 mt-2 text-xs text-gray-400 ${
                          message.role === 'user' ? 'justify-end' : 'justify-start'
                        }`}>
                          <span>{formatTimestamp(message.timestamp)}</span>
                          <button
                            onClick={() => {
                              navigator.clipboard.writeText(message.content);
                              showSuccess('Message copied to clipboard');
                            }}
                            className="opacity-0 group-hover:opacity-100 hover:text-white transition-all"
                          >
                            <Copy className="w-3 h-3" />
                          </button>
                        </div>
                      </div>
                      
                      {message.role === 'user' && (
                        <div className="p-2 bg-blue-500/20 rounded-lg">
                          <User className="w-5 h-5 text-blue-400" />
                        </div>
                      )}
                    </motion.div>
                  ))
                )}
                
                <div ref={messagesEndRef} />
              </div>

              {/* Input */}
              <div className="p-6 border-t border-white/10">
                <div className="flex gap-3">
                  <div className="flex-1">
                    <textarea
                      value={currentInput}
                      onChange={(e) => setCurrentInput(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type your message here... (Press Enter to send, Shift+Enter for new line)"
                      rows={3}
                      className="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40 resize-none"
                      disabled={isExecuting}
                    />
                  </div>
                  
                  <button
                    onClick={executeAgent}
                    disabled={!currentInput.trim() || isExecuting || !agent}
                    className="px-6 py-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white hover:opacity-90 transition-all disabled:opacity-50 flex items-center gap-2"
                  >
                    {isExecuting ? (
                      <>
                        <Loader2 className="w-5 h-5 animate-spin" />
                        Processing...
                      </>
                    ) : (
                      <>
                        <Send className="w-5 h-5" />
                        Send
                      </>
                    )}
                  </button>
                </div>
                
                <div className="flex items-center justify-between mt-3 text-xs text-gray-400">
                  <span>
                    {agent ? `Ready to chat with ${agent.name}` : 'Loading agent...'}
                  </span>
                  <span>
                    {currentInput.length} characters
                  </span>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}