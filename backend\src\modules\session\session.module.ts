import { Module } from '@nestjs/common';
import { RedisModule } from '../redis/redis.module';
import { PrismaModule } from '../prisma/prisma.module';
import { SessionService } from './services/session.service';
import { SessionController } from './controllers/session.controller';
import { SessionMemoryService } from './services/session-memory.service';
import { SessionCleanupService } from './services/session-cleanup.service';
import { SessionAnalyticsService } from './services/session-analytics.service';

/**
 * Session Management Module
 * 
 * Production-ready session management with:
 * - Redis-based storage for high performance
 * - Cross-module session sharing
 * - Memory management and intelligent truncation
 * - Session analytics and monitoring
 * - Real-time synchronization across instances
 */
@Module({
  imports: [
    RedisModule,
    PrismaModule,
  ],
  controllers: [SessionController],
  providers: [
    SessionService,
    SessionMemoryService,
    SessionCleanupService,
    SessionAnalyticsService,
  ],
  exports: [
    SessionService,
    SessionMemoryService,
  ],
})
export class SessionModule {} 