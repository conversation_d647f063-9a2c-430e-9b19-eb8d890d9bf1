import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { AgentService } from './agent.service';
import { Prisma } from '.prisma/client';
import { v4 as uuidv4 } from 'uuid';

/**
 * Agent Versioning Service
 * 
 * Manages agent versions:
 * - Version creation and tracking
 * - Rollback to previous versions
 * - A/B testing between versions
 * - Version comparison and analytics
 */
@Injectable()
export class AgentVersioningService {
  private readonly logger = new Logger(AgentVersioningService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly agentService: AgentService,
  ) {}

  /**
   * Create a new version of an agent
   */
  async createVersion(
    agentId: string,
    userId: string,
    organizationId: string,
    data: {
      name?: string;
      description?: string;
      configuration?: Record<string, any>;
      version?: string;
    },
  ): Promise<any> {
    try {
      // Get original agent
      const original = await this.agentService.getAgent(agentId, userId, organizationId);
      if (!original) {
        throw new NotFoundException('Agent not found');
      }

      // Generate new version number if not provided
      let version = data.version;
      if (!version) {
        const [major, minor, patch] = original.version.split('.').map(Number);
        version = `${major}.${minor}.${(patch || 0) + 1}`;
      }

      // Create new agent version
      const newAgentId = `agt_${uuidv4()}`;
      
      // Create a custom field to store version relationship
      const customMetadata = {
        parentAgentId: agentId,
        isVersion: true,
        versionInfo: {
          originalAgentId: agentId,
          versionNumber: version,
          createdAt: new Date(),
        }
      };
      
      const newAgent = await this.prismaService.agent.create({
        data: {
          id: newAgentId,
          name: data.name || original.name,
          description: data.description || original.description,
          organizationId,
          createdById: userId,
          templateId: original.templateId,
          config: (data.configuration || original.configuration) as unknown as Prisma.InputJsonValue,
          model: data.configuration?.model || original.model,
          temperature: data.configuration?.temperature || original.temperature,
          maxTokens: data.configuration?.maxTokens || original.maxTokens,
          tools: data.configuration?.tools || original.tools,
          prompt: data.configuration?.systemPrompt || original.prompt,
          capabilities: original.capabilities as unknown as Prisma.InputJsonValue,
          performance: {} as Prisma.InputJsonValue,
          analytics: {} as Prisma.InputJsonValue,
          version,
          status: 'DRAFT',
        },
      });

      // Store version relationship in a separate table
      await this.prismaService.$executeRaw`
        INSERT INTO "agent_versions" (
          "childAgentId", "parentAgentId", "version", "createdById", "createdAt"
        ) VALUES (
          ${newAgentId}, ${agentId}, ${version}, ${userId}, ${new Date()}
        )
      `;

      this.logger.log(`Agent version created: ${newAgentId} (version ${version})`);

      return this.agentService.getAgent(newAgentId, userId, organizationId);
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to create agent version: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * Get agent version history
   */
  async getVersionHistory(
    agentId: string,
    userId: string,
    organizationId: string,
  ): Promise<any[]> {
    try {
      // Get agent
      const agent = await this.agentService.getAgent(agentId, userId, organizationId);
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      // Get version history using the agent_versions table
      const versionsResult = await this.prismaService.$queryRaw`
        WITH RECURSIVE version_tree AS (
          SELECT a.id, a.name, a.version, a.status, a.created_at as "createdAt",
                 u.id as "createdById", u.email, u.first_name as "firstName", u.last_name as "lastName"
          FROM agents a
          LEFT JOIN users u ON a.created_by_id = u.id
          WHERE a.id = ${agentId} AND a.organization_id = ${organizationId} AND a.deleted_at IS NULL
          
          UNION ALL
          
          SELECT a.id, a.name, a.version, a.status, a.created_at as "createdAt",
                 u.id as "createdById", u.email, u.first_name as "firstName", u.last_name as "lastName"
          FROM agents a
          JOIN agent_versions av ON a.id = av.child_agent_id
          JOIN version_tree vt ON av.parent_agent_id = vt.id
          LEFT JOIN users u ON a.created_by_id = u.id
          WHERE a.organization_id = ${organizationId} AND a.deleted_at IS NULL
        )
        SELECT id, name, version, status, "createdAt",
               json_build_object('id', "createdById", 'email', email, 'firstName', "firstName", 'lastName', "lastName") as "createdBy"
        FROM version_tree
        ORDER BY "createdAt" DESC
      `;
      
      // Ensure we have a proper array result
      const versions = Array.isArray(versionsResult) ? versionsResult : [];

      return versions;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to get agent version history: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * Compare two agent versions
   */
  async compareVersions(
    version1Id: string,
    version2Id: string,
    userId: string,
    organizationId: string,
  ): Promise<any> {
    try {
      // Get both versions
      const version1 = await this.agentService.getAgent(version1Id, userId, organizationId);
      if (!version1) {
        throw new NotFoundException('Version 1 not found');
      }

      const version2 = await this.agentService.getAgent(version2Id, userId, organizationId);
      if (!version2) {
        throw new NotFoundException('Version 2 not found');
      }

      // Compare configurations
      const configDiff = this.compareObjects(version1.configuration, version2.configuration);

      // Compare performance metrics
      const performanceDiff = this.compareObjects(
        version1.performance || {},
        version2.performance || {},
      );

      // Get execution stats for both versions
      const version1Stats = await this.getVersionStats(version1Id);
      const version2Stats = await this.getVersionStats(version2Id);

      return {
        version1: {
          id: version1.id,
          name: version1.name,
          version: version1.version,
          createdAt: version1.createdAt,
          stats: version1Stats,
        },
        version2: {
          id: version2.id,
          name: version2.name,
          version: version2.version,
          createdAt: version2.createdAt,
          stats: version2Stats,
        },
        configDiff,
        performanceDiff,
      };
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to compare agent versions: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * Rollback to a previous version
   */
  async rollback(
    agentId: string,
    targetVersionId: string,
    userId: string,
    organizationId: string,
  ): Promise<any> {
    try {
      // Get current agent
      const currentAgent = await this.agentService.getAgent(agentId, userId, organizationId);
      if (!currentAgent) {
        throw new NotFoundException('Agent not found');
      }

      // Get target version
      const targetVersion = await this.agentService.getAgent(targetVersionId, userId, organizationId);
      if (!targetVersion) {
        throw new NotFoundException('Target version not found');
      }

      // Verify target version is related to current agent using agent_versions table
      const isRelated = await this.prismaService.$queryRaw`
        WITH RECURSIVE version_tree AS (
          SELECT parent_agent_id, child_agent_id
          FROM agent_versions
          WHERE parent_agent_id = ${agentId}
          
          UNION ALL
          
          SELECT av.parent_agent_id, av.child_agent_id
          FROM agent_versions av
          JOIN version_tree vt ON av.parent_agent_id = vt.child_agent_id
        )
        SELECT COUNT(*) > 0 as is_related
        FROM version_tree
        WHERE child_agent_id = ${targetVersionId}
        
        UNION
        
        SELECT ${agentId} = ${targetVersionId} as is_related
      `;

      const related = Array.isArray(isRelated) && isRelated.length > 0 && isRelated[0].is_related;
      if (!related) {
        throw new BadRequestException('Target version is not related to this agent');
      }

      // Create a new version based on the target version
      const newVersion = await this.createVersion(agentId, userId, organizationId, {
        name: currentAgent.name,
        description: currentAgent.description || undefined,
        configuration: targetVersion.configuration,
      });

      // Activate the new version
      await this.agentService.activateAgent(newVersion.id, userId, organizationId);

      // Update original agent status to inactive
      await this.agentService.updateAgent(agentId, userId, organizationId, {
        status: 'INACTIVE',
      });

      this.logger.log(`Agent rolled back: ${agentId} to version ${targetVersionId}`);

      return newVersion;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to rollback agent: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * Start A/B testing between two agent versions
   */
  async startAbTesting(
    version1Id: string,
    version2Id: string,
    userId: string,
    organizationId: string,
    options: {
      name: string;
      description?: string;
      distribution?: number; // Percentage of traffic to version 1 (0-100)
      duration?: number; // Duration in days
    },
  ): Promise<any> {
    try {
      // Get both versions
      const version1 = await this.agentService.getAgent(version1Id, userId, organizationId);
      if (!version1) {
        throw new NotFoundException('Version 1 not found');
      }

      const version2 = await this.agentService.getAgent(version2Id, userId, organizationId);
      if (!version2) {
        throw new NotFoundException('Version 2 not found');
      }

      // Verify both versions are active
      if (version1.status !== 'ACTIVE' || version2.status !== 'ACTIVE') {
        throw new BadRequestException('Both versions must be active to start A/B testing');
      }

      // Calculate end date
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + (options.duration || 7)); // Default 7 days

      // Create A/B test record
      const abTestId = `abtest_${uuidv4()}`;
      const abTestResult = await this.prismaService.$queryRaw`
        INSERT INTO "ab_tests" (
          "id", "name", "description", "version1Id", "version2Id", 
          "distribution", "startDate", "endDate", "status", 
          "organizationId", "createdById", "createdAt", "updatedAt"
        ) VALUES (
          ${abTestId}, ${options.name}, ${options.description || ''}, 
          ${version1Id}, ${version2Id}, ${options.distribution || 50}, 
          ${startDate}, ${endDate}, 'ACTIVE', 
          ${organizationId}, ${userId}, ${startDate}, ${startDate}
        ) RETURNING *;
      `;

      this.logger.log(`A/B test started: ${abTestId} between ${version1Id} and ${version2Id}`);

      // Ensure we handle the raw query result properly
      const abTest = Array.isArray(abTestResult) ? abTestResult[0] : abTestResult;

      return abTest;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to start A/B testing: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * Get A/B test results
   */
  async getAbTestResults(
    abTestId: string,
    userId: string,
    organizationId: string,
  ): Promise<any> {
    try {
      // Get A/B test
      const abTestResult = await this.prismaService.$queryRaw`
        SELECT * FROM "ab_tests" 
        WHERE "id" = ${abTestId} AND "organizationId" = ${organizationId};
      `;

      if (!abTestResult || (Array.isArray(abTestResult) && abTestResult.length === 0)) {
        throw new NotFoundException('A/B test not found');
      }

      // Ensure we handle the raw query result properly
      const abTest = Array.isArray(abTestResult) ? abTestResult[0] : abTestResult;

      // Get version stats
      const version1Stats = await this.getVersionStats(abTest.version1Id);
      const version2Stats = await this.getVersionStats(abTest.version2Id);

      // Calculate winner based on success rate
      let winner = null;
      if (version1Stats.successRate > version2Stats.successRate) {
        winner = {
          versionId: abTest.version1Id,
          stats: version1Stats,
          improvement: ((version1Stats.successRate - version2Stats.successRate) / version2Stats.successRate) * 100,
        };
      } else if (version2Stats.successRate > version1Stats.successRate) {
        winner = {
          versionId: abTest.version2Id,
          stats: version2Stats,
          improvement: ((version2Stats.successRate - version1Stats.successRate) / version1Stats.successRate) * 100,
        };
      }

      return {
        id: abTest.id,
        name: abTest.name,
        description: abTest.description,
        startDate: abTest.startDate,
        endDate: abTest.endDate,
        status: abTest.status,
        version1: {
          id: abTest.version1Id,
          stats: version1Stats,
        },
        version2: {
          id: abTest.version2Id,
          stats: version2Stats,
        },
        winner,
      };
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to get A/B test results: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * Get version statistics
   */
  private async getVersionStats(versionId: string): Promise<any> {
    try {
      // Get execution stats
      const executionStats = await this.prismaService.$queryRaw<{ count: number, avg_duration: number }[]>`
        SELECT 
          COUNT(*) as count,
          AVG(duration) as avg_duration
        FROM "agent_executions"
        WHERE "agentId" = ${versionId}
      `;
      
      const successCount = await this.prismaService.$queryRaw<{ count: number }[]>`
        SELECT COUNT(*) as count
        FROM "agent_executions"
        WHERE "agentId" = ${versionId} AND "status" = 'COMPLETED'
      `;
      
      const count = executionStats[0]?.count || 0;
      
      return {
        executionCount: count,
        successRate: count > 0 ? ((successCount[0]?.count || 0) / count) * 100 : 0,
        avgResponseTime: executionStats[0]?.avg_duration || 0,
      };
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to get version stats: ${err.message}`, err.stack);
      return {
        executionCount: 0,
        successRate: 0,
        avgResponseTime: 0,
      };
    }
  }

  /**
   * Compare two objects and return differences
   */
  private compareObjects(obj1: any, obj2: any): any {
    const result: any = {};
    
    // Get all keys from both objects
    const allKeys = [...new Set([...Object.keys(obj1 || {}), ...Object.keys(obj2 || {})])];
    
    for (const key of allKeys) {
      // If key exists in both objects
      if (obj1?.hasOwnProperty(key) && obj2?.hasOwnProperty(key)) {
        // If values are different
        if (JSON.stringify(obj1[key]) !== JSON.stringify(obj2[key])) {
          result[key] = {
            version1: obj1[key],
            version2: obj2[key],
          };
        }
      }
      // If key exists only in obj1
      else if (obj1?.hasOwnProperty(key)) {
        result[key] = {
          version1: obj1[key],
          version2: undefined,
        };
      }
      // If key exists only in obj2
      else if (obj2?.hasOwnProperty(key)) {
        result[key] = {
          version1: undefined,
          version2: obj2[key],
        };
      }
    }
    
    return result;
  }
} 