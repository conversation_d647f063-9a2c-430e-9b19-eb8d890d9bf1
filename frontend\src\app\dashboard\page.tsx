'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON>, 
  Wrench, 
  Workflow, 
  BookOpen, 
  BarChart3, 
  <PERSON>lette, 
  Plug, 
  Bell, 
  Settings, 
  CreditCard,
  Store,
  Users,
  Plus,
  ChevronRight,
  Zap,
  TrendingUp,
  Activity
} from 'lucide-react';
import { showSuccess, showInfo } from '@/lib/toast';

/**
 * Revolutionary Dashboard
 * 
 * A billion-dollar UI that makes AI accessible to everyone
 * through intuitive, click-based interactions
 */
export default function DashboardPage() {
  const [activeModule, setActiveModule] = useState<string | null>(null);
  const [stats, setStats] = useState({
    agents: 0,
    tools: 0,
    workflows: 0,
    executions: 0
  });

  useEffect(() => {
    // Simulate real-time stats
    const interval = setInterval(() => {
      setStats(prev => ({
        agents: prev.agents + Math.floor(Math.random() * 2),
        tools: prev.tools + Math.floor(Math.random() * 3),
        workflows: prev.workflows + Math.floor(Math.random() * 2),
        executions: prev.executions + Math.floor(Math.random() * 10)
      }));
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  const modules = [
    {
      id: 'agents',
      title: 'AI Agents',
      description: 'Create intelligent agents with no coding',
      icon: Brain,
      color: 'from-purple-500 to-pink-500',
      stats: stats.agents,
      action: 'Create Agent'
    },
    {
      id: 'tools',
      title: 'Tool Builder',
      description: 'Connect any API with visual builder',
      icon: Wrench,
      color: 'from-blue-500 to-cyan-500',
      stats: stats.tools,
      action: 'Build Tool'
    },
    {
      id: 'workflows',
      title: 'Workflow Designer',
      description: 'Design complex workflows visually',
      icon: Workflow,
      color: 'from-green-500 to-emerald-500',
      stats: stats.workflows,
      action: 'New Workflow'
    },
    {
      id: 'knowledge',
      title: 'Knowledge Base',
      description: 'Manage documents with AI insights',
      icon: BookOpen,
      color: 'from-orange-500 to-red-500',
      stats: '2.5TB',
      action: 'Add Knowledge'
    },
    {
      id: 'analytics',
      title: 'Analytics Hub',
      description: 'Real-time insights and predictions',
      icon: BarChart3,
      color: 'from-indigo-500 to-purple-500',
      stats: stats.executions,
      action: 'View Analytics'
    },
    {
      id: 'widgets',
      title: 'Widget Studio',
      description: 'Create embeddable AI widgets',
      icon: Palette,
      color: 'from-pink-500 to-rose-500',
      stats: '50+',
      action: 'Design Widget'
    }
  ];

  const quickActions = [
    { icon: Plus, label: 'Quick Agent', action: () => showSuccess('Agent creation started!') },
    { icon: Zap, label: 'Test Tool', action: () => showInfo('Tool testing mode activated') },
    { icon: Activity, label: 'Live Monitor', action: () => showInfo('Monitoring dashboard opened') },
    { icon: TrendingUp, label: 'AI Insights', action: () => showSuccess('Generating insights...') }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden">
        <div className="absolute -inset-[10px] opacity-20">
          {[...Array(20)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute h-32 w-32 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 blur-3xl"
              animate={{
                x: [0, 100, 0],
                y: [0, -100, 0],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 10 + i * 2,
                repeat: Infinity,
                repeatType: 'reverse',
              }}
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
            />
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 p-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-12 text-center"
        >
          <h1 className="mb-4 text-6xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
            Welcome to SynapseAI
          </h1>
          <p className="text-xl text-gray-300">
            The future of AI development - No code, just clicks
          </p>
        </motion.div>

        {/* Quick Actions Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-12 flex justify-center gap-4"
        >
          {quickActions.map((action, index) => (
            <motion.button
              key={index}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={action.action}
              className="flex items-center gap-2 px-6 py-3 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 text-white hover:bg-white/20 transition-all"
            >
              <action.icon className="w-5 h-5" />
              <span>{action.label}</span>
            </motion.button>
          ))}
        </motion.div>

        {/* Module Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-7xl mx-auto">
          <AnimatePresence>
            {modules.map((module, index) => (
              <motion.div
                key={module.id}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ delay: index * 0.1 }}
                whileHover={{ y: -5 }}
                onClick={() => setActiveModule(module.id)}
                className="relative cursor-pointer"
              >
                <div className="p-6 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20 hover:border-white/40 transition-all">
                  {/* Gradient Background */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${module.color} opacity-20 rounded-2xl`} />
                  
                  {/* Content */}
                  <div className="relative z-10">
                    <div className="flex items-start justify-between mb-4">
                      <div className={`p-3 bg-gradient-to-br ${module.color} rounded-xl`}>
                        <module.icon className="w-6 h-6 text-white" />
                      </div>
                      <div className="text-2xl font-bold text-white">
                        {module.stats}
                      </div>
                    </div>
                    
                    <h3 className="text-xl font-semibold text-white mb-2">
                      {module.title}
                    </h3>
                    <p className="text-gray-300 mb-4">
                      {module.description}
                    </p>
                    
                    <motion.button
                      whileHover={{ x: 5 }}
                      className="flex items-center gap-2 text-white font-medium"
                    >
                      {module.action}
                      <ChevronRight className="w-4 h-4" />
                    </motion.button>
                  </div>
                </div>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* Live Activity Feed */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mt-12 max-w-7xl mx-auto"
        >
          <div className="p-6 bg-white/10 backdrop-blur-md rounded-2xl border border-white/20">
            <h2 className="text-2xl font-semibold text-white mb-4 flex items-center gap-2">
              <Sparkles className="w-6 h-6 text-yellow-400" />
              Live Platform Activity
            </h2>
            <div className="space-y-3">
              {[
                { action: 'Agent Created', user: 'Sarah M.', time: 'Just now', type: 'agent' },
                { action: 'Workflow Executed', user: 'John D.', time: '2 mins ago', type: 'workflow' },
                { action: 'Tool Published', user: 'Emma K.', time: '5 mins ago', type: 'tool' },
                { action: 'Widget Deployed', user: 'Alex R.', time: '8 mins ago', type: 'widget' }
              ].map((activity, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.9 + index * 0.1 }}
                  className="flex items-center justify-between p-3 bg-white/5 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                    <span className="text-white font-medium">{activity.action}</span>
                    <span className="text-gray-400">by {activity.user}</span>
                  </div>
                  <span className="text-gray-500 text-sm">{activity.time}</span>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
} 