import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { Prisma, TemplateStatus } from '.prisma/client';
import { v4 as uuidv4 } from 'uuid';

export interface CreateTemplateDto {
  name: string;
  description?: string;
  content: Record<string, any>;
  schema?: Record<string, any>;
  variables?: Array<{
    name: string;
    type: string;
    description?: string;
    required?: boolean;
    default?: any;
  }>;
  isPublic?: boolean;
  category?: string;
  tags?: string[];
  parentId?: string;
}

export interface UpdateTemplateDto {
  name?: string;
  description?: string;
  content?: Record<string, any>;
  schema?: Record<string, any>;
  variables?: Array<{
    name: string;
    type: string;
    description?: string;
    required?: boolean;
    default?: any;
  }>;
  isPublic?: boolean;
  category?: string;
  tags?: string[];
  status?: TemplateStatus;
}

/**
 * Agent Template Service
 * 
 * Manages agent templates:
 * - Template creation and management
 * - Template versioning and inheritance
 * - Variable injection and validation
 * - Template marketplace and sharing
 */
@Injectable()
export class AgentTemplateService {
  private readonly logger = new Logger(AgentTemplateService.name);

  constructor(private readonly prismaService: PrismaService) {}

  /**
   * Create a new template
   */
  async createTemplate(
    userId: string,
    organizationId: string,
    data: CreateTemplateDto,
  ): Promise<any> {
    try {
      // Generate template ID
      const templateId = `tmpl_${uuidv4()}`;

      // If parentId is provided, verify it exists and user has access
      if (data.parentId) {
        const parentTemplate = await this.prismaService.template.findFirst({
          where: {
            id: data.parentId,
            OR: [
              { organizationId },
              { isPublic: true },
            ],
          },
        });

        if (!parentTemplate) {
          throw new NotFoundException('Parent template not found or access denied');
        }
      }

      // Create template
      const template = await this.prismaService.template.create({
        data: {
          id: templateId,
          name: data.name,
          description: data.description,
          organizationId,
          createdById: userId,
          content: data.content as unknown as Prisma.InputJsonValue,
          schema: data.schema as unknown as Prisma.InputJsonValue || {},
          variables: data.variables as unknown as Prisma.InputJsonValue || [],
          isPublic: data.isPublic || false,
          category: data.category,
          tags: data.tags || [],
          parentId: data.parentId,
          status: 'DRAFT',
          version: '1.0.0',
          performance: {} as Prisma.InputJsonValue,
        },
      });

      this.logger.log(`Template created: ${templateId} by user: ${userId}`);

      return template;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to create template: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * Get template by ID
   */
  async getTemplate(
    templateId: string,
    userId: string,
    organizationId: string,
  ): Promise<any> {
    try {
      const template = await this.prismaService.template.findFirst({
        where: {
          id: templateId,
          OR: [
            { organizationId },
            { isPublic: true },
          ],
          deletedAt: null,
        },
        include: {
          parent: {
            select: {
              id: true,
              name: true,
              version: true,
            },
          },
          versions: {
            select: {
              id: true,
              version: true,
              createdAt: true,
            },
            orderBy: {
              createdAt: 'desc',
            },
          },
          createdBy: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      if (!template) {
        throw new NotFoundException('Template not found');
      }

      return template;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to get template: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * Update template
   */
  async updateTemplate(
    templateId: string,
    userId: string,
    organizationId: string,
    data: UpdateTemplateDto,
  ): Promise<any> {
    try {
      // Check if template exists and user has access
      const template = await this.prismaService.template.findFirst({
        where: {
          id: templateId,
          organizationId,
          deletedAt: null,
        },
      });

      if (!template) {
        throw new NotFoundException('Template not found');
      }

      // Update template
      const updated = await this.prismaService.template.update({
        where: { id: templateId },
        data: {
          ...(data.name !== undefined && { name: data.name }),
          ...(data.description !== undefined && { description: data.description }),
          ...(data.content !== undefined && { content: data.content as unknown as Prisma.InputJsonValue }),
          ...(data.schema !== undefined && { schema: data.schema as unknown as Prisma.InputJsonValue }),
          ...(data.variables !== undefined && { variables: data.variables as unknown as Prisma.InputJsonValue }),
          ...(data.isPublic !== undefined && { isPublic: data.isPublic }),
          ...(data.category !== undefined && { category: data.category }),
          ...(data.tags !== undefined && { tags: data.tags }),
          ...(data.status !== undefined && { status: data.status }),
          updatedAt: new Date(),
        },
      });

      this.logger.log(`Template updated: ${templateId}`);

      return updated;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to update template: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * Delete template (soft delete)
   */
  async deleteTemplate(
    templateId: string,
    userId: string,
    organizationId: string,
  ): Promise<void> {
    try {
      // Check if template exists and user has access
      const template = await this.prismaService.template.findFirst({
        where: {
          id: templateId,
          organizationId,
          deletedAt: null,
        },
      });

      if (!template) {
        throw new NotFoundException('Template not found');
      }

      // Check if template is used by any agents
      const agentsUsingTemplate = await this.prismaService.agent.count({
        where: {
          templateId,
          deletedAt: null,
        },
      });

      if (agentsUsingTemplate > 0) {
        throw new BadRequestException(`Template is used by ${agentsUsingTemplate} agents and cannot be deleted`);
      }

      // Soft delete
      await this.prismaService.template.update({
        where: { id: templateId },
        data: {
          deletedAt: new Date(),
          status: 'ARCHIVED',
        },
      });

      this.logger.log(`Template deleted: ${templateId}`);
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to delete template: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * List templates for organization
   */
  async listTemplates(
    organizationId: string,
    options: {
      status?: TemplateStatus;
      category?: string;
      search?: string;
      includePublic?: boolean;
      limit?: number;
      offset?: number;
      orderBy?: 'createdAt' | 'updatedAt' | 'name' | 'downloads';
      order?: 'asc' | 'desc';
    } = {},
  ): Promise<{ templates: any[]; total: number }> {
    try {
      const where: Prisma.TemplateWhereInput = {
        OR: [
          { organizationId },
          ...(options.includePublic ? [{ isPublic: true }] : []),
        ],
        deletedAt: null,
        ...(options.status && { status: options.status }),
        ...(options.category && { category: options.category }),
        ...(options.search && {
          OR: [
            { name: { contains: options.search, mode: 'insensitive' } },
            { description: { contains: options.search, mode: 'insensitive' } },
          ],
        }),
      };

      // Get total count
      const total = await this.prismaService.template.count({ where });

      // Get templates
      const templates = await this.prismaService.template.findMany({
        where,
        take: options.limit || 20,
        skip: options.offset || 0,
        orderBy: {
          [options.orderBy || 'createdAt']: options.order || 'desc',
        },
        include: {
          createdBy: {
            select: {
              id: true,
              email: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      return {
        templates,
        total,
      };
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to list templates: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * Create a new version of a template
   */
  async createVersion(
    templateId: string,
    userId: string,
    organizationId: string,
    data: {
      content?: Record<string, any>;
      schema?: Record<string, any>;
      variables?: Array<any>;
      version?: string;
    },
  ): Promise<any> {
    try {
      // Get parent template
      const parentTemplate = await this.getTemplate(templateId, userId, organizationId);

      // Generate new version number if not provided
      let version = data.version;
      if (!version) {
        const [major, minor, patch] = parentTemplate.version.split('.').map(Number);
        version = `${major}.${minor}.${patch + 1}`;
      }

      // Create new version
      const newVersionData: CreateTemplateDto = {
        name: parentTemplate.name,
        description: parentTemplate.description,
        content: data.content || parentTemplate.content,
        schema: data.schema || parentTemplate.schema,
        variables: data.variables || parentTemplate.variables,
        isPublic: parentTemplate.isPublic,
        category: parentTemplate.category,
        tags: parentTemplate.tags,
        parentId: templateId,
      };

      const newVersion = await this.createTemplate(userId, organizationId, newVersionData);

      // Update version number
      await this.prismaService.template.update({
        where: { id: newVersion.id },
        data: {
          version,
        },
      });

      this.logger.log(`Template version created: ${newVersion.id} (version ${version})`);

      return this.getTemplate(newVersion.id, userId, organizationId);
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to create template version: ${err.message}`, err.stack);
      throw error;
    }
  }

  /**
   * Apply template to create an agent
   */
  async applyTemplate(
    templateId: string,
    userId: string,
    organizationId: string,
    data: {
      name: string;
      description?: string;
      variables?: Record<string, any>;
    },
  ): Promise<any> {
    try {
      // Get template
      const template = await this.getTemplate(templateId, userId, organizationId);

      // Validate variables against template schema
      const templateVariables = template.variables || [];
      const providedVariables = data.variables || {};

      // Check for required variables
      for (const variable of templateVariables) {
        if (variable.required && !providedVariables.hasOwnProperty(variable.name)) {
          throw new BadRequestException(`Missing required variable: ${variable.name}`);
        }
      }

      // Apply template variables to content
      let content = JSON.stringify(template.content);
      for (const [key, value] of Object.entries(providedVariables)) {
        const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
        content = content.replace(regex, String(value));
      }

      // Parse content back to object
      const agentConfig = JSON.parse(content);

      // Return agent configuration
      return {
        name: data.name,
        description: data.description || template.description,
        templateId: template.id,
        configuration: agentConfig,
        metadata: {
          appliedFrom: {
            templateId: template.id,
            templateName: template.name,
            templateVersion: template.version,
          },
          variables: providedVariables,
        },
      };
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to apply template: ${err.message}`, err.stack);
      throw error;
    }
  }
} 