# Development Dockerfile for NestJS Backend
FROM node:18-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    curl \
    git \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Copy package files for dependency installation
COPY package*.json ./
COPY backend/package*.json ./backend/
COPY shared/package*.json ./shared/

# Install dependencies with npm ci for faster builds
RUN npm ci --only=production --silent && \
    cd backend && npm ci --silent && \
    cd ../shared && npm ci --silent

# Development stage
FROM base AS development

# Install development dependencies
RUN cd backend && npm ci --silent && \
    cd ../shared && npm ci --silent

# Copy source code
COPY backend/ ./backend/
COPY shared/ ./shared/

# Copy TypeScript configurations
COPY tsconfig*.json ./
COPY backend/tsconfig*.json ./backend/
COPY shared/tsconfig*.json ./shared/

# Set working directory to backend
WORKDIR /app/backend

# Generate Prisma client
RUN npx prisma generate

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001 -G nodejs

# Change ownership of the app directory
RUN chown -R nestjs:nodejs /app

# Switch to non-root user
USER nestjs

# Expose ports
EXPOSE 3001 9229

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Start the application in development mode with debugging
CMD ["npm", "run", "start:debug"] 