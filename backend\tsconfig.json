{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "noEmit": false, "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "noImplicitAny": true, "strictBindCallApply": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "strict": true, "noImplicitReturns": true, "noUncheckedIndexedAccess": true, "composite": true, "paths": {"@/*": ["src/*"], "@shared/*": ["../shared/src/*"], "@synapseai/shared/*": ["../shared/src/*"], "@config/*": ["src/config/*"], "@modules/*": ["src/modules/*"], "@common/*": ["src/common/*"], "@utils/*": ["src/utils/*"]}, "resolveJsonModule": true, "esModuleInterop": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*.spec.ts", "**/*.test.ts"], "references": [{"path": "../shared"}]}