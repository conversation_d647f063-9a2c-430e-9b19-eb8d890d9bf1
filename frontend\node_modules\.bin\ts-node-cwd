#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._8694e8f6033e46b4cf63dba2e64c0ad7/node_modules/ts-node/dist/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._8694e8f6033e46b4cf63dba2e64c0ad7/node_modules/ts-node/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._8694e8f6033e46b4cf63dba2e64c0ad7/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._8694e8f6033e46b4cf63dba2e64c0ad7/node_modules/ts-node/dist/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._8694e8f6033e46b4cf63dba2e64c0ad7/node_modules/ts-node/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._8694e8f6033e46b4cf63dba2e64c0ad7/node_modules:/mnt/c/laragon/www/max/cursorpro/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._8694e8f6033e46b4cf63dba2e64c0ad7/node_modules/ts-node/dist/bin-cwd.js" "$@"
else
  exec node  "$basedir/../../../node_modules/.pnpm/ts-node@10.9.2_@swc+core@1._8694e8f6033e46b4cf63dba2e64c0ad7/node_modules/ts-node/dist/bin-cwd.js" "$@"
fi
