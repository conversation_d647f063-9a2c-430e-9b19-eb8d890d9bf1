import { Global, Module } from '@nestjs/common';
import { PrismaService } from './prisma.service';

/**
 * Prisma Database Module
 * 
 * Provides global database connectivity using Prisma ORM.
 * This module is marked as @Global to make PrismaService 
 * available throughout the application without explicit imports.
 */
@Global()
@Module({
  providers: [PrismaService],
  exports: [PrismaService],
})
export class PrismaModule {} 