'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  Wrench, 
  ArrowLeft, 
  Edit, 
  Play, 
  Trash2,
  Check,
  X,
  Server,
  Code,
  Database,
  Cloud,
  MessageSquare,
  Loader2,
  AlertCircle,
  Clock,
  BarChart2,
  FileJson,
  Globe
} from 'lucide-react';
import { useToast } from '@/lib/usetoast';
import axios from 'axios';

interface Tool {
  id: string;
  name: string;
  description: string;
  status: string;
  type: string;
  createdAt: string;
  updatedAt: string;
  createdById: string;
  endpoint?: string;
  method?: string;
  configuration: Record<string, any>;
  executionCount?: number;
  successRate?: number;
  avgResponseTime?: number;
}

export default function ToolDetailPage() {
  const params = useParams();
  const toolId = params.id as string;
  const router = useRouter();
  const { showSuccess, showError } = useToast();
  
  const [tool, setTool] = useState<Tool | null>(null);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState(false);
  const [activating, setActivating] = useState(false);
  const [deactivating, setDeactivating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  
  // Fetch tool data
  useEffect(() => {
    fetchTool();
  }, []);
  
  // Fetch tool data from API
  const fetchTool = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get(`/api/v1/tools/${toolId}`);
      setTool(response.data);
    } catch (err: any) {
      console.error('Error fetching tool:', err);
      setError(err.response?.data?.message || 'Failed to load tool');
      showError('Failed to load tool');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle tool deletion
  const handleDeleteTool = async () => {
    try {
      setDeleting(true);
      
      await axios.delete(`/api/v1/tools/${toolId}`);
      
      showSuccess('Tool deleted successfully');
      router.push('/dashboard/tools');
    } catch (err: any) {
      console.error('Error deleting tool:', err);
      setError(err.response?.data?.message || 'Failed to delete tool');
      showError('Failed to delete tool');
      setDeleting(false);
    }
  };
  
  // Handle tool activation
  const handleActivateTool = async () => {
    try {
      setActivating(true);
      
      await axios.put(`/api/v1/tools/${toolId}/activate`);
      
      showSuccess('Tool activated successfully');
      fetchTool();
    } catch (err: any) {
      console.error('Error activating tool:', err);
      setError(err.response?.data?.message || 'Failed to activate tool');
      showError('Failed to activate tool');
    } finally {
      setActivating(false);
    }
  };
  
  // Handle tool deactivation
  const handleDeactivateTool = async () => {
    try {
      setDeactivating(true);
      
      await axios.put(`/api/v1/tools/${toolId}/deactivate`);
      
      showSuccess('Tool deactivated successfully');
      fetchTool();
    } catch (err: any) {
      console.error('Error deactivating tool:', err);
      setError(err.response?.data?.message || 'Failed to deactivate tool');
      showError('Failed to deactivate tool');
    } finally {
      setDeactivating(false);
    }
  };
  
  // Get tool type icon
  const getToolTypeIcon = (type: string) => {
    switch (type) {
      case 'API':
        return <Server className="w-5 h-5" />;
      case 'FUNCTION':
        return <Code className="w-5 h-5" />;
      case 'DATABASE':
        return <Database className="w-5 h-5" />;
      case 'STORAGE':
        return <Cloud className="w-5 h-5" />;
      case 'MESSAGING':
        return <MessageSquare className="w-5 h-5" />;
      default:
        return <Wrench className="w-5 h-5" />;
    }
  };
  
  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-500';
      case 'DRAFT':
        return 'bg-yellow-500';
      case 'INACTIVE':
        return 'bg-gray-500';
      case 'DEPRECATED':
        return 'bg-red-500';
      default:
        return 'bg-blue-500';
    }
  };
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <Loader2 className="w-12 h-12 text-blue-500 animate-spin mb-4" />
          <h2 className="text-xl font-medium text-white">Loading tool...</h2>
        </div>
      </div>
    );
  }
  
  if (!tool) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
          <h2 className="text-xl font-medium text-white mb-2">Tool not found</h2>
          <p className="text-gray-400 mb-4">The tool you're looking for doesn't exist or you don't have access to it.</p>
          <button
            onClick={() => router.push('/dashboard/tools')}
            className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-all"
          >
            Back to Tools
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-6xl mx-auto"
      >
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center">
            <button
              onClick={() => router.push('/dashboard/tools')}
              className="mr-4 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-all text-white"
            >
              <ArrowLeft className="w-5 h-5" />
            </button>
            <h1 className="text-3xl font-bold text-white">
              Tool Details
            </h1>
          </div>
          
          <div className="flex items-center gap-3">
            <button
              onClick={() => router.push(`/dashboard/tools/${toolId}/test`)}
              className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all flex items-center gap-2"
            >
              <Play className="w-4 h-4" />
              Test Tool
            </button>
            
            <button
              onClick={() => router.push(`/dashboard/tools/${toolId}/edit`)}
              className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all flex items-center gap-2"
            >
              <Edit className="w-4 h-4" />
              Edit Tool
            </button>
            
            {tool.status === 'ACTIVE' ? (
              <button
                onClick={handleDeactivateTool}
                disabled={deactivating}
                className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-red-500/20 transition-all flex items-center gap-2 disabled:opacity-50"
              >
                {deactivating ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <X className="w-4 h-4" />
                )}
                Deactivate
              </button>
            ) : (
              <button
                onClick={handleActivateTool}
                disabled={activating}
                className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-green-500/20 transition-all flex items-center gap-2 disabled:opacity-50"
              >
                {activating ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Check className="w-4 h-4" />
                )}
                Activate
              </button>
            )}
            
            <button
              onClick={() => setShowDeleteConfirm(true)}
              className="px-4 py-2 bg-red-500/20 border border-red-500/40 rounded-lg text-white hover:bg-red-500/30 transition-all flex items-center gap-2"
            >
              <Trash2 className="w-4 h-4" />
              Delete
            </button>
          </div>
        </div>
        
        {error && (
          <div className="p-4 bg-red-500/20 border border-red-500/40 rounded-lg mb-6 flex items-center gap-2 text-white">
            <AlertCircle className="w-5 h-5 flex-shrink-0" />
            <span>{error}</span>
          </div>
        )}
        
        {/* Tool Overview */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
          <div className="lg:col-span-2 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <div className="flex items-start">
              <div className="p-3 bg-white/10 rounded-lg mr-4">
                {getToolTypeIcon(tool.type)}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h2 className="text-2xl font-medium text-white">{tool.name}</h2>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(tool.status)} bg-opacity-20 text-white`}>
                    {tool.status}
                  </span>
                </div>
                <p className="text-gray-300 mt-1">{tool.description || 'No description provided'}</p>
                
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div>
                    <span className="text-sm text-gray-400">Type:</span>
                    <div className="flex items-center mt-1">
                      {getToolTypeIcon(tool.type)}
                      <span className="text-white ml-1.5">{tool.type}</span>
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-400">Created:</span>
                    <div className="text-white mt-1">
                      {new Date(tool.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-400">Last Updated:</span>
                    <div className="text-white mt-1">
                      {new Date(tool.updatedAt).toLocaleDateString()}
                    </div>
                  </div>
                  <div>
                    <span className="text-sm text-gray-400">ID:</span>
                    <div className="text-white mt-1 font-mono text-sm truncate">
                      {tool.id}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <h3 className="text-lg font-medium text-white mb-4">Performance Metrics</h3>
            
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Executions</span>
                  <span className="text-white font-medium">{tool.executionCount || 0}</span>
                </div>
                <div className="h-2 bg-white/10 rounded-full mt-1 overflow-hidden">
                  <div 
                    className="h-full bg-blue-500 rounded-full" 
                    style={{ width: `${Math.min(100, ((tool.executionCount || 0) / 100) * 100)}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Success Rate</span>
                  <span className="text-white font-medium">{tool.successRate?.toFixed(1) || 0}%</span>
                </div>
                <div className="h-2 bg-white/10 rounded-full mt-1 overflow-hidden">
                  <div 
                    className={`h-full rounded-full ${
                      (tool.successRate || 0) > 90 ? 'bg-green-500' : 
                      (tool.successRate || 0) > 70 ? 'bg-yellow-500' : 
                      'bg-red-500'
                    }`}
                    style={{ width: `${tool.successRate || 0}%` }}
                  ></div>
                </div>
              </div>
              
              <div>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-400">Avg Response Time</span>
                  <span className="text-white font-medium">{tool.avgResponseTime ? `${tool.avgResponseTime.toFixed(0)}ms` : 'N/A'}</span>
                </div>
                <div className="h-2 bg-white/10 rounded-full mt-1 overflow-hidden">
                  <div 
                    className={`h-full rounded-full ${
                      (tool.avgResponseTime || 0) < 200 ? 'bg-green-500' : 
                      (tool.avgResponseTime || 0) < 500 ? 'bg-yellow-500' : 
                      'bg-red-500'
                    }`}
                    style={{ width: `${Math.min(100, ((tool.avgResponseTime || 0) / 1000) * 100)}%` }}
                  ></div>
                </div>
              </div>
              
              <div className="pt-2">
                <button
                  onClick={() => router.push(`/dashboard/tools/${toolId}/analytics`)}
                  className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all flex items-center justify-center gap-2"
                >
                  <BarChart2 className="w-4 h-4" />
                  View Analytics
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {/* Configuration Details */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* API Details */}
          {tool.type === 'API' && (
            <div className="lg:col-span-2 bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
              <h3 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
                <Globe className="w-5 h-5" />
                API Configuration
              </h3>
              
              <div className="space-y-4">
                <div>
                  <span className="text-sm text-gray-400">Endpoint:</span>
                  <div className="text-white mt-1 font-mono text-sm bg-white/5 p-2 rounded-lg break-all">
                    {tool.configuration.endpoint || 'Not specified'}
                  </div>
                </div>
                
                <div>
                  <span className="text-sm text-gray-400">Method:</span>
                  <div className="text-white mt-1">
                    <span className="px-2 py-1 bg-white/10 rounded-md font-mono">
                      {tool.configuration.method}
                    </span>
                  </div>
                </div>
                
                <div>
                  <span className="text-sm text-gray-400">Headers:</span>
                  {!tool.configuration.headers || Object.keys(tool.configuration.headers).length === 0 ? (
                    <div className="text-gray-400 italic mt-1">No headers defined</div>
                  ) : (
                    <div className="mt-1 bg-white/5 rounded-lg overflow-hidden">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="bg-white/10">
                            <th className="text-left py-2 px-3 text-gray-400 font-medium">Name</th>
                            <th className="text-left py-2 px-3 text-gray-400 font-medium">Value</th>
                          </tr>
                        </thead>
                        <tbody>
                          {Object.entries(tool.configuration.headers).map(([key, value], index) => (
                            <tr key={index} className="border-t border-white/10">
                              <td className="py-2 px-3 font-mono text-white">{key}</td>
                              <td className="py-2 px-3 font-mono text-white">{value as string}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  )}
                </div>
                
                <div>
                  <span className="text-sm text-gray-400">Authentication:</span>
                  <div className="text-white mt-1">
                    {tool.configuration.authentication?.type || 'NONE'}
                  </div>
                </div>
                
                <div>
                  <span className="text-sm text-gray-400">Timeout:</span>
                  <div className="text-white mt-1">
                    {tool.configuration.timeout ? `${tool.configuration.timeout}ms` : 'Default'}
                  </div>
                </div>
                
                <div>
                  <span className="text-sm text-gray-400">Retry Configuration:</span>
                  {!tool.configuration.retry ? (
                    <div className="text-gray-400 italic mt-1">Default retry configuration</div>
                  ) : (
                    <div className="mt-1 grid grid-cols-2 gap-2">
                      <div className="bg-white/5 p-2 rounded-lg">
                        <span className="text-xs text-gray-400">Max Retries:</span>
                        <div className="text-white">{tool.configuration.retry.maxRetries}</div>
                      </div>
                      <div className="bg-white/5 p-2 rounded-lg">
                        <span className="text-xs text-gray-400">Retry Delay:</span>
                        <div className="text-white">{tool.configuration.retry.retryDelay}ms</div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          )}
          
          {/* Parameters */}
          <div className={`${tool.type === 'API' ? '' : 'lg:col-span-2'} bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6`}>
            <h3 className="text-lg font-medium text-white mb-4 flex items-center gap-2">
              <FileJson className="w-5 h-5" />
              Parameters
            </h3>
            
            {!tool.configuration.parameters || tool.configuration.parameters.length === 0 ? (
              <div className="text-gray-400 italic">No parameters defined</div>
            ) : (
              <div className="bg-white/5 rounded-lg overflow-hidden">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="bg-white/10">
                      <th className="text-left py-2 px-3 text-gray-400 font-medium">Name</th>
                      <th className="text-left py-2 px-3 text-gray-400 font-medium">Type</th>
                      <th className="text-left py-2 px-3 text-gray-400 font-medium">Required</th>
                      <th className="text-left py-2 px-3 text-gray-400 font-medium">Description</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tool.configuration.parameters.map((param: any, index: number) => (
                      <tr key={index} className="border-t border-white/10">
                        <td className="py-2 px-3 font-mono text-white">{param.name}</td>
                        <td className="py-2 px-3 text-white">{param.type}</td>
                        <td className="py-2 px-3">
                          {param.required ? (
                            <Check className="w-4 h-4 text-green-500" />
                          ) : (
                            <X className="w-4 h-4 text-gray-400" />
                          )}
                        </td>
                        <td className="py-2 px-3 text-white">{param.description || '-'}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        </div>
      </motion.div>
      
      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-gray-800 rounded-xl border border-white/20 p-6 max-w-md w-full"
          >
            <h3 className="text-xl font-medium text-white mb-2">Delete Tool</h3>
            <p className="text-gray-300 mb-4">
              Are you sure you want to delete this tool? This action cannot be undone.
            </p>
            
            <div className="flex justify-end gap-3">
              <button
                onClick={() => setShowDeleteConfirm(false)}
                className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all"
              >
                Cancel
              </button>
              
              <button
                onClick={handleDeleteTool}
                disabled={deleting}
                className="px-4 py-2 bg-red-500/20 border border-red-500/40 rounded-lg text-white hover:bg-red-500/30 transition-all flex items-center gap-2"
              >
                {deleting ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Trash2 className="w-4 h-4" />
                )}
                Delete Tool
              </button>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
} 