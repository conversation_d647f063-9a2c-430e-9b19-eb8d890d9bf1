import { Global, Module, DynamicModule } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisService } from './redis.service';

/**
 * Redis configuration interface
 */
export interface RedisModuleOptions {
  host: string;
  port: number;
  password?: string;
  db?: number;
  retryAttempts?: number;
  retryDelay?: number;
  maxRetriesPerRequest?: number;
}

/**
 * Redis Module
 * 
 * Provides Redis connectivity for:
 * - Session management
 * - Caching
 * - Real-time pub/sub
 * - Rate limiting
 * - Queue management
 */
@Global()
@Module({})
export class RedisModule {
  /**
   * Create Redis module with async configuration
   */
  static forRootAsync(options: {
    imports?: any[];
    useFactory: (configService: ConfigService) => RedisModuleOptions | Promise<RedisModuleOptions>;
    inject?: any[];
  }): DynamicModule {
    return {
      module: RedisModule,
      imports: [ConfigModule, ...(options.imports || [])],
      providers: [
        {
          provide: 'REDIS_OPTIONS',
          useFactory: options.useFactory,
          inject: options.inject || [],
        },
        RedisService,
      ],
      exports: [RedisService],
    };
  }

  /**
   * Create Redis module with synchronous configuration
   */
  static forRoot(options: RedisModuleOptions): DynamicModule {
    return {
      module: RedisModule,
      imports: [ConfigModule],
      providers: [
        {
          provide: 'REDIS_OPTIONS',
          useValue: options,
        },
        RedisService,
      ],
      exports: [RedisService],
    };
  }
} 