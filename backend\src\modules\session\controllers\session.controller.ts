import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Req,
  HttpStatus,
  HttpException,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { Request } from 'express';
import { SessionService } from '../services/session.service';
import { SessionAnalyticsService } from '../services/session-analytics.service';
import { JwtAuthGuard } from '../../auth/guards/jwt-auth.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { Public } from '../../auth/decorators/public.decorator';

// Define the JWT payload interface to match what's in the auth service
interface JwtPayload {
  sub: string; // userId
  email: string;
  organizationId: string;
  role: string;
  permissions: string[];
  iat?: number;
  exp?: number;
}

interface CreateSessionDto {
  agentId?: string;
  metadata?: Record<string, any>;
}

interface AddMessageDto {
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  metadata?: Record<string, any>;
  tokens?: number;
}

/**
 * Session Controller
 * 
 * Handles session management endpoints including:
 * - Session creation and retrieval
 * - Message management
 * - Session metrics and analytics
 */
@ApiTags('Sessions')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('api/v1/sessions')
export class SessionController {
  private readonly logger = new Logger(SessionController.name);

  constructor(
    private readonly sessionService: SessionService,
    private readonly sessionAnalyticsService: SessionAnalyticsService,
  ) {}

  /**
   * Create a new session
   */
  @Post()
  @ApiOperation({ summary: 'Create a new session' })
  @ApiBody({ type: Object, description: 'Session creation parameters' })
  @ApiResponse({ status: 201, description: 'Session created successfully' })
  @ApiResponse({ status: 400, description: 'Invalid request' })
  async createSession(@Req() req: Request, @Body() body: CreateSessionDto) {
    try {
      const user = req['user'] as JwtPayload;
      
      if (!user || !user.sub || !user.organizationId) {
        throw new HttpException('User context not found', HttpStatus.UNAUTHORIZED);
      }

      const session = await this.sessionService.createSession(
        user.sub,
        user.organizationId,
        body.agentId,
        body.metadata,
      );

      // Track session creation
      await this.sessionAnalyticsService.trackSessionEvent(
        session.id,
        'session.created',
        { agentId: body.agentId },
      );

      return session;
    } catch (error: unknown) {
      this.logger.error('Error creating session', error);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to create session',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get session by ID
   */
  @Get(':id')
  @ApiOperation({ summary: 'Get session by ID' })
  @ApiParam({ name: 'id', description: 'Session ID' })
  @ApiResponse({ status: 200, description: 'Session retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  async getSession(@Param('id') id: string, @Req() req: Request) {
    try {
      const session = await this.sessionService.getSession(id);
      
      if (!session) {
        throw new HttpException('Session not found', HttpStatus.NOT_FOUND);
      }

      // Verify user has access to this session
      const user = req['user'] as JwtPayload;
      if (session.organizationId !== user.organizationId) {
        throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
      }

      return session;
    } catch (error: unknown) {
      this.logger.error(`Error retrieving session: ${id}`, error);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to retrieve session',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Add message to session
   */
  @Post(':id/messages')
  @ApiOperation({ summary: 'Add message to session' })
  @ApiParam({ name: 'id', description: 'Session ID' })
  @ApiBody({ type: Object, description: 'Message to add' })
  @ApiResponse({ status: 200, description: 'Message added successfully' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  async addMessage(
    @Param('id') id: string,
    @Body() message: AddMessageDto,
    @Req() req: Request,
  ) {
    try {
      const session = await this.sessionService.getSession(id);
      
      if (!session) {
        throw new HttpException('Session not found', HttpStatus.NOT_FOUND);
      }

      // Verify user has access to this session
      const user = req['user'] as JwtPayload;
      if (session.organizationId !== user.organizationId) {
        throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
      }

      const updatedSession = await this.sessionService.addMessage(id, message);

      // Track message added
      await this.sessionAnalyticsService.trackSessionEvent(
        id,
        'message.added',
        { role: message.role },
      );

      return updatedSession;
    } catch (error: unknown) {
      this.logger.error(`Error adding message to session: ${id}`, error);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to add message',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get user sessions
   */
  @Get()
  @ApiOperation({ summary: 'Get user sessions' })
  @ApiQuery({ name: 'limit', required: false, description: 'Maximum number of sessions to return' })
  @ApiQuery({ name: 'offset', required: false, description: 'Number of sessions to skip' })
  @ApiResponse({ status: 200, description: 'Sessions retrieved successfully' })
  async getUserSessions(
    @Req() req: Request,
    @Query('limit') limit: number = 10,
    @Query('offset') offset: number = 0,
  ) {
    try {
      const user = req['user'] as JwtPayload;
      
      if (!user || !user.sub) {
        throw new HttpException('User context not found', HttpStatus.UNAUTHORIZED);
      }

      const sessions = await this.sessionService.getUserSessions(user.sub);
      return sessions;
    } catch (error: unknown) {
      this.logger.error('Error retrieving user sessions', error);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to retrieve sessions',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Delete session
   */
  @Delete(':id')
  @ApiOperation({ summary: 'Delete session' })
  @ApiParam({ name: 'id', description: 'Session ID' })
  @ApiResponse({ status: 200, description: 'Session deleted successfully' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  async deleteSession(@Param('id') id: string, @Req() req: Request) {
    try {
      const session = await this.sessionService.getSession(id);
      
      if (!session) {
        throw new HttpException('Session not found', HttpStatus.NOT_FOUND);
      }

      // Verify user has access to this session
      const user = req['user'] as JwtPayload;
      if (session.organizationId !== user.organizationId) {
        throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
      }

      await this.sessionService.deleteSession(id);

      // Track session deletion
      await this.sessionAnalyticsService.trackSessionEvent(
        id,
        'session.deleted',
      );

      return { success: true, message: 'Session deleted successfully' };
    } catch (error: unknown) {
      this.logger.error(`Error deleting session: ${id}`, error);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to delete session',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get session metrics
   */
  @Get('metrics/organization')
  @ApiOperation({ summary: 'Get session metrics for organization' })
  @ApiQuery({ name: 'days', required: false, description: 'Number of days to include in metrics' })
  @ApiResponse({ status: 200, description: 'Metrics retrieved successfully' })
  @Roles('ORG_ADMIN', 'SUPER_ADMIN')
  async getSessionMetrics(@Req() req: Request, @Query('days') days: number = 7) {
    try {
      const user = req['user'] as JwtPayload;
      
      if (!user || !user.organizationId) {
        throw new HttpException('User context not found', HttpStatus.UNAUTHORIZED);
      }

      const metrics = await this.sessionAnalyticsService.getSessionMetrics(
        user.organizationId,
        days,
      );

      return metrics;
    } catch (error: unknown) {
      this.logger.error('Error retrieving session metrics', error);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to retrieve metrics',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Extend session expiration
   */
  @Post(':id/extend')
  @ApiOperation({ summary: 'Extend session expiration' })
  @ApiParam({ name: 'id', description: 'Session ID' })
  @ApiQuery({ name: 'seconds', required: false, description: 'Seconds to extend session by' })
  @ApiResponse({ status: 200, description: 'Session extended successfully' })
  @ApiResponse({ status: 404, description: 'Session not found' })
  async extendSession(
    @Param('id') id: string,
    @Query('seconds') seconds: number,
    @Req() req: Request,
  ) {
    try {
      const session = await this.sessionService.getSession(id);
      
      if (!session) {
        throw new HttpException('Session not found', HttpStatus.NOT_FOUND);
      }

      // Verify user has access to this session
      const user = req['user'] as JwtPayload;
      if (session.organizationId !== user.organizationId) {
        throw new HttpException('Access denied', HttpStatus.FORBIDDEN);
      }

      const updatedSession = await this.sessionService.extendSession(id, seconds);

      // Track session extension
      await this.sessionAnalyticsService.trackSessionEvent(
        id,
        'session.extended',
        { seconds },
      );

      return updatedSession;
    } catch (error: unknown) {
      this.logger.error(`Error extending session: ${id}`, error);
      throw new HttpException(
        error instanceof Error ? error.message : 'Failed to extend session',
        error instanceof HttpException ? error.getStatus() : HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Health check endpoint
   */
  @Get('health')
  @Public()
  @ApiOperation({ summary: 'Health check for session service' })
  @ApiResponse({ status: 200, description: 'Service is healthy' })
  async healthCheck() {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
    };
  }
} 