'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  Wrench, 
  ArrowLeft, 
  Play, 
  Loader2,
  AlertCircle,
  Server,
  Code,
  Database,
  Cloud,
  MessageSquare,
  Download,
  Copy
} from 'lucide-react';
import { useToast } from '@/lib/usetoast';
import axios from 'axios';

interface Tool {
  id: string;
  name: string;
  description: string;
  status: string;
  type: string;
  configuration: {
    endpoint?: string;
    method: string;
    parameters?: Array<{
      name: string;
      type: string;
      required: boolean;
      description?: string;
    }>;
  };
}

export default function TestToolPage() {
  const params = useParams();
  const toolId = params.id as string;
  const router = useRouter();
  const { showSuccess, showError } = useToast();
  
  const [tool, setTool] = useState<Tool | null>(null);
  const [loading, setLoading] = useState(true);
  const [executing, setExecuting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paramValues, setParamValues] = useState<Record<string, any>>({});
  const [result, setResult] = useState<any>(null);
  const [executionTime, setExecutionTime] = useState<number | null>(null);
  
  // Fetch tool data
  useEffect(() => {
    fetchTool();
  }, []);
  
  // Fetch tool data from API
  const fetchTool = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get(`/api/v1/tools/${toolId}`);
      setTool(response.data);
      
      // Initialize parameter values
      const initialParams: Record<string, any> = {};
      if (response.data.configuration?.parameters) {
        response.data.configuration.parameters.forEach((param: any) => {
          initialParams[param.name] = '';
        });
      }
      setParamValues(initialParams);
    } catch (err: any) {
      console.error('Error fetching tool:', err);
      setError(err.response?.data?.message || 'Failed to load tool');
      showError('Failed to load tool');
    } finally {
      setLoading(false);
    }
  };
  
  // Handle parameter change
  const handleParamChange = (name: string, value: any) => {
    setParamValues({
      ...paramValues,
      [name]: value,
    });
  };
  
  // Execute tool
  const executeTool = async () => {
    try {
      setExecuting(true);
      setError(null);
      setResult(null);
      setExecutionTime(null);
      
      const startTime = performance.now();
      
      const response = await axios.post(`/api/v1/tools/${toolId}/execute`, {
        parameters: paramValues,
      });
      
      const endTime = performance.now();
      setExecutionTime(endTime - startTime);
      setResult(response.data);
      
      showSuccess('Tool executed successfully');
    } catch (err: any) {
      console.error('Error executing tool:', err);
      setError(err.response?.data?.message || 'Failed to execute tool');
      showError('Failed to execute tool');
    } finally {
      setExecuting(false);
    }
  };
  
  // Copy result to clipboard
  const copyResult = () => {
    if (result) {
      navigator.clipboard.writeText(JSON.stringify(result, null, 2));
      showSuccess('Result copied to clipboard');
    }
  };
  
  // Get tool type icon
  const getToolTypeIcon = (type: string) => {
    switch (type) {
      case 'API':
        return <Server className="w-5 h-5" />;
      case 'FUNCTION':
        return <Code className="w-5 h-5" />;
      case 'DATABASE':
        return <Database className="w-5 h-5" />;
      case 'STORAGE':
        return <Cloud className="w-5 h-5" />;
      case 'MESSAGING':
        return <MessageSquare className="w-5 h-5" />;
      default:
        return <Wrench className="w-5 h-5" />;
    }
  };
  
  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <Loader2 className="w-12 h-12 text-blue-500 animate-spin mb-4" />
          <h2 className="text-xl font-medium text-white">Loading tool...</h2>
        </div>
      </div>
    );
  }
  
  if (!tool) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <AlertCircle className="w-12 h-12 text-red-500 mb-4" />
          <h2 className="text-xl font-medium text-white mb-2">Tool not found</h2>
          <p className="text-gray-400 mb-4">The tool you're looking for doesn't exist or you don't have access to it.</p>
          <button
            onClick={() => router.push('/dashboard/tools')}
            className="px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-all"
          >
            Back to Tools
          </button>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-5xl mx-auto"
      >
        <div className="flex items-center mb-8">
          <button
            onClick={() => router.push('/dashboard/tools')}
            className="mr-4 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-all text-white"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <h1 className="text-3xl font-bold text-white">
            Test Tool
          </h1>
        </div>
        
        {/* Tool Info */}
        <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6 mb-6">
          <div className="flex items-start">
            <div className="p-3 bg-white/10 rounded-lg mr-4">
              {getToolTypeIcon(tool.type)}
            </div>
            <div>
              <h2 className="text-xl font-medium text-white">{tool.name}</h2>
              <p className="text-gray-300 mt-1">{tool.description || 'No description provided'}</p>
              <div className="flex items-center mt-2">
                <span className="text-sm text-gray-400 mr-2">Endpoint:</span>
                <span className="text-sm text-white">{tool.configuration.endpoint || 'N/A'}</span>
              </div>
              <div className="flex items-center mt-1">
                <span className="text-sm text-gray-400 mr-2">Method:</span>
                <span className="text-sm text-white">{tool.configuration.method}</span>
              </div>
            </div>
          </div>
        </div>
        
        {error && (
          <div className="p-4 bg-red-500/20 border border-red-500/40 rounded-lg mb-6 flex items-center gap-2 text-white">
            <AlertCircle className="w-5 h-5 flex-shrink-0" />
            <span>{error}</span>
          </div>
        )}
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Parameters */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <h2 className="text-xl font-medium text-white mb-4">Parameters</h2>
            
            {!tool.configuration.parameters || tool.configuration.parameters.length === 0 ? (
              <p className="text-sm text-gray-400 italic">No parameters required</p>
            ) : (
              <div className="space-y-4">
                {tool.configuration.parameters.map((param, index) => (
                  <div key={index}>
                    <label className="block text-white text-sm font-medium mb-1">
                      {param.name}
                      {param.required && <span className="text-red-400 ml-1">*</span>}
                      {param.description && (
                        <span className="text-gray-400 text-xs ml-2">({param.description})</span>
                      )}
                    </label>
                    
                    {param.type === 'boolean' ? (
                      <select
                        value={paramValues[param.name] || ''}
                        onChange={(e) => handleParamChange(param.name, e.target.value === 'true')}
                        className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
                      >
                        <option value="">Select...</option>
                        <option value="true">True</option>
                        <option value="false">False</option>
                      </select>
                    ) : param.type === 'object' || param.type === 'array' ? (
                      <textarea
                        value={paramValues[param.name] || ''}
                        onChange={(e) => {
                          try {
                            // Try to parse as JSON if not empty
                            const value = e.target.value.trim() 
                              ? JSON.parse(e.target.value)
                              : e.target.value;
                            handleParamChange(param.name, value);
                          } catch {
                            // If not valid JSON, store as string
                            handleParamChange(param.name, e.target.value);
                          }
                        }}
                        placeholder={`Enter ${param.type === 'object' ? 'JSON object' : 'JSON array'}...`}
                        rows={4}
                        className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40 font-mono text-sm"
                      />
                    ) : (
                      <input
                        type={param.type === 'number' ? 'number' : 'text'}
                        value={paramValues[param.name] || ''}
                        onChange={(e) => handleParamChange(param.name, e.target.value)}
                        placeholder={`Enter ${param.name}...`}
                        className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40"
                      />
                    )}
                  </div>
                ))}
              </div>
            )}
            
            <button
              onClick={executeTool}
              disabled={executing}
              className="mt-6 px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white transition-all hover:opacity-90 flex items-center gap-2 disabled:opacity-70"
            >
              {executing ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Play className="w-5 h-5" />
              )}
              Execute Tool
            </button>
          </div>
          
          {/* Result */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-medium text-white">Result</h2>
              
              {result && (
                <div className="flex gap-2">
                  <button
                    onClick={copyResult}
                    className="p-1.5 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-all"
                    title="Copy result"
                  >
                    <Copy className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => {
                      const blob = new Blob([JSON.stringify(result, null, 2)], { type: 'application/json' });
                      const url = URL.createObjectURL(blob);
                      const a = document.createElement('a');
                      a.href = url;
                      a.download = `${tool.name.toLowerCase().replace(/\s+/g, '-')}-result.json`;
                      a.click();
                      URL.revokeObjectURL(url);
                    }}
                    className="p-1.5 bg-white/10 hover:bg-white/20 rounded-lg text-white transition-all"
                    title="Download result"
                  >
                    <Download className="w-4 h-4" />
                  </button>
                </div>
              )}
            </div>
            
            {executing ? (
              <div className="flex items-center justify-center p-12">
                <Loader2 className="w-8 h-8 text-blue-500 animate-spin" />
              </div>
            ) : result ? (
              <div>
                {executionTime !== null && (
                  <div className="text-xs text-gray-400 mb-2">
                    Execution time: {executionTime.toFixed(2)}ms
                  </div>
                )}
                <pre className="bg-white/5 rounded-lg p-4 text-sm text-gray-300 overflow-x-auto max-h-96">
                  {JSON.stringify(result, null, 2)}
                </pre>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center p-12 text-center">
                <Wrench className="w-12 h-12 text-gray-500 mb-4 opacity-30" />
                <p className="text-gray-400">Execute the tool to see results</p>
              </div>
            )}
          </div>
        </div>
      </motion.div>
    </div>
  );
} 