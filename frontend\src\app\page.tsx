'use client';

import { motion } from 'framer-motion';
import React from 'react';
import { 
  Brain, 
  Zap, 
  Users, 
  Shield, 
  ArrowRight, 
  CheckCircle2, 
  Star,
  PlayCircle,
  BarChart3,
  Globe,
  Layers,
  Sparkles,
  Rocket,
  Code,
  Building2,
  Target,
  TrendingUp,
  Clock,
  Award
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';

export default function LandingPage() {
  const [activeFeature, setActiveFeature] = useState(0);

  const features = [
    {
      icon: Brain,
      title: 'AI Agent Builder',
      description: 'Create intelligent AI agents with our visual drag-and-drop interface. No coding required.',
      gradient: 'from-blue-500 to-purple-500',
    },
    {
      icon: Zap,
      title: 'Tool Integration',
      description: 'Connect your agents to APIs, databases, and external services seamlessly.',
      gradient: 'from-purple-500 to-pink-500',
    },
    {
      icon: Layers,
      title: 'Hybrid Workflows',
      description: 'Combine AI agents with human oversight for complex, reliable automation.',
      gradient: 'from-pink-500 to-red-500',
    },
    {
      icon: BarChart3,
      title: 'Analytics & Insights',
      description: 'Monitor performance, track usage, and optimize your AI implementations.',
      gradient: 'from-red-500 to-orange-500',
    },
  ];

  const benefits = [
    {
      icon: Rocket,
      title: 'Rapid Deployment',
      description: 'Get your AI agents up and running in minutes, not months.',
    },
    {
      icon: Shield,
      title: 'Enterprise Security',
      description: 'Bank-grade security with SOC 2 compliance and data encryption.',
    },
    {
      icon: Globe,
      title: 'Scalable Infrastructure',
      description: 'Handle millions of requests with our cloud-native architecture.',
    },
    {
      icon: Users,
      title: 'Team Collaboration',
      description: 'Work together with role-based access and real-time collaboration.',
    },
  ];

  const testimonials = [
    {
      name: 'Sarah Chen',
      role: 'CTO, TechCorp',
      avatar: '/avatars/sarah.jpg',
      content: 'SynapseAI transformed how we handle customer support. Our response time improved by 80%.',
      rating: 5,
    },
    {
      name: 'Marcus Johnson',
      role: 'Head of Operations, StartupXYZ',
      avatar: '/avatars/marcus.jpg',
      content: 'The hybrid workflow capability is a game-changer. Human oversight with AI efficiency.',
      rating: 5,
    },
    {
      name: 'Emily Rodriguez',
      role: 'AI Lead, InnovateLab',
      avatar: '/avatars/emily.jpg',
      content: 'Finally, an AI platform that developers and business users can both love.',
      rating: 5,
    },
  ];

  const stats = [
    { number: '10M+', label: 'API Calls Processed' },
    { number: '500+', label: 'Companies Trust Us' },
    { number: '99.9%', label: 'Uptime Guarantee' },
    { number: '24/7', label: 'Support Available' },
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900">
      {/* Navigation */}
      <nav className="relative z-50 border-b border-white/10 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl">
                <Brain className="w-6 h-6 text-white" />
              </div>
              <span className="text-2xl font-bold text-white">SynapseAI</span>
            </div>
            
            <div className="hidden md:flex items-center gap-8">
              <Link href="#features" className="text-gray-300 hover:text-white transition-colors">
                Features
              </Link>
              <Link href="#pricing" className="text-gray-300 hover:text-white transition-colors">
                Pricing
              </Link>
              <Link href="#about" className="text-gray-300 hover:text-white transition-colors">
                About
              </Link>
              <Link href="/docs" className="text-gray-300 hover:text-white transition-colors">
                Docs
              </Link>
            </div>

            <div className="flex items-center gap-4">
              <Link
                href="/auth/login"
                className="px-4 py-2 text-white hover:text-gray-300 transition-colors"
              >
                Sign In
              </Link>
              <Link
                href="/auth/register"
                className="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white font-medium hover:opacity-90 transition-all"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/5 to-pink-500/10"></div>
        
        <div className="relative max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center max-w-4xl mx-auto"
          >
            <div className="flex justify-center mb-6">
              <div className="flex items-center gap-2 px-4 py-2 bg-white/10 rounded-full border border-white/20">
                <Sparkles className="w-4 h-4 text-yellow-400" />
                <span className="text-white text-sm">Now in Public Beta</span>
              </div>
            </div>
            
            <h1 className="text-5xl lg:text-7xl font-bold text-white mb-6 leading-tight">
              Build AI Agents
              <br />
              <span className="bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                Without Code
              </span>
            </h1>
            
            <p className="text-xl text-gray-300 mb-8 leading-relaxed">
              Create powerful AI agents, integrate with any tool, and automate complex workflows 
              with our intuitive visual platform. No coding expertise required.
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-12">
              <Link
                href="/auth/register"
                className="flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white font-semibold text-lg hover:opacity-90 transition-all shadow-lg"
              >
                Start Building Free
                <ArrowRight className="w-5 h-5" />
              </Link>
              
              <button className="flex items-center gap-2 px-8 py-4 bg-white/10 border border-white/20 rounded-lg text-white font-semibold text-lg hover:bg-white/20 transition-all">
                <PlayCircle className="w-5 h-5" />
                Watch Demo
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {stats.map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="text-3xl lg:text-4xl font-bold text-white mb-2">
                    {stat.number}
                  </div>
                  <div className="text-gray-400 text-sm">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 lg:py-32">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Everything you need to build
              <br />
              <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                intelligent automation
              </span>
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              From simple chatbots to complex AI workflows, SynapseAI provides all the tools 
              you need to create, deploy, and manage AI agents at scale.
            </p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Feature List */}
            <div className="space-y-6">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true }}
                  transition={{ delay: index * 0.1 }}
                  onMouseEnter={() => setActiveFeature(index)}
                  className={`p-6 rounded-2xl border cursor-pointer transition-all ${
                    activeFeature === index
                      ? 'bg-white/10 border-white/30'
                      : 'bg-white/5 border-white/10 hover:bg-white/8'
                  }`}
                >
                  <div className="flex items-start gap-4">
                    <div className={`p-3 bg-gradient-to-r ${feature.gradient} rounded-xl`}>
                      <feature.icon className="w-6 h-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-white mb-2">
                        {feature.title}
                      </h3>
                      <p className="text-gray-400">
                        {feature.description}
                      </p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Feature Visual */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-3xl p-8 border border-white/20">
                <div className="bg-white/10 rounded-2xl p-6 backdrop-blur-sm">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="w-3 h-3 bg-red-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-400 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                  </div>
                  
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 bg-gradient-to-r ${features[activeFeature]!.gradient} rounded-lg`}>
                        {/* Use dynamic component properly */}
                        {React.createElement(features[activeFeature]!.icon, { className: "w-4 h-4 text-white" })}
                      </div>
                      <div className="h-2 bg-white/20 rounded-full flex-1">
                        <motion.div
                          className="h-full bg-gradient-to-r from-blue-400 to-purple-400 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: '70%' }}
                          transition={{ duration: 0.8, delay: 0.2 }}
                        />
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-3 gap-2">
                      {[1, 2, 3].map((i) => (
                        <div key={i} className="h-16 bg-white/10 rounded-lg"></div>
                      ))}
                    </div>
                    
                    <div className="flex justify-between">
                      <div className="w-8 h-8 bg-green-400/20 rounded-full flex items-center justify-center">
                        <CheckCircle2 className="w-4 h-4 text-green-400" />
                      </div>
                      <div className="text-white text-sm">
                        {features[activeFeature]!.title}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 lg:py-32 bg-white/5">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Why choose SynapseAI?
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              We've built the most comprehensive AI agent platform to help you succeed.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {benefits.map((benefit, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="text-center p-6"
              >
                <div className="inline-flex p-4 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-2xl mb-6">
                  <benefit.icon className="w-8 h-8 text-blue-400" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-3">
                  {benefit.title}
                </h3>
                <p className="text-gray-400">
                  {benefit.description}
                </p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 lg:py-32">
        <div className="max-w-7xl mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Trusted by innovative teams
            </h2>
            <p className="text-xl text-gray-400 max-w-3xl mx-auto">
              See what our customers are saying about SynapseAI.
            </p>
          </motion.div>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="p-8 bg-white/10 rounded-2xl border border-white/20 backdrop-blur-sm"
              >
                <div className="flex gap-1 mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                
                <p className="text-gray-300 mb-6 leading-relaxed">
                  "{testimonial.content}"
                </p>
                
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                    <span className="text-white font-semibold">
                      {testimonial.name.split(' ').map(n => n[0]).join('')}
                    </span>
                  </div>
                  <div>
                    <div className="text-white font-semibold">
                      {testimonial.name}
                    </div>
                    <div className="text-gray-400 text-sm">
                      {testimonial.role}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 lg:py-32 bg-gradient-to-br from-blue-500/20 to-purple-500/20">
        <div className="max-w-4xl mx-auto px-6 text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
          >
            <h2 className="text-4xl lg:text-5xl font-bold text-white mb-6">
              Ready to build the future?
            </h2>
            <p className="text-xl text-gray-300 mb-8">
              Join thousands of developers and businesses already building with SynapseAI.
            </p>
            
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link
                href="/auth/register"
                className="flex items-center gap-2 px-8 py-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white font-semibold text-lg hover:opacity-90 transition-all shadow-lg"
              >
                Start Building Free
                <ArrowRight className="w-5 h-5" />
              </Link>
              
              <Link
                href="/contact"
                className="flex items-center gap-2 px-8 py-4 bg-white/10 border border-white/20 rounded-lg text-white font-semibold text-lg hover:bg-white/20 transition-all"
              >
                Contact Sales
              </Link>
            </div>
            
            <p className="text-gray-400 text-sm mt-6">
              Free plan includes 1,000 API calls per month. No credit card required.
            </p>
          </motion.div>
        </div>
      </section>

      {/* Footer */}
      <footer className="border-t border-white/10 py-12">
        <div className="max-w-7xl mx-auto px-6">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-3 mb-4">
                <div className="p-2 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl">
                  <Brain className="w-5 h-5 text-white" />
                </div>
                <span className="text-xl font-bold text-white">SynapseAI</span>
              </div>
              <p className="text-gray-400 text-sm leading-relaxed">
                Build intelligent AI agents without code. Deploy at scale with enterprise security.
              </p>
            </div>
            
            <div>
              <h3 className="text-white font-semibold mb-4">Product</h3>
              <div className="space-y-2">
                <Link href="#features" className="block text-gray-400 hover:text-white text-sm transition-colors">
                  Features
                </Link>
                <Link href="/pricing" className="block text-gray-400 hover:text-white text-sm transition-colors">
                  Pricing
                </Link>
                <Link href="/docs" className="block text-gray-400 hover:text-white text-sm transition-colors">
                  Documentation
                </Link>
                <Link href="/api" className="block text-gray-400 hover:text-white text-sm transition-colors">
                  API Reference
                </Link>
              </div>
            </div>
            
            <div>
              <h3 className="text-white font-semibold mb-4">Company</h3>
              <div className="space-y-2">
                <Link href="/about" className="block text-gray-400 hover:text-white text-sm transition-colors">
                  About
                </Link>
                <Link href="/blog" className="block text-gray-400 hover:text-white text-sm transition-colors">
                  Blog
                </Link>
                <Link href="/careers" className="block text-gray-400 hover:text-white text-sm transition-colors">
                  Careers
                </Link>
                <Link href="/contact" className="block text-gray-400 hover:text-white text-sm transition-colors">
                  Contact
                </Link>
              </div>
            </div>
            
            <div>
              <h3 className="text-white font-semibold mb-4">Legal</h3>
              <div className="space-y-2">
                <Link href="/privacy" className="block text-gray-400 hover:text-white text-sm transition-colors">
                  Privacy Policy
                </Link>
                <Link href="/terms" className="block text-gray-400 hover:text-white text-sm transition-colors">
                  Terms of Service
                </Link>
                <Link href="/security" className="block text-gray-400 hover:text-white text-sm transition-colors">
                  Security
                </Link>
                <Link href="/compliance" className="block text-gray-400 hover:text-white text-sm transition-colors">
                  Compliance
                </Link>
              </div>
            </div>
          </div>
          
          <div className="border-t border-white/10 mt-8 pt-8 flex flex-col md:flex-row items-center justify-between">
            <p className="text-gray-400 text-sm">
              © 2024 SynapseAI. All rights reserved.
            </p>
            <div className="flex items-center gap-6 mt-4 md:mt-0">
              <Link href="/status" className="text-gray-400 hover:text-white text-sm transition-colors">
                Status
              </Link>
              <Link href="/changelog" className="text-gray-400 hover:text-white text-sm transition-colors">
                Changelog
              </Link>
              <div className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span className="text-gray-400 text-sm">All systems operational</span>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}