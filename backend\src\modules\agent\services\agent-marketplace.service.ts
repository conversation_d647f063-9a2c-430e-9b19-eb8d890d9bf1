import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../prisma/prisma.service';
import { AgentService } from './agent.service';

@Injectable()
export class AgentMarketplaceService {
  private readonly logger = new Logger(AgentMarketplaceService.name);

  constructor(
    private readonly prismaService: PrismaService,
    private readonly agentService: AgentService,
  ) {}

  async publishAgent(
    agentId: string,
    userId: string,
    organizationId: string,
    data: {
      isPublic: boolean;
      category?: string;
      tags?: string[];
      description?: string;
    },
  ): Promise<any> {
    try {
      const agent = await this.agentService.getAgent(agentId, userId, organizationId);
      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      const updated = await this.prismaService.agent.update({
        where: { id: agentId },
        data: {
          description: data.description || agent.description,
        },
      });

      this.logger.log(`Agent published to marketplace: ${agentId}`);
      return updated;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to publish agent: ${err.message}`, err.stack);
      throw error;
    }
  }

  async searchMarketplace(
    options: {
      search?: string;
      limit?: number;
      offset?: number;
    } = {},
  ): Promise<{ agents: any[]; total: number }> {
    try {
      const where = {
        deletedAt: null,
        ...(options.search && {
          OR: [
            { name: { contains: options.search, mode: 'insensitive' as const } },
            { description: { contains: options.search, mode: 'insensitive' as const } },
          ],
        }),
      };

      const total = await this.prismaService.agent.count({ where });

      const agents = await this.prismaService.agent.findMany({
        where,
        take: options.limit || 20,
        skip: options.offset || 0,
        orderBy: { createdAt: 'desc' },
      });

      return {
        agents,
        total,
      };
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to search marketplace: ${err.message}`, err.stack);
      throw error;
    }
  }

  async downloadAgent(
    agentId: string,
    userId: string,
    organizationId: string,
    data: { name?: string } = {},
  ): Promise<any> {
    try {
      const agent = await this.prismaService.agent.findFirst({
        where: {
          id: agentId,
          deletedAt: null,
        },
      });

      if (!agent) {
        throw new NotFoundException('Agent not found');
      }

      const newAgent = await this.agentService.createAgent(userId, organizationId, {
        name: data.name || `${agent.name} (Copy)`,
        description: agent.description || '',
        configuration: {
          systemPrompt: agent.prompt || '',
          model: agent.model,
          temperature: agent.temperature,
          maxTokens: agent.maxTokens,
          tools: agent.tools,
        },
      });

      this.logger.log(`Agent downloaded: ${agentId} by user: ${userId}`);
      return newAgent;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      this.logger.error(`Failed to download agent: ${err.message}`, err.stack);
      throw error;
    }
  }
} 