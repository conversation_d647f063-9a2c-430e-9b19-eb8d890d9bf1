import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { RedisService } from '../../redis/redis.service';
import { SessionStatus } from '.prisma/client';

/**
 * Session Cleanup Service
 * 
 * Handles automatic session management:
 * - Expired session cleanup
 * - Orphaned session detection
 * - Session analytics and reporting
 * - Database and Redis synchronization
 */
@Injectable()
export class SessionCleanupService {
  private readonly logger = new Logger(SessionCleanupService.name);
  private readonly SESSION_PREFIX = 'session:';

  constructor(
    private readonly prismaService: PrismaService,
    private readonly redisService: RedisService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * Run cleanup every hour
   */
  @Cron(CronExpression.EVERY_HOUR)
  async cleanupExpiredSessions() {
    this.logger.log('Running expired session cleanup');
    
    try {
      // Find expired sessions in database
      const expiredSessions = await this.prismaService.session.findMany({
        where: {
          status: SessionStatus.ACTIVE,
          expiresAt: {
            lt: new Date(),
          },
        },
        select: {
          id: true,
        },
      });

      if (expiredSessions.length === 0) {
        this.logger.log('No expired sessions found');
        return;
      }

      this.logger.log(`Found ${expiredSessions.length} expired sessions to clean up`);

      // Update database status
      await this.prismaService.session.updateMany({
        where: {
          id: {
            in: expiredSessions.map(s => s.id),
          },
        },
        data: {
          status: SessionStatus.EXPIRED,
        },
      });

      // Delete from Redis
      const redisKeys = expiredSessions.map(s => `${this.SESSION_PREFIX}${s.id}`);
      for (const key of redisKeys) {
        await this.redisService.del(key);
      }

      this.logger.log(`Cleaned up ${expiredSessions.length} expired sessions`);
    } catch (error) {
      this.logger.error('Error cleaning up expired sessions', error);
    }
  }

  /**
   * Run daily to clean up orphaned sessions
   */
  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async cleanupOrphanedSessions() {
    this.logger.log('Running orphaned session cleanup');
    
    try {
      // Get all active sessions from database
      const activeSessions = await this.prismaService.session.findMany({
        where: {
          status: SessionStatus.ACTIVE,
        },
        select: {
          id: true,
        },
      });

      let orphanedCount = 0;

      // Check each session in Redis
      for (const session of activeSessions) {
        const exists = await this.redisService.exists(`${this.SESSION_PREFIX}${session.id}`);
        
        if (!exists) {
          // Session exists in DB but not in Redis - it's orphaned
          await this.prismaService.session.update({
            where: {
              id: session.id,
            },
            data: {
              status: SessionStatus.EXPIRED,
            },
          });
          orphanedCount++;
        }
      }

      if (orphanedCount > 0) {
        this.logger.log(`Cleaned up ${orphanedCount} orphaned sessions`);
      } else {
        this.logger.log('No orphaned sessions found');
      }
    } catch (error) {
      this.logger.error('Error cleaning up orphaned sessions', error);
    }
  }

  /**
   * Run weekly to generate session analytics
   */
  @Cron(CronExpression.EVERY_WEEK)
  async generateSessionAnalytics() {
    this.logger.log('Generating session analytics');
    
    try {
      // Get session statistics for the past week
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

      // Count sessions by status
      const sessionsByStatus = await this.prismaService.session.groupBy({
        by: ['status'],
        _count: {
          id: true,
        },
        where: {
          createdAt: {
            gte: oneWeekAgo,
          },
        },
      });

      // Count sessions by organization
      const sessionsByOrg = await this.prismaService.session.groupBy({
        by: ['organizationId'],
        _count: {
          id: true,
        },
        where: {
          createdAt: {
            gte: oneWeekAgo,
          },
        },
      });

      // Average session duration
      const avgDurationResult = await this.prismaService.$queryRaw`
        SELECT AVG(EXTRACT(EPOCH FROM ("updatedAt" - "createdAt"))) as avg_duration
        FROM sessions
        WHERE "createdAt" >= ${oneWeekAgo}
        AND "status" != 'ACTIVE'
      `;

      this.logger.log('Session analytics generated', {
        sessionsByStatus,
        sessionsByOrg,
        avgDuration: avgDurationResult,
      });

      // Store analytics in database
      // In a real implementation, we would store this in an analytics table
    } catch (error) {
      this.logger.error('Error generating session analytics', error);
    }
  }
} 