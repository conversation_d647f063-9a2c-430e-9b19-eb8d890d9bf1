'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  Wrench, 
  ArrowLeft, 
  Save, 
  Trash2, 
  Plus, 
  Minus,
  Server,
  Code,
  Database,
  Cloud,
  MessageSquare,
  Loader2,
  AlertCircle,
  Check
} from 'lucide-react';
import { useToast } from '@/lib/usetoast';
import axios from 'axios';

interface ToolFormProps {
  toolId?: string;
  isEditing?: boolean;
}

interface ToolParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  required: boolean;
  description?: string;
  defaultValue?: any;
}

interface ToolFormData {
  name: string;
  description: string;
  type: 'API' | 'FUNCTION' | 'DATABASE' | 'STORAGE' | 'MESSAGING';
  configuration: {
    endpoint?: string;
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
    headers?: Record<string, string>;
    parameters?: ToolParameter[];
    authentication?: {
      type: 'NONE' | 'BASIC' | 'API_KEY' | 'OAUTH2' | 'CUSTOM';
      credentials?: Record<string, string>;
    };
    timeout?: number;
    retry?: {
      maxRetries: number;
      retryDelay: number;
    };
  };
}

export default function ToolForm({ toolId, isEditing = false }: ToolFormProps) {
  const router = useRouter();
  const { showSuccess, showError } = useToast();
  
  const [loading, setLoading] = useState(false);
  const [fetchingTool, setFetchingTool] = useState(isEditing);
  const [validating, setValidating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  
  const [formData, setFormData] = useState<ToolFormData>({
    name: '',
    description: '',
    type: 'API',
    configuration: {
      endpoint: '',
      method: 'GET',
      headers: {},
      parameters: [],
      authentication: {
        type: 'NONE',
        credentials: {},
      },
      timeout: 30000,
      retry: {
        maxRetries: 3,
        retryDelay: 1000,
      },
    },
  });
  
  // Fetch tool data if editing
  useEffect(() => {
    if (isEditing && toolId) {
      fetchTool(toolId);
    }
  }, [isEditing, toolId]);
  
  // Fetch tool data from API
  const fetchTool = async (id: string) => {
    try {
      setFetchingTool(true);
      setError(null);
      
      const response = await axios.get(`/api/v1/tools/${id}`);
      
      setFormData({
        name: response.data.name,
        description: response.data.description || '',
        type: response.data.type,
        configuration: response.data.configuration || {
          endpoint: '',
          method: 'GET',
          headers: {},
          parameters: [],
          authentication: {
            type: 'NONE',
            credentials: {},
          },
          timeout: 30000,
          retry: {
            maxRetries: 3,
            retryDelay: 1000,
          },
        },
      });
    } catch (err: any) {
      console.error('Error fetching tool:', err);
      setError(err.response?.data?.message || 'Failed to load tool');
      showError('Failed to load tool');
    } finally {
      setFetchingTool(false);
    }
  };
  
  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setValidationErrors(errors);
      return;
    }
    
    try {
      setLoading(true);
      setError(null);
      
      if (isEditing && toolId) {
        // Update existing tool
        await axios.put(`/api/v1/tools/${toolId}`, formData);
        showSuccess('Tool updated successfully');
      } else {
        // Create new tool
        await axios.post('/api/v1/tools', formData);
        showSuccess('Tool created successfully');
      }
      
      // Redirect to tools list
      router.push('/dashboard/tools');
    } catch (err: any) {
      console.error('Error saving tool:', err);
      setError(err.response?.data?.message || 'Failed to save tool');
      showError('Failed to save tool');
    } finally {
      setLoading(false);
    }
  };
  
  // Validate configuration with backend
  const validateConfiguration = async () => {
    try {
      setValidating(true);
      setError(null);
      
      const response = await axios.post('/api/v1/tools/validate', {
        configuration: formData.configuration,
      });
      
      if (response.data.valid) {
        showSuccess('Configuration is valid');
      } else {
        setValidationErrors(response.data.errors || {});
        showError('Configuration has errors');
      }
    } catch (err: any) {
      console.error('Error validating configuration:', err);
      setError(err.response?.data?.message || 'Failed to validate configuration');
      showError('Failed to validate configuration');
    } finally {
      setValidating(false);
    }
  };
  
  // Form validation
  const validateForm = () => {
    const errors: Record<string, string> = {};
    
    if (!formData.name.trim()) {
      errors.name = 'Name is required';
    }
    
    if (formData.type === 'API' && !formData.configuration.endpoint?.trim()) {
      errors.endpoint = 'Endpoint is required for API tools';
    }
    
    return errors;
  };
  
  // Handle input change
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name.includes('.')) {
      // Handle nested fields (e.g., configuration.endpoint)
      const [parent, child] = name.split('.');
      const parentKey = parent as keyof ToolFormData;
      const parentValue = formData[parentKey];
      
      if (parentValue && typeof parentValue === 'object') {
        setFormData({
          ...formData,
          [parentKey]: {
            ...parentValue,
            [child as keyof typeof parentValue]: value,
          } as typeof parentValue,
        });
      }
    } else {
      // Handle top-level fields
      setFormData({
        ...formData,
        [name]: value,
      });
    }
    
    // Clear validation error for this field
    if (validationErrors[name]) {
      setValidationErrors({
        ...validationErrors,
        [name]: '',
      });
    }
  };
  
  // Add header
  const addHeader = () => {
    setFormData({
      ...formData,
      configuration: {
        ...formData.configuration,
        headers: {
          ...formData.configuration.headers,
          '': '',
        },
      },
    });
  };
  
  // Update header
  const updateHeader = (oldKey: string, newKey: string, value: string) => {
    const updatedHeaders = { ...formData.configuration.headers };
    
    // Remove old key if it's different
    if (oldKey !== newKey && oldKey in updatedHeaders) {
      delete updatedHeaders[oldKey];
    }
    
    // Set new key-value
    updatedHeaders[newKey] = value;
    
    setFormData({
      ...formData,
      configuration: {
        ...formData.configuration,
        headers: updatedHeaders,
      },
    });
  };
  
  // Remove header
  const removeHeader = (key: string) => {
    const updatedHeaders = { ...formData.configuration.headers };
    delete updatedHeaders[key];
    
    setFormData({
      ...formData,
      configuration: {
        ...formData.configuration,
        headers: updatedHeaders,
      },
    });
  };
  
  // Add parameter
  const addParameter = () => {
    setFormData({
      ...formData,
      configuration: {
        ...formData.configuration,
        parameters: [
          ...(formData.configuration.parameters || []),
          {
            name: '',
            type: 'string',
            required: false,
            description: '',
          },
        ],
      },
    });
  };
  
  // Update parameter
  const updateParameter = (index: number, field: keyof ToolParameter, value: any) => {
    const updatedParameters = [...(formData.configuration.parameters || [])];
    
    // Ensure we have a valid parameter object at this index
    if (!updatedParameters[index]) {
      updatedParameters[index] = {
        name: '',
        type: 'string',
        required: false,
      };
    }
    
    // Update the field with type assertion
    updatedParameters[index] = {
      ...updatedParameters[index],
      [field]: value,
    } as ToolParameter;
    
    setFormData({
      ...formData,
      configuration: {
        ...formData.configuration,
        parameters: updatedParameters,
      },
    });
  };
  
  // Remove parameter
  const removeParameter = (index: number) => {
    const updatedParameters = [...(formData.configuration.parameters || [])];
    updatedParameters.splice(index, 1);
    
    setFormData({
      ...formData,
      configuration: {
        ...formData.configuration,
        parameters: updatedParameters,
      },
    });
  };
  
  // Get tool type icon
  const getToolTypeIcon = (type: string) => {
    switch (type) {
      case 'API':
        return <Server className="w-5 h-5" />;
      case 'FUNCTION':
        return <Code className="w-5 h-5" />;
      case 'DATABASE':
        return <Database className="w-5 h-5" />;
      case 'STORAGE':
        return <Cloud className="w-5 h-5" />;
      case 'MESSAGING':
        return <MessageSquare className="w-5 h-5" />;
      default:
        return <Wrench className="w-5 h-5" />;
    }
  };
  
  if (fetchingTool) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8 flex items-center justify-center">
        <div className="flex flex-col items-center">
          <Loader2 className="w-12 h-12 text-blue-500 animate-spin mb-4" />
          <h2 className="text-xl font-medium text-white">Loading tool...</h2>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 p-8">
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        className="max-w-5xl mx-auto"
      >
        <div className="flex items-center mb-8">
          <button
            onClick={() => router.push('/dashboard/tools')}
            className="mr-4 p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-all text-white"
          >
            <ArrowLeft className="w-5 h-5" />
          </button>
          <h1 className="text-3xl font-bold text-white">
            {isEditing ? 'Edit Tool' : 'Create Tool'}
          </h1>
        </div>
        
        {error && (
          <div className="p-4 bg-red-500/20 border border-red-500/40 rounded-lg mb-6 flex items-center gap-2 text-white">
            <AlertCircle className="w-5 h-5 flex-shrink-0" />
            <span>{error}</span>
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <h2 className="text-xl font-medium text-white mb-4">Basic Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-white text-sm font-medium mb-1">
                  Name <span className="text-red-400">*</span>
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  placeholder="Enter tool name"
                  className={`w-full px-4 py-2 bg-white/10 border ${
                    validationErrors.name ? 'border-red-500' : 'border-white/20'
                  } rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40`}
                />
                {validationErrors.name && (
                  <p className="mt-1 text-sm text-red-400">{validationErrors.name}</p>
                )}
              </div>
              
              <div>
                <label className="block text-white text-sm font-medium mb-1">
                  Type <span className="text-red-400">*</span>
                </label>
                <select
                  name="type"
                  value={formData.type}
                  onChange={handleChange}
                  className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
                >
                  <option value="API">API</option>
                  <option value="FUNCTION">Function</option>
                  <option value="DATABASE">Database</option>
                  <option value="STORAGE">Storage</option>
                  <option value="MESSAGING">Messaging</option>
                </select>
              </div>
              
              <div className="md:col-span-2">
                <label className="block text-white text-sm font-medium mb-1">
                  Description
                </label>
                <textarea
                  name="description"
                  value={formData.description}
                  onChange={handleChange}
                  placeholder="Describe what this tool does"
                  rows={3}
                  className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40"
                />
              </div>
            </div>
          </div>
          
          {/* API Configuration */}
          {formData.type === 'API' && (
            <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
              <h2 className="text-xl font-medium text-white mb-4">API Configuration</h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-white text-sm font-medium mb-1">
                    Endpoint <span className="text-red-400">*</span>
                  </label>
                  <input
                    type="text"
                    name="configuration.endpoint"
                    value={formData.configuration.endpoint || ''}
                    onChange={handleChange}
                    placeholder="https://api.example.com/v1/resource"
                    className={`w-full px-4 py-2 bg-white/10 border ${
                      validationErrors.endpoint ? 'border-red-500' : 'border-white/20'
                    } rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40`}
                  />
                  {validationErrors.endpoint && (
                    <p className="mt-1 text-sm text-red-400">{validationErrors.endpoint}</p>
                  )}
                </div>
                
                <div>
                  <label className="block text-white text-sm font-medium mb-1">
                    Method <span className="text-red-400">*</span>
                  </label>
                  <select
                    name="configuration.method"
                    value={formData.configuration.method}
                    onChange={handleChange}
                    className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
                  >
                    <option value="GET">GET</option>
                    <option value="POST">POST</option>
                    <option value="PUT">PUT</option>
                    <option value="DELETE">DELETE</option>
                    <option value="PATCH">PATCH</option>
                  </select>
                </div>
              </div>
              
              {/* Headers */}
              <div className="mt-6">
                <div className="flex justify-between items-center mb-2">
                  <label className="text-white text-sm font-medium">Headers</label>
                  <button
                    type="button"
                    onClick={addHeader}
                    className="text-sm px-2 py-1 bg-white/10 hover:bg-white/20 rounded-md text-white flex items-center gap-1 transition-all"
                  >
                    <Plus className="w-3 h-3" />
                    Add Header
                  </button>
                </div>
                
                {Object.keys(formData.configuration.headers || {}).length === 0 ? (
                  <p className="text-sm text-gray-400 italic">No headers defined</p>
                ) : (
                  <div className="space-y-2">
                    {Object.entries(formData.configuration.headers || {}).map(([key, value], index) => (
                      <div key={index} className="flex gap-2 items-center">
                        <input
                          type="text"
                          value={key}
                          onChange={(e) => updateHeader(key, e.target.value, value)}
                          placeholder="Header name"
                          className="flex-1 px-3 py-1.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40 text-sm"
                        />
                        <input
                          type="text"
                          value={value}
                          onChange={(e) => updateHeader(key, key, e.target.value)}
                          placeholder="Header value"
                          className="flex-1 px-3 py-1.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40 text-sm"
                        />
                        <button
                          type="button"
                          onClick={() => removeHeader(key)}
                          className="p-1.5 bg-white/10 hover:bg-red-500/20 rounded-lg text-white transition-all"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Parameters */}
              <div className="mt-6">
                <div className="flex justify-between items-center mb-2">
                  <label className="text-white text-sm font-medium">Parameters</label>
                  <button
                    type="button"
                    onClick={addParameter}
                    className="text-sm px-2 py-1 bg-white/10 hover:bg-white/20 rounded-md text-white flex items-center gap-1 transition-all"
                  >
                    <Plus className="w-3 h-3" />
                    Add Parameter
                  </button>
                </div>
                
                {!formData.configuration.parameters || formData.configuration.parameters.length === 0 ? (
                  <p className="text-sm text-gray-400 italic">No parameters defined</p>
                ) : (
                  <div className="space-y-4">
                    {formData.configuration.parameters.map((param, index) => (
                      <div key={index} className="p-3 bg-white/5 rounded-lg border border-white/10">
                        <div className="flex justify-between items-center mb-2">
                          <h4 className="text-white font-medium">Parameter {index + 1}</h4>
                          <button
                            type="button"
                            onClick={() => removeParameter(index)}
                            className="p-1 bg-white/10 hover:bg-red-500/20 rounded-lg text-white transition-all"
                          >
                            <Trash2 className="w-3.5 h-3.5" />
                          </button>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                          <div>
                            <label className="block text-white text-xs mb-1">Name</label>
                            <input
                              type="text"
                              value={param.name}
                              onChange={(e) => updateParameter(index, 'name', e.target.value)}
                              placeholder="Parameter name"
                              className="w-full px-3 py-1.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40 text-sm"
                            />
                          </div>
                          
                          <div>
                            <label className="block text-white text-xs mb-1">Type</label>
                            <select
                              value={param.type}
                              onChange={(e) => updateParameter(index, 'type', e.target.value)}
                              className="w-full px-3 py-1.5 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40 text-sm"
                            >
                              <option value="string">String</option>
                              <option value="number">Number</option>
                              <option value="boolean">Boolean</option>
                              <option value="object">Object</option>
                              <option value="array">Array</option>
                            </select>
                          </div>
                          
                          <div>
                            <label className="block text-white text-xs mb-1">Description</label>
                            <input
                              type="text"
                              value={param.description || ''}
                              onChange={(e) => updateParameter(index, 'description', e.target.value)}
                              placeholder="Parameter description"
                              className="w-full px-3 py-1.5 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40 text-sm"
                            />
                          </div>
                          
                          <div className="flex items-center">
                            <label className="inline-flex items-center mt-3">
                              <input
                                type="checkbox"
                                checked={param.required}
                                onChange={(e) => updateParameter(index, 'required', e.target.checked)}
                                className="form-checkbox h-4 w-4 text-blue-500 rounded bg-white/10 border-white/20 focus:ring-0"
                              />
                              <span className="ml-2 text-white text-sm">Required</span>
                            </label>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Authentication */}
              <div className="mt-6">
                <label className="block text-white text-sm font-medium mb-1">
                  Authentication Type
                </label>
                <select
                  name="configuration.authentication.type"
                  value={formData.configuration.authentication?.type || 'NONE'}
                  onChange={handleChange}
                  className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white focus:outline-none focus:border-white/40"
                >
                  <option value="NONE">None</option>
                  <option value="BASIC">Basic Auth</option>
                  <option value="API_KEY">API Key</option>
                  <option value="OAUTH2">OAuth 2.0</option>
                  <option value="CUSTOM">Custom</option>
                </select>
              </div>
            </div>
          )}
          
          {/* Advanced Settings */}
          <div className="bg-white/10 backdrop-blur-md rounded-xl border border-white/20 p-6">
            <h2 className="text-xl font-medium text-white mb-4">Advanced Settings</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-white text-sm font-medium mb-1">
                  Timeout (ms)
                </label>
                <input
                  type="number"
                  name="configuration.timeout"
                  value={formData.configuration.timeout || 30000}
                  onChange={handleChange}
                  placeholder="30000"
                  className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40"
                />
              </div>
              
              <div>
                <label className="block text-white text-sm font-medium mb-1">
                  Max Retries
                </label>
                <input
                  type="number"
                  name="configuration.retry.maxRetries"
                  value={formData.configuration.retry?.maxRetries || 3}
                  onChange={handleChange}
                  placeholder="3"
                  min="0"
                  max="10"
                  className="w-full px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-white/40"
                />
              </div>
            </div>
          </div>
          
          {/* Actions */}
          <div className="flex justify-between">
            <button
              type="button"
              onClick={validateConfiguration}
              disabled={validating}
              className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all flex items-center gap-2 disabled:opacity-50"
            >
              {validating ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Check className="w-4 h-4" />
              )}
              Validate Configuration
            </button>
            
            <div className="flex gap-3">
              <button
                type="button"
                onClick={() => router.push('/dashboard/tools')}
                className="px-4 py-2 bg-white/10 border border-white/20 rounded-lg text-white hover:bg-white/20 transition-all"
              >
                Cancel
              </button>
              
              <button
                type="submit"
                disabled={loading}
                className="px-6 py-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg text-white transition-all hover:opacity-90 flex items-center gap-2 disabled:opacity-70"
              >
                {loading ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                {isEditing ? 'Update Tool' : 'Create Tool'}
              </button>
            </div>
          </div>
        </form>
      </motion.div>
    </div>
  );
} 