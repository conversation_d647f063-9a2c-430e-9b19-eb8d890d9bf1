import { SetMetadata } from '@nestjs/common';
import { IS_PUBLIC_KEY } from './public.decorator';

export const ROLES_KEY = 'roles';

/**
 * Roles Decorator
 * 
 * Mark routes with required roles for access control.
 * Works with RolesGuard to enforce role-based permissions.
 */
export const Roles = (...roles: string[]) => SetMetadata(ROLES_KEY, roles); 

/**
 * Public Decorator
 * 
 * Mark routes as public, bypassing authentication.
 */
export const Public = () => SetMetadata(IS_PUBLIC_KEY, true);




