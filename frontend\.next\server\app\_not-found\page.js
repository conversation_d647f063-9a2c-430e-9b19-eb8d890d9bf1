/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/_not-found/page";
exports.ids = ["app/_not-found/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2F..%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ccursorpro%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ccursorpro%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2F..%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ccursorpro%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ccursorpro%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?42d4\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n          children: [\"/_not-found\", {\n            children: ['__PAGE__', {}, {\n              page: [\n                () => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/not-found-error.js\", 23)),\n                \"next/dist/client/components/not-found-error\"\n              ]\n            }]\n          }, {}]\n        },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/_not-found/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/_not-found/page\",\n        pathname: \"/_not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2F..%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ccursorpro%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ccursorpro%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/lib/providers.tsx */ \"(ssr)/./src/lib/providers.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4zMF9AYmFiZWwrY29yZUA3Ll84NTk4NjBkYmFhMzU5MjgwOGI5YzFhMjg1YjU0MjNhOS9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDbWF4JTVDJTVDY3Vyc29ycHJvJTVDJTVDZnJvbnRlbmQlNUMlNUNzcmMlNUMlNUNsaWIlNUMlNUNwcm92aWRlcnMudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyUHJvdmlkZXJzJTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNsYXJhZ29uJTVDJTVDd3d3JTVDJTVDbWF4JTVDJTVDY3Vyc29ycHJvJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTQuMi4zMF8lNDBiYWJlbCUyQmNvcmUlNDA3Ll84NTk4NjBkYmFhMzU5MjgwOGI5YzFhMjg1YjU0MjNhOSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJzcmMlNUMlNUMlNUMlNUNhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDbGFyYWdvbiU1QyU1Q3d3dyU1QyU1Q21heCU1QyU1Q2N1cnNvcnBybyU1QyU1Q2Zyb250ZW5kJTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDBKQUF1SSIsInNvdXJjZXMiOlsid2VicGFjazovL0BzeW5hcHNlYWkvZnJvbnRlbmQvP2MwNTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJQcm92aWRlcnNcIl0gKi8gXCJDOlxcXFxsYXJhZ29uXFxcXHd3d1xcXFxtYXhcXFxcY3Vyc29ycHJvXFxcXGZyb250ZW5kXFxcXHNyY1xcXFxsaWJcXFxccHJvdmlkZXJzLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cfrontend%5C%5Csrc%5C%5Clib%5C%5Cproviders.tsx%22%2C%22ids%22%3A%5B%22Providers%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22src%5C%5C%5C%5Capp%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cfrontend%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5Claragon%5C%5Cwww%5C%5Cmax%5C%5Ccursorpro%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/components/error-fallback.tsx":
/*!*******************************************!*\
  !*** ./src/components/error-fallback.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorFallback: () => (/* binding */ ErrorFallback)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCcw_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCcw!=!lucide-react */ \"(ssr)/../node_modules/.pnpm/lucide-react@0.292.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCcw_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCcw!=!lucide-react */ \"(ssr)/../node_modules/.pnpm/lucide-react@0.292.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/refresh-ccw.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_Home_RefreshCcw_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,Home,RefreshCcw!=!lucide-react */ \"(ssr)/../node_modules/.pnpm/lucide-react@0.292.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* __next_internal_client_entry_do_not_use__ ErrorFallback auto */ \n\n\nfunction ErrorFallback({ error, resetErrorBoundary }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 px-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCcw_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        className: \"h-12 w-12 text-error-500\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n                    lineNumber: 15,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2\",\n                    children: \"Something went wrong\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n                    lineNumber: 19,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-gray-600 dark:text-gray-400 mb-6\",\n                    children: \"We apologize for the inconvenience. An unexpected error has occurred.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 9\n                }, this),\n                 true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"details\", {\n                    className: \"mb-6 text-left\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"summary\", {\n                            className: \"cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\",\n                            children: \"Error Details\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                            className: \"text-xs bg-gray-100 dark:bg-gray-700 p-3 rounded border overflow-x-auto\",\n                            children: [\n                                error.message,\n                                error.stack && \"\\n\\n\" + error.stack\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n                    lineNumber: 28,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col sm:flex-row gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetErrorBoundary,\n                            className: \"btn-primary flex items-center justify-center gap-2 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCcw_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n                                    lineNumber: 44,\n                                    columnNumber: 13\n                                }, this),\n                                \"Try Again\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = \"/\",\n                            className: \"btn-secondary flex items-center justify-center gap-2 flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_Home_RefreshCcw_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n                                    lineNumber: 52,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go Home\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n                            lineNumber: 48,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\components\\\\error-fallback.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/error-fallback.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/providers.tsx":
/*!*******************************!*\
  !*** ./src/lib/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Providers: () => (/* binding */ Providers)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/.pnpm/@tanstack+query-core@5.83.0/node_modules/@tanstack/query-core/build/modern/queryClient.js\");\n/* harmony import */ var _tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/react-query */ \"(ssr)/../node_modules/.pnpm/@tanstack+react-query@5.83.0_react@18.3.1/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @tanstack/react-query-devtools */ \"(ssr)/../node_modules/.pnpm/@tanstack+react-query-devto_0fcbf818c31175028da9946478d04a4e/node_modules/@tanstack/react-query-devtools/build/modern/index.js\");\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/../node_modules/.pnpm/next-themes@0.2.1_next@14.2_a696b11a4ab7cf8b0bdc787b5fe2c9ce/node_modules/next-themes/dist/index.module.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/../node_modules/.pnpm/next-auth@4.24.11_next@14.2_a1ef9d8a702641c3a4a31ef028c064a2/node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var react_error_boundary__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-error-boundary */ \"(ssr)/../node_modules/.pnpm/react-error-boundary@4.1.2_react@18.3.1/node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js\");\n/* harmony import */ var _components_error_fallback__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/error-fallback */ \"(ssr)/./src/components/error-fallback.tsx\");\n/* harmony import */ var react_hot_toast__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! react-hot-toast */ \"(ssr)/../node_modules/.pnpm/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d/node_modules/react-hot-toast/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Providers auto */ \n\n\n\n\n\n\n\n\n// Create a client\nconst queryClient = new _tanstack_react_query__WEBPACK_IMPORTED_MODULE_6__.QueryClient({\n    defaultOptions: {\n        queries: {\n            staleTime: 1000 * 60 * 5,\n            gcTime: 1000 * 60 * 10,\n            retry: (failureCount, error)=>{\n                // Don't retry on 4xx errors except 429 (rate limit)\n                const err = error;\n                if (err?.status && err.status >= 400 && err.status < 500 && err.status !== 429) {\n                    return false;\n                }\n                return failureCount < 3;\n            },\n            refetchOnWindowFocus: false,\n            refetchOnReconnect: true\n        },\n        mutations: {\n            retry: (failureCount, error)=>{\n                // Don't retry mutations on client errors\n                const err = error;\n                if (err?.status && err.status >= 400 && err.status < 500) {\n                    return false;\n                }\n                return failureCount < 2;\n            }\n        }\n    }\n});\nfunction Providers({ children, session }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_error_boundary__WEBPACK_IMPORTED_MODULE_7__.ErrorBoundary, {\n        FallbackComponent: _components_error_fallback__WEBPACK_IMPORTED_MODULE_4__.ErrorFallback,\n        onError: (error, errorInfo)=>{\n            // Log error to monitoring service\n            console.error(\"Application Error:\", error, errorInfo);\n            // Report to error tracking service (Sentry, etc.)\n            if (false) {}\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_3__.SessionProvider, {\n            session: session,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query__WEBPACK_IMPORTED_MODULE_8__.QueryClientProvider, {\n                client: queryClient,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    attribute: \"class\",\n                    defaultTheme: \"system\",\n                    enableSystem: true,\n                    disableTransitionOnChange: false,\n                    themes: [\n                        \"light\",\n                        \"dark\"\n                    ],\n                    storageKey: \"synapseai-theme\",\n                    children: [\n                        children,\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_hot_toast__WEBPACK_IMPORTED_MODULE_5__.Toaster, {\n                            position: \"top-right\",\n                            toastOptions: {\n                                duration: 3000\n                            }\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\lib\\\\providers.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 13\n                        }, this),\n                         true && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_tanstack_react_query_devtools__WEBPACK_IMPORTED_MODULE_9__.ReactQueryDevtools, {\n                            initialIsOpen: false,\n                            buttonPosition: \"bottom-right\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\lib\\\\providers.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\lib\\\\providers.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\lib\\\\providers.tsx\",\n                lineNumber: 62,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\lib\\\\providers.tsx\",\n            lineNumber: 61,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\lib\\\\providers.tsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9zcmMvbGliL3Byb3ZpZGVycy50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRTBCO0FBQytDO0FBQ0w7QUFDeEI7QUFDTTtBQUNHO0FBQ087QUFDbEI7QUFFMUMsa0JBQWtCO0FBQ2xCLE1BQU1TLGNBQWMsSUFBSVIsOERBQVdBLENBQUM7SUFDbENTLGdCQUFnQjtRQUNkQyxTQUFTO1lBQ1BDLFdBQVcsT0FBTyxLQUFLO1lBQ3ZCQyxRQUFRLE9BQU8sS0FBSztZQUNwQkMsT0FBTyxDQUFDQyxjQUFjQztnQkFDcEIsb0RBQW9EO2dCQUNwRCxNQUFNQyxNQUFNRDtnQkFDWixJQUFJQyxLQUFLQyxVQUFVRCxJQUFJQyxNQUFNLElBQUksT0FBT0QsSUFBSUMsTUFBTSxHQUFHLE9BQU9ELElBQUlDLE1BQU0sS0FBSyxLQUFLO29CQUM5RSxPQUFPO2dCQUNUO2dCQUNBLE9BQU9ILGVBQWU7WUFDeEI7WUFDQUksc0JBQXNCO1lBQ3RCQyxvQkFBb0I7UUFDdEI7UUFDQUMsV0FBVztZQUNUUCxPQUFPLENBQUNDLGNBQWNDO2dCQUNwQix5Q0FBeUM7Z0JBQ3pDLE1BQU1DLE1BQU1EO2dCQUNaLElBQUlDLEtBQUtDLFVBQVVELElBQUlDLE1BQU0sSUFBSSxPQUFPRCxJQUFJQyxNQUFNLEdBQUcsS0FBSztvQkFDeEQsT0FBTztnQkFDVDtnQkFDQSxPQUFPSCxlQUFlO1lBQ3hCO1FBQ0Y7SUFDRjtBQUNGO0FBT08sU0FBU08sVUFBVSxFQUFFQyxRQUFRLEVBQUVDLE9BQU8sRUFBa0I7SUFDN0QscUJBQ0UsOERBQUNsQiwrREFBYUE7UUFDWm1CLG1CQUFtQmxCLHFFQUFhQTtRQUNoQ21CLFNBQVMsQ0FBQ1YsT0FBT1c7WUFDZixrQ0FBa0M7WUFDbENDLFFBQVFaLEtBQUssQ0FBQyxzQkFBc0JBLE9BQU9XO1lBRTNDLGtEQUFrRDtZQUNsRCxJQUFJLEtBQTBELEVBQWMsRUFFM0U7UUFDSDtrQkFFQSw0RUFBQ3RCLDREQUFlQTtZQUFDbUIsU0FBU0E7c0JBQ3hCLDRFQUFDdEIsc0VBQW1CQTtnQkFBQzRCLFFBQVFyQjswQkFDM0IsNEVBQUNMLHNEQUFhQTtvQkFDWjJCLFdBQVU7b0JBQ1ZDLGNBQWE7b0JBQ2JDLFlBQVk7b0JBQ1pDLDJCQUEyQjtvQkFDM0JDLFFBQVE7d0JBQUM7d0JBQVM7cUJBQU87b0JBQ3pCQyxZQUFXOzt3QkFFVmI7c0NBR0QsOERBQUNmLG9EQUFPQTs0QkFDTjZCLFVBQVM7NEJBQ1RDLGNBQWM7Z0NBQ1pDLFVBQVU7NEJBQ1o7Ozs7Ozt3QkE3RWQsS0FpRnNDLGtCQUN4Qiw4REFBQ3BDLDhFQUFrQkE7NEJBQ2pCcUMsZUFBZTs0QkFDZkMsZ0JBQWU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVEvQiIsInNvdXJjZXMiOlsid2VicGFjazovL0BzeW5hcHNlYWkvZnJvbnRlbmQvLi9zcmMvbGliL3Byb3ZpZGVycy50c3g/ODRjMiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XHJcblxyXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xyXG5pbXBvcnQgeyBRdWVyeUNsaWVudCwgUXVlcnlDbGllbnRQcm92aWRlciB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeSc7XHJcbmltcG9ydCB7IFJlYWN0UXVlcnlEZXZ0b29scyB9IGZyb20gJ0B0YW5zdGFjay9yZWFjdC1xdWVyeS1kZXZ0b29scyc7XHJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgfSBmcm9tICduZXh0LXRoZW1lcyc7XHJcbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XHJcbmltcG9ydCB7IEVycm9yQm91bmRhcnkgfSBmcm9tICdyZWFjdC1lcnJvci1ib3VuZGFyeSc7XHJcbmltcG9ydCB7IEVycm9yRmFsbGJhY2sgfSBmcm9tICdAL2NvbXBvbmVudHMvZXJyb3ItZmFsbGJhY2snO1xyXG5pbXBvcnQgeyBUb2FzdGVyIH0gZnJvbSAncmVhY3QtaG90LXRvYXN0JztcclxuXHJcbi8vIENyZWF0ZSBhIGNsaWVudFxyXG5jb25zdCBxdWVyeUNsaWVudCA9IG5ldyBRdWVyeUNsaWVudCh7XHJcbiAgZGVmYXVsdE9wdGlvbnM6IHtcclxuICAgIHF1ZXJpZXM6IHtcclxuICAgICAgc3RhbGVUaW1lOiAxMDAwICogNjAgKiA1LCAvLyA1IG1pbnV0ZXNcclxuICAgICAgZ2NUaW1lOiAxMDAwICogNjAgKiAxMCwgLy8gMTAgbWludXRlc1xyXG4gICAgICByZXRyeTogKGZhaWx1cmVDb3VudCwgZXJyb3I6IHVua25vd24pID0+IHtcclxuICAgICAgICAvLyBEb24ndCByZXRyeSBvbiA0eHggZXJyb3JzIGV4Y2VwdCA0MjkgKHJhdGUgbGltaXQpXHJcbiAgICAgICAgY29uc3QgZXJyID0gZXJyb3IgYXMgeyBzdGF0dXM/OiBudW1iZXIgfTtcclxuICAgICAgICBpZiAoZXJyPy5zdGF0dXMgJiYgZXJyLnN0YXR1cyA+PSA0MDAgJiYgZXJyLnN0YXR1cyA8IDUwMCAmJiBlcnIuc3RhdHVzICE9PSA0MjkpIHtcclxuICAgICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIGZhaWx1cmVDb3VudCA8IDM7XHJcbiAgICAgIH0sXHJcbiAgICAgIHJlZmV0Y2hPbldpbmRvd0ZvY3VzOiBmYWxzZSxcclxuICAgICAgcmVmZXRjaE9uUmVjb25uZWN0OiB0cnVlLFxyXG4gICAgfSxcclxuICAgIG11dGF0aW9uczoge1xyXG4gICAgICByZXRyeTogKGZhaWx1cmVDb3VudCwgZXJyb3I6IHVua25vd24pID0+IHtcclxuICAgICAgICAvLyBEb24ndCByZXRyeSBtdXRhdGlvbnMgb24gY2xpZW50IGVycm9yc1xyXG4gICAgICAgIGNvbnN0IGVyciA9IGVycm9yIGFzIHsgc3RhdHVzPzogbnVtYmVyIH07XHJcbiAgICAgICAgaWYgKGVycj8uc3RhdHVzICYmIGVyci5zdGF0dXMgPj0gNDAwICYmIGVyci5zdGF0dXMgPCA1MDApIHtcclxuICAgICAgICAgIHJldHVybiBmYWxzZTtcclxuICAgICAgICB9XHJcbiAgICAgICAgcmV0dXJuIGZhaWx1cmVDb3VudCA8IDI7XHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gIH0sXHJcbn0pO1xyXG5cclxuaW50ZXJmYWNlIFByb3ZpZGVyc1Byb3BzIHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG4gIHNlc3Npb24/OiB1bmtub3duO1xyXG59XHJcblxyXG5leHBvcnQgZnVuY3Rpb24gUHJvdmlkZXJzKHsgY2hpbGRyZW4sIHNlc3Npb24gfTogUHJvdmlkZXJzUHJvcHMpIHtcclxuICByZXR1cm4gKFxyXG4gICAgPEVycm9yQm91bmRhcnlcclxuICAgICAgRmFsbGJhY2tDb21wb25lbnQ9e0Vycm9yRmFsbGJhY2t9XHJcbiAgICAgIG9uRXJyb3I9eyhlcnJvciwgZXJyb3JJbmZvKSA9PiB7XHJcbiAgICAgICAgLy8gTG9nIGVycm9yIHRvIG1vbml0b3Jpbmcgc2VydmljZVxyXG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0FwcGxpY2F0aW9uIEVycm9yOicsIGVycm9yLCBlcnJvckluZm8pO1xyXG4gICAgICAgIFxyXG4gICAgICAgIC8vIFJlcG9ydCB0byBlcnJvciB0cmFja2luZyBzZXJ2aWNlIChTZW50cnksIGV0Yy4pXHJcbiAgICAgICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcclxuICAgICAgICAgIC8vIHdpbmRvdy5TZW50cnk/LmNhcHR1cmVFeGNlcHRpb24oZXJyb3IpO1xyXG4gICAgICAgIH1cclxuICAgICAgfX1cclxuICAgID5cclxuICAgICAgPFNlc3Npb25Qcm92aWRlciBzZXNzaW9uPXtzZXNzaW9uIGFzIGFueX0+XHJcbiAgICAgICAgPFF1ZXJ5Q2xpZW50UHJvdmlkZXIgY2xpZW50PXtxdWVyeUNsaWVudH0+XHJcbiAgICAgICAgICA8VGhlbWVQcm92aWRlclxyXG4gICAgICAgICAgICBhdHRyaWJ1dGU9XCJjbGFzc1wiXHJcbiAgICAgICAgICAgIGRlZmF1bHRUaGVtZT1cInN5c3RlbVwiXHJcbiAgICAgICAgICAgIGVuYWJsZVN5c3RlbVxyXG4gICAgICAgICAgICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlPXtmYWxzZX1cclxuICAgICAgICAgICAgdGhlbWVzPXtbJ2xpZ2h0JywgJ2RhcmsnXX1cclxuICAgICAgICAgICAgc3RvcmFnZUtleT1cInN5bmFwc2VhaS10aGVtZVwiXHJcbiAgICAgICAgICA+XHJcbiAgICAgICAgICAgIHtjaGlsZHJlbn1cclxuICAgICAgICAgICAgXHJcbiAgICAgICAgICAgIHsvKiBUb2FzdCBOb3RpZmljYXRpb25zICovfVxyXG4gICAgICAgICAgICA8VG9hc3RlclxyXG4gICAgICAgICAgICAgIHBvc2l0aW9uPVwidG9wLXJpZ2h0XCJcclxuICAgICAgICAgICAgICB0b2FzdE9wdGlvbnM9e3tcclxuICAgICAgICAgICAgICAgIGR1cmF0aW9uOiAzMDAwLFxyXG4gICAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgIFxyXG4gICAgICAgICAgICB7LyogRGV2ZWxvcG1lbnQgdG9vbHMgKi99XHJcbiAgICAgICAgICAgIHtwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ2RldmVsb3BtZW50JyAmJiAoXHJcbiAgICAgICAgICAgICAgPFJlYWN0UXVlcnlEZXZ0b29sc1xyXG4gICAgICAgICAgICAgICAgaW5pdGlhbElzT3Blbj17ZmFsc2V9XHJcbiAgICAgICAgICAgICAgICBidXR0b25Qb3NpdGlvbj1cImJvdHRvbS1yaWdodFwiXHJcbiAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgKX1cclxuICAgICAgICAgIDwvVGhlbWVQcm92aWRlcj5cclxuICAgICAgICA8L1F1ZXJ5Q2xpZW50UHJvdmlkZXI+XHJcbiAgICAgIDwvU2Vzc2lvblByb3ZpZGVyPlxyXG4gICAgPC9FcnJvckJvdW5kYXJ5PlxyXG4gICk7XHJcbn0gIl0sIm5hbWVzIjpbIlJlYWN0IiwiUXVlcnlDbGllbnQiLCJRdWVyeUNsaWVudFByb3ZpZGVyIiwiUmVhY3RRdWVyeURldnRvb2xzIiwiVGhlbWVQcm92aWRlciIsIlNlc3Npb25Qcm92aWRlciIsIkVycm9yQm91bmRhcnkiLCJFcnJvckZhbGxiYWNrIiwiVG9hc3RlciIsInF1ZXJ5Q2xpZW50IiwiZGVmYXVsdE9wdGlvbnMiLCJxdWVyaWVzIiwic3RhbGVUaW1lIiwiZ2NUaW1lIiwicmV0cnkiLCJmYWlsdXJlQ291bnQiLCJlcnJvciIsImVyciIsInN0YXR1cyIsInJlZmV0Y2hPbldpbmRvd0ZvY3VzIiwicmVmZXRjaE9uUmVjb25uZWN0IiwibXV0YXRpb25zIiwiUHJvdmlkZXJzIiwiY2hpbGRyZW4iLCJzZXNzaW9uIiwiRmFsbGJhY2tDb21wb25lbnQiLCJvbkVycm9yIiwiZXJyb3JJbmZvIiwiY29uc29sZSIsInByb2Nlc3MiLCJjbGllbnQiLCJhdHRyaWJ1dGUiLCJkZWZhdWx0VGhlbWUiLCJlbmFibGVTeXN0ZW0iLCJkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlIiwidGhlbWVzIiwic3RvcmFnZUtleSIsInBvc2l0aW9uIiwidG9hc3RPcHRpb25zIiwiZHVyYXRpb24iLCJpbml0aWFsSXNPcGVuIiwiYnV0dG9uUG9zaXRpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/providers.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"60117574a372\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHN5bmFwc2VhaS9mcm9udGVuZC8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/MjZmYiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjYwMTE3NTc0YTM3MlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata),\n/* harmony export */   viewport: () => (/* binding */ viewport)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _lib_providers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/providers */ \"(rsc)/./src/lib/providers.tsx\");\n\n\n\n\nconst metadata = {\n    title: {\n        default: \"SynapseAI - AI Agent Platform\",\n        template: \"%s | SynapseAI\"\n    },\n    description: \"Create AI agents, build tools, manage hybrid workflows, and more with SynapseAI platform.\",\n    keywords: [\n        \"AI\",\n        \"Artificial Intelligence\",\n        \"Agent\",\n        \"Tool\",\n        \"Hybrid\",\n        \"Workflow\",\n        \"Platform\",\n        \"SaaS\"\n    ],\n    authors: [\n        {\n            name: \"SynapseAI Team\",\n            url: \"https://synapseai.com\"\n        }\n    ],\n    creator: \"SynapseAI\",\n    publisher: \"SynapseAI\",\n    formatDetection: {\n        email: false,\n        address: false,\n        telephone: false\n    },\n    metadataBase: new URL(\"https://synapseai.com\"),\n    alternates: {\n        canonical: \"/\",\n        languages: {\n            \"en-US\": \"/en-US\"\n        }\n    },\n    openGraph: {\n        title: \"SynapseAI - AI Agent Platform\",\n        description: \"Create AI agents, build tools, manage hybrid workflows, and more with SynapseAI platform.\",\n        url: \"https://synapseai.com\",\n        siteName: \"SynapseAI\",\n        images: [\n            {\n                url: \"/og-image.jpg\",\n                width: 1200,\n                height: 630,\n                alt: \"SynapseAI - AI Agent Platform\"\n            }\n        ],\n        locale: \"en_US\",\n        type: \"website\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"SynapseAI - AI Agent Platform\",\n        description: \"Create AI agents, build tools, manage hybrid workflows, and more with SynapseAI platform.\",\n        creator: \"@synapseai\",\n        images: [\n            \"/twitter-image.jpg\"\n        ]\n    },\n    robots: {\n        index: true,\n        follow: true,\n        googleBot: {\n            index: true,\n            follow: true,\n            \"max-video-preview\": -1,\n            \"max-image-preview\": \"large\",\n            \"max-snippet\": -1\n        }\n    },\n    verification: {\n        google: process.env.NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION || \"\"\n    },\n    category: \"technology\"\n};\nconst viewport = {\n    themeColor: [\n        {\n            media: \"(prefers-color-scheme: light)\",\n            color: \"#ffffff\"\n        },\n        {\n            media: \"(prefers-color-scheme: dark)\",\n            color: \"#0f172a\"\n        }\n    ],\n    width: \"device-width\",\n    initialScale: 1\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_providers__WEBPACK_IMPORTED_MODULE_2__.Providers, {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 101,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\laragon\\\\www\\\\max\\\\cursorpro\\\\frontend\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 99,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/lib/providers.tsx":
/*!*******************************!*\
  !*** ./src/lib/providers.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Providers: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\laragon\www\max\cursorpro\frontend\src\lib\providers.tsx#Providers`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/@tanstack+query-devtools@5.81.2","vendor-chunks/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9","vendor-chunks/@tanstack+query-core@5.83.0","vendor-chunks/next-auth@4.24.11_next@14.2_a1ef9d8a702641c3a4a31ef028c064a2","vendor-chunks/@babel+runtime@7.27.6","vendor-chunks/react-hot-toast@2.5.2_react_9bc054aa3de8cae57bd3b78a8a871a4d","vendor-chunks/lucide-react@0.292.0_react@18.3.1","vendor-chunks/react-error-boundary@4.1.2_react@18.3.1","vendor-chunks/@tanstack+react-query-devto_0fcbf818c31175028da9946478d04a4e","vendor-chunks/next-themes@0.2.1_next@14.2_a696b11a4ab7cf8b0bdc787b5fe2c9ce","vendor-chunks/goober@2.1.16_csstype@3.1.3","vendor-chunks/@swc+helpers@0.5.5","vendor-chunks/@tanstack+react-query@5.83.0_react@18.3.1"], () => (__webpack_exec__("(rsc)/../node_modules/.pnpm/next@14.2.30_@babel+core@7._859860dbaa3592808b9c1a285b5423a9/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2F_not-found%2Fpage&page=%2F_not-found%2Fpage&appPaths=&pagePath=..%2F..%2Fnode_modules%2F.pnpm%2Fnext%4014.2.30_%40babel%2Bcore%407._859860dbaa3592808b9c1a285b5423a9%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=C%3A%5Claragon%5Cwww%5Cmax%5Ccursorpro%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Claragon%5Cwww%5Cmax%5Ccursorpro%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();