/**
 * Configuration settings for the SynapseAI platform frontend
 */

/**
 * Get the backend API URL
 */
export function getBackendUrl(): string {
  // Use environment variable if available, otherwise default
  return process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:3001';
}

/**
 * Get the frontend URL
 */
export function getFrontendUrl(): string {
  // Use environment variable if available, otherwise default
  return process.env.NEXT_PUBLIC_FRONTEND_URL || 'http://localhost:3000';
}

/**
 * Application settings
 */
export const appConfig = {
  name: 'SynapseAI',
  description: 'AI Agent Platform',
  version: '1.0.0',
  apiVersion: 'v1',
  maxUploadSize: 50 * 1024 * 1024, // 50MB
  supportEmail: '<EMAIL>',
  defaultPageSize: 10,
};

/**
 * Feature flags
 */
export const featureFlags = {
  enableAgentMarketplace: true,
  enableToolMarketplace: true,
  enableHybridWorkflows: true,
  enableKnowledgeBase: true,
  enableWidgets: true,
  enableAnalytics: true,
  enableSandbox: true,
}; 